{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\index.vue", "mtime": 1731833096000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovMjAyNVx1NTkyN1x1NTIxQl9cdTU3MzBcdTRFMEJcdTc1MzBcdTVFODQvdnVlLWVsZW1lbnQtYWRtaW43LjAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBhZG1pbkRhc2hib2FyZCBmcm9tICcuL2FkbWluJzsKaW1wb3J0IGVkaXRvckRhc2hib2FyZCBmcm9tICcuL2VkaXRvcic7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGFzaGJvYXJkJywKICBjb21wb25lbnRzOiB7CiAgICBhZG1pbkRhc2hib2FyZDogYWRtaW5EYXNoYm9hcmQsCiAgICBlZGl0b3JEYXNoYm9hcmQ6IGVkaXRvckRhc2hib2FyZAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRSb2xlOiAnYWRtaW5EYXNoYm9hcmQnCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoe30sIG1hcEdldHRlcnMoWydyb2xlcyddKSksCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIC8vIGlmICghdGhpcy5yb2xlcy5pbmNsdWRlcygnYWRtaW4nKSkgewogICAgLy8gICB0aGlzLmN1cnJlbnRSb2xlID0gJ2VkaXRvckRhc2hib2FyZCcKICAgIC8vIH0KICB9Cn07"}, {"version": 3, "names": ["mapGetters", "adminDashboard", "editorDashboard", "name", "components", "data", "currentRole", "computed", "_objectSpread", "created"], "sources": ["src/views/dashboard/index.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <component :is=\"currentRole\" />\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport adminDashboard from './admin'\nimport editorDashboard from './editor'\n\nexport default {\n  name: 'Dashboard',\n  components: { adminDashboard, editorDashboard },\n  data() {\n    return {\n      currentRole: 'adminDashboard'\n    }\n  },\n  computed: {\n    ...mapGetters([\n      'roles'\n    ])\n  },\n  created() {\n    // if (!this.roles.includes('admin')) {\n    //   this.currentRole = 'editorDashboard'\n    // }\n  }\n}\n</script>\n"], "mappings": ";AAOA,SAAAA,UAAA;AACA,OAAAC,cAAA;AACA,OAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH,cAAA,EAAAA,cAAA;IAAAC,eAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,KACAR,UAAA,EACA,QACA,EACA;EACAS,OAAA,WAAAA,QAAA;IACA;IACA;IACA;EAAA;AAEA", "ignoreList": []}]}