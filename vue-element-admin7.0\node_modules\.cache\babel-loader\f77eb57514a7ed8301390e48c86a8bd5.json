{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\@babel\\runtime\\helpers\\esm\\arrayWithHoles.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\@babel\\runtime\\helpers\\esm\\arrayWithHoles.js", "mtime": 1749148890534}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZnVuY3Rpb24gX2FycmF5V2l0aEhvbGVzKHIpIHsKICBpZiAoQXJyYXkuaXNBcnJheShyKSkgcmV0dXJuIHI7Cn0KZXhwb3J0IHsgX2FycmF5V2l0aEhvbGVzIGFzIGRlZmF1bHQgfTs="}, {"version": 3, "names": ["_arrayWithHoles", "r", "Array", "isArray", "default"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js"], "sourcesContent": ["function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAE;EAC1B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AACA,SAASD,eAAe,IAAII,OAAO", "ignoreList": []}]}