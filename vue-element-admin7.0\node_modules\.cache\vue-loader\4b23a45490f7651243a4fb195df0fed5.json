{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\index.vue?vue&type=style&index=0&id=13877386&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CkBpbXBvcnQgIn5AL3N0eWxlcy9taXhpbi5zY3NzIjsKQGltcG9ydCAifkAvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzIjsKCi5hcHAtd3JhcHBlciB7CiAgQGluY2x1ZGUgY2xlYXJmaXg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGhlaWdodDogMTAwJTsKICB3aWR0aDogMTAwJTsKCiAgJi5tb2JpbGUub3BlblNpZGViYXIgewogICAgcG9zaXRpb246IGZpeGVkOwogICAgdG9wOiAwOwogIH0KfQoKLmRyYXdlci1iZyB7CiAgYmFja2dyb3VuZDogIzAwMDsKICBvcGFjaXR5OiAwLjM7CiAgd2lkdGg6IDEwMCU7CiAgdG9wOiAwOwogIGhlaWdodDogMTAwJTsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgei1pbmRleDogOTk5Owp9CgouZml4ZWQtaGVhZGVyIHsKICBwb3NpdGlvbjogZml4ZWQ7CiAgdG9wOiAwOwogIHJpZ2h0OiAwOwogIHotaW5kZXg6IDk7CiAgd2lkdGg6IGNhbGMoMTAwJSAtICN7JHNpZGVCYXJXaWR0aH0pOwogIHRyYW5zaXRpb246IHdpZHRoIDAuMjhzOwp9CgouaGlkZVNpZGViYXIgLmZpeGVkLWhlYWRlciB7CiAgd2lkdGg6IGNhbGMoMTAwJSAtIDU0cHgpCn0KCi5tb2JpbGUgLmZpeGVkLWhlYWRlciB7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuGA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\n    <sidebar class=\"sidebar-container\" />\n    <div :class=\"{hasTagsView:needTagsView}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar />\n        <tags-view v-if=\"needTagsView\" />\n      </div>\n      <app-main />\n      <right-panel v-if=\"showSettings\">\n        <settings />\n      </right-panel>\n    </div>\n  </div>\n</template>\n\n<script>\nimport RightPanel from '@/components/RightPanel'\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    <PERSON>Panel,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      showSettings: state => state.settings.showSettings,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/styles/mixin.scss\";\n  @import \"~@/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$sideBarWidth});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px)\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"]}]}