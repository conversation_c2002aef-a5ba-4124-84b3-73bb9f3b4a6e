{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\TagsView\\index.vue?vue&type=template&id=fac8ca64&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749148885234}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "id", "ref", "on", "scroll", "handleScroll", "_l", "visitedViews", "tag", "key", "path", "refInFor", "class", "isActive", "to", "query", "fullPath", "nativeOn", "mouseup", "$event", "button", "isAffix", "closeSelectedTag", "contextmenu", "preventDefault", "openMenu", "_v", "_s", "title", "click", "stopPropagation", "_e", "directives", "name", "rawName", "value", "visible", "expression", "style", "left", "top", "refreshSelectedTag", "selectedTag", "closeOthersTags", "closeAllTags", "staticRenderFns", "_withStripped"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/layout/components/TagsView/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"tags-view-container\",\n      attrs: { id: \"tags-view-container\" },\n    },\n    [\n      _c(\n        \"scroll-pane\",\n        {\n          ref: \"scrollPane\",\n          staticClass: \"tags-view-wrapper\",\n          on: { scroll: _vm.handleScroll },\n        },\n        _vm._l(_vm.visitedViews, function (tag) {\n          return _c(\n            \"router-link\",\n            {\n              key: tag.path,\n              ref: \"tag\",\n              refInFor: true,\n              staticClass: \"tags-view-item\",\n              class: _vm.isActive(tag) ? \"active\" : \"\",\n              attrs: {\n                to: {\n                  path: tag.path,\n                  query: tag.query,\n                  fullPath: tag.fullPath,\n                },\n                tag: \"span\",\n              },\n              nativeOn: {\n                mouseup: function ($event) {\n                  if (\"button\" in $event && $event.button !== 1) return null\n                  !_vm.isAffix(tag) ? _vm.closeSelectedTag(tag) : \"\"\n                },\n                contextmenu: function ($event) {\n                  $event.preventDefault()\n                  return _vm.openMenu(tag, $event)\n                },\n              },\n            },\n            [\n              _vm._v(\" \" + _vm._s(tag.title) + \" \"),\n              !_vm.isAffix(tag)\n                ? _c(\"span\", {\n                    staticClass: \"el-icon-close\",\n                    on: {\n                      click: function ($event) {\n                        $event.preventDefault()\n                        $event.stopPropagation()\n                        return _vm.closeSelectedTag(tag)\n                      },\n                    },\n                  })\n                : _vm._e(),\n            ]\n          )\n        }),\n        1\n      ),\n      _c(\n        \"ul\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.visible,\n              expression: \"visible\",\n            },\n          ],\n          staticClass: \"contextmenu\",\n          style: { left: _vm.left + \"px\", top: _vm.top + \"px\" },\n        },\n        [\n          _c(\n            \"li\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.refreshSelectedTag(_vm.selectedTag)\n                },\n              },\n            },\n            [_vm._v(\"Refresh\")]\n          ),\n          !_vm.isAffix(_vm.selectedTag)\n            ? _c(\n                \"li\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.closeSelectedTag(_vm.selectedTag)\n                    },\n                  },\n                },\n                [_vm._v(\"Close\")]\n              )\n            : _vm._e(),\n          _c(\"li\", { on: { click: _vm.closeOthersTags } }, [\n            _vm._v(\"Close Others\"),\n          ]),\n          _c(\n            \"li\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.closeAllTags(_vm.selectedTag)\n                },\n              },\n            },\n            [_vm._v(\"Close All\")]\n          ),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,qBAAqB;IAClCC,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAsB;EACrC,CAAC,EACD,CACEJ,EAAE,CACA,aAAa,EACb;IACEK,GAAG,EAAE,YAAY;IACjBH,WAAW,EAAE,mBAAmB;IAChCI,EAAE,EAAE;MAAEC,MAAM,EAAER,GAAG,CAACS;IAAa;EACjC,CAAC,EACDT,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,YAAY,EAAE,UAAUC,GAAG,EAAE;IACtC,OAAOX,EAAE,CACP,aAAa,EACb;MACEY,GAAG,EAAED,GAAG,CAACE,IAAI;MACbR,GAAG,EAAE,KAAK;MACVS,QAAQ,EAAE,IAAI;MACdZ,WAAW,EAAE,gBAAgB;MAC7Ba,KAAK,EAAEhB,GAAG,CAACiB,QAAQ,CAACL,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE;MACxCR,KAAK,EAAE;QACLc,EAAE,EAAE;UACFJ,IAAI,EAAEF,GAAG,CAACE,IAAI;UACdK,KAAK,EAAEP,GAAG,CAACO,KAAK;UAChBC,QAAQ,EAAER,GAAG,CAACQ;QAChB,CAAC;QACDR,GAAG,EAAE;MACP,CAAC;MACDS,QAAQ,EAAE;QACRC,OAAO,EAAE,SAATA,OAAOA,CAAYC,MAAM,EAAE;UACzB,IAAI,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;UAC1D,CAACxB,GAAG,CAACyB,OAAO,CAACb,GAAG,CAAC,GAAGZ,GAAG,CAAC0B,gBAAgB,CAACd,GAAG,CAAC,GAAG,EAAE;QACpD,CAAC;QACDe,WAAW,EAAE,SAAbA,WAAWA,CAAYJ,MAAM,EAAE;UAC7BA,MAAM,CAACK,cAAc,CAAC,CAAC;UACvB,OAAO5B,GAAG,CAAC6B,QAAQ,CAACjB,GAAG,EAAEW,MAAM,CAAC;QAClC;MACF;IACF,CAAC,EACD,CACEvB,GAAG,CAAC8B,EAAE,CAAC,GAAG,GAAG9B,GAAG,CAAC+B,EAAE,CAACnB,GAAG,CAACoB,KAAK,CAAC,GAAG,GAAG,CAAC,EACrC,CAAChC,GAAG,CAACyB,OAAO,CAACb,GAAG,CAAC,GACbX,EAAE,CAAC,MAAM,EAAE;MACTE,WAAW,EAAE,eAAe;MAC5BI,EAAE,EAAE;QACF0B,KAAK,EAAE,SAAPA,KAAKA,CAAYV,MAAM,EAAE;UACvBA,MAAM,CAACK,cAAc,CAAC,CAAC;UACvBL,MAAM,CAACW,eAAe,CAAC,CAAC;UACxB,OAAOlC,GAAG,CAAC0B,gBAAgB,CAACd,GAAG,CAAC;QAClC;MACF;IACF,CAAC,CAAC,GACFZ,GAAG,CAACmC,EAAE,CAAC,CAAC,CAEhB,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDlC,EAAE,CACA,IAAI,EACJ;IACEmC,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEvC,GAAG,CAACwC,OAAO;MAClBC,UAAU,EAAE;IACd,CAAC,CACF;IACDtC,WAAW,EAAE,aAAa;IAC1BuC,KAAK,EAAE;MAAEC,IAAI,EAAE3C,GAAG,CAAC2C,IAAI,GAAG,IAAI;MAAEC,GAAG,EAAE5C,GAAG,CAAC4C,GAAG,GAAG;IAAK;EACtD,CAAC,EACD,CACE3C,EAAE,CACA,IAAI,EACJ;IACEM,EAAE,EAAE;MACF0B,KAAK,EAAE,SAAPA,KAAKA,CAAYV,MAAM,EAAE;QACvB,OAAOvB,GAAG,CAAC6C,kBAAkB,CAAC7C,GAAG,CAAC8C,WAAW,CAAC;MAChD;IACF;EACF,CAAC,EACD,CAAC9C,GAAG,CAAC8B,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACD,CAAC9B,GAAG,CAACyB,OAAO,CAACzB,GAAG,CAAC8C,WAAW,CAAC,GACzB7C,EAAE,CACA,IAAI,EACJ;IACEM,EAAE,EAAE;MACF0B,KAAK,EAAE,SAAPA,KAAKA,CAAYV,MAAM,EAAE;QACvB,OAAOvB,GAAG,CAAC0B,gBAAgB,CAAC1B,GAAG,CAAC8C,WAAW,CAAC;MAC9C;IACF;EACF,CAAC,EACD,CAAC9C,GAAG,CAAC8B,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,GACD9B,GAAG,CAACmC,EAAE,CAAC,CAAC,EACZlC,EAAE,CAAC,IAAI,EAAE;IAAEM,EAAE,EAAE;MAAE0B,KAAK,EAAEjC,GAAG,CAAC+C;IAAgB;EAAE,CAAC,EAAE,CAC/C/C,GAAG,CAAC8B,EAAE,CAAC,cAAc,CAAC,CACvB,CAAC,EACF7B,EAAE,CACA,IAAI,EACJ;IACEM,EAAE,EAAE;MACF0B,KAAK,EAAE,SAAPA,KAAKA,CAAYV,MAAM,EAAE;QACvB,OAAOvB,GAAG,CAACgD,YAAY,CAAChD,GAAG,CAAC8C,WAAW,CAAC;MAC1C;IACF;EACF,CAAC,EACD,CAAC9C,GAAG,CAAC8B,EAAE,CAAC,WAAW,CAAC,CACtB,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAImB,eAAe,GAAG,EAAE;AACxBlD,MAAM,CAACmD,aAAa,GAAG,IAAI;AAE3B,SAASnD,MAAM,EAAEkD,eAAe", "ignoreList": []}]}