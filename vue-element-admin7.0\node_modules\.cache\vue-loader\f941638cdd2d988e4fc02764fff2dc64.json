{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\index.vue?vue&type=style&index=1&id=37dfd6fc&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\index.vue", "mtime": 1747748935260}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA8PA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/login", "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <img src=\"../../assets/used_images/北邮logo白色-去背景.png\" class=\"small-image\" alt=\"Small Image\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" autocomplete=\"on\" label-position=\"left\">\n\n      <div class=\"title-container\">\n        <h3 class=\"title\">地下钱庄大数据智能分析系统</h3>\n      </div>\n\n      <el-form-item prop=\"username\">\n        <span class=\"svg-container\">\n          <svg-icon icon-class=\"user\" />\n        </span>\n        <el-input\n          ref=\"username\"\n          v-model=\"loginForm.username\"\n          placeholder=\"Username\"\n          name=\"username\"\n          type=\"text\"\n          tabindex=\"1\"\n          autocomplete=\"on\"\n        />\n      </el-form-item>\n\n      <el-tooltip v-model=\"capsTooltip\" content=\"Caps lock is On\" placement=\"right\" manual>\n        <el-form-item prop=\"password\">\n          <span class=\"svg-container\">\n            <svg-icon icon-class=\"password\" />\n          </span>\n          <el-input\n            :key=\"passwordType\"\n            ref=\"password\"\n            v-model=\"loginForm.password\"\n            :type=\"passwordType\"\n            placeholder=\"Password\"\n            name=\"password\"\n            tabindex=\"2\"\n            autocomplete=\"on\"\n            @keyup.native=\"checkCapslock\"\n            @blur=\"capsTooltip = false\"\n            @keyup.enter.native=\"handleLogin\"\n          />\n          <span class=\"show-pwd\" @click=\"showPwd\">\n            <svg-icon :icon-class=\"passwordType === 'password' ? 'eye' : 'eye-open'\" />\n          </span>\n        </el-form-item>\n      </el-tooltip>\n\n      <el-button :loading=\"loading\" type=\"primary\" style=\"width:100%;margin-bottom:30px;\" @click.native.prevent=\"handleLogin\">Login</el-button>\n\n      <!-- <div style=\"position:relative\">\n        <div class=\"tips\">\n          <span>Username : admin</span>\n          <span>Password : any</span>\n        </div>\n        <div class=\"tips\">\n          <span style=\"margin-right:18px;\">Username : editor</span>\n          <span>Password : any</span>\n        </div>\n\n        <el-button class=\"thirdparty-button\" type=\"primary\" @click=\"showDialog=true\">\n          Or connect with\n        </el-button>\n      </div> -->\n    </el-form>\n\n    <el-dialog title=\"Or connect with\" :visible.sync=\"showDialog\">\n      Can not be simulated on local, so please combine you own business simulation! ! !\n      <br>\n      <br>\n      <br>\n      <social-sign />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { validUsername } from '@/utils/validate'\nimport SocialSign from './components/SocialSignin'\n\nexport default {\n  name: 'Login',\n  components: { SocialSign },\n  data() {\n    const validateUsername = (rule, value, callback) => {\n      if (!validUsername(value)) {\n        callback(new Error('Please enter the correct user name'))\n      } else {\n        callback()\n      }\n    }\n    const validatePassword = (rule, value, callback) => {\n      if (value.length < 6) {\n        callback(new Error('The password can not be less than 6 digits'))\n      } else {\n        callback()\n      }\n    }\n    return {\n      loginForm: {\n        username: 'editor',\n        password: '111111'\n      },\n      loginRules: {\n        username: [{ required: true, trigger: 'blur', validator: validateUsername }],\n        password: [{ required: true, trigger: 'blur', validator: validatePassword }]\n      },\n      passwordType: 'password',\n      capsTooltip: false,\n      loading: false,\n      showDialog: false,\n      redirect: undefined,\n      otherQuery: {}\n    }\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        const query = route.query\n        if (query) {\n          this.redirect = query.redirect\n          this.otherQuery = this.getOtherQuery(query)\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    // window.addEventListener('storage', this.afterQRScan)\n  },\n  mounted() {\n    if (this.loginForm.username === '') {\n      this.$refs.username.focus()\n    } else if (this.loginForm.password === '') {\n      this.$refs.password.focus()\n    }\n  },\n  destroyed() {\n    // window.removeEventListener('storage', this.afterQRScan)\n  },\n  methods: {\n    checkCapslock(e) {\n      const { key } = e\n      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')\n    },\n    showPwd() {\n      if (this.passwordType === 'password') {\n        this.passwordType = ''\n      } else {\n        this.passwordType = 'password'\n      }\n      this.$nextTick(() => {\n        this.$refs.password.focus()\n      })\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          this.$store.dispatch('user/login', this.loginForm)\n            .then(() => {\n              this.$router.push({ path: this.redirect || '/', query: this.otherQuery })\n              this.loading = false\n            })\n            .catch(() => {\n              this.loading = false\n            })\n        } else {\n          console.log('error submit!!')\n          return false\n        }\n      })\n    },\n    getOtherQuery(query) {\n      return Object.keys(query).reduce((acc, cur) => {\n        if (cur !== 'redirect') {\n          acc[cur] = query[cur]\n        }\n        return acc\n      }, {})\n    }\n    // afterQRScan() {\n    //   if (e.key === 'x-admin-oauth-code') {\n    //     const code = getQueryObject(e.newValue)\n    //     const codeMap = {\n    //       wechat: 'code',\n    //       tencent: 'code'\n    //     }\n    //     const type = codeMap[this.auth_type]\n    //     const codeName = code[type]\n    //     if (codeName) {\n    //       this.$store.dispatch('LoginByThirdparty', codeName).then(() => {\n    //         this.$router.push({ path: this.redirect || '/' })\n    //       })\n    //     } else {\n    //       alert('第三方登录失败')\n    //     }\n    //   }\n    // }\n  }\n}\n\n// 导出 USER 作为常量\nexport const USER = 'editor'\n</script>\n\n<style lang=\"scss\">\n/* 修复input 背景不协调 和光标变色 */\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\n\n$bg:#283443;\n$light_gray:#fff;\n$cursor: #fff;\n\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\n  .login-container .el-input input {\n    color: $cursor;\n  }\n}\n\n/* reset element-ui css */\n.login-container {\n  .el-input {\n    display: inline-block;\n    height: 47px;\n    width: 85%;\n\n    input {\n      background: transparent;\n      border: 0px;\n      -webkit-appearance: none;\n      border-radius: 0px;\n      padding: 12px 5px 12px 15px;\n      color: $light_gray;\n      height: 47px;\n      caret-color: $cursor;\n\n      &:-webkit-autofill {\n        box-shadow: 0 0 0px 1000px $bg inset !important;\n        -webkit-text-fill-color: $cursor !important;\n      }\n    }\n  }\n\n  .el-form-item {\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    background: rgba(0, 0, 0, 0.1);\n    border-radius: 5px;\n    color: #454545;\n  }\n}\n</style>\n\n<style lang=\"scss\" scoped>\n$bg:#2d3a4b;\n$dark_gray:#889aa4;\n$light_gray:#eee;\n\n.login-container {\n  background-image: url('../../assets/used_images/background1.jpg'); /* 替换为你的图片路径 */\n  background-size: cover; /* 使背景图像覆盖整个容器 */\n  background-position: center; /* 将背景图像居中 */\n  background-repeat: no-repeat; /* 不重复 */\n  height: 100vh; /* 设置容器的高度 */\n  display: flex; /* 使用 Flexbox 使内容居中 */\n  align-items: center; /* 垂直居中 */\n  justify-content: center; /* 水平居中 */\n}\n\n.small-image {\n  position: absolute; /* 绝对定位 */\n  top: 10px; /* 距离上方的距离 */\n  left: 10px; /* 距离左侧的距离 */\n  width: 130px; /* 设置宽度 */\n  height: 130px; /* 高度自适应 */\n  opacity: 1; /* 设置透明度 */\n  z-index: 10; /* 确保小图在其它元素上方 */\n  transition: transform 0.3s; /* 添加过渡效果 */\n}\n\n.login-container {\n  min-height: 100%;\n  width: 100%;\n  background-color: $bg;\n  overflow: hidden;\n\n  .login-form {\n    position: relative;\n    width: 520px;\n    max-width: 100%;\n    padding: 60px 35px 0;\n    margin: 0 auto;\n    overflow: hidden;\n  }\n\n  .tips {\n    font-size: 14px;\n    color: #fff;\n    margin-bottom: 10px;\n\n    span {\n      &:first-of-type {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .svg-container {\n    padding: 6px 5px 6px 15px;\n    color: $dark_gray;\n    vertical-align: middle;\n    width: 30px;\n    display: inline-block;\n  }\n\n  .title-container {\n    position: relative;\n\n    .title {\n      font-size: 26px;\n      color: $light_gray;\n      margin: 0px auto 40px auto;\n      text-align: center;\n      font-weight: bold;\n    }\n  }\n\n  .show-pwd {\n    position: absolute;\n    right: 10px;\n    top: 7px;\n    font-size: 16px;\n    color: $dark_gray;\n    cursor: pointer;\n    user-select: none;\n  }\n\n  .thirdparty-button {\n    position: absolute;\n    right: 0;\n    bottom: 6px;\n  }\n\n  @media only screen and (max-width: 470px) {\n    .thirdparty-button {\n      display: none;\n    }\n  }\n}\n</style>\n"]}]}