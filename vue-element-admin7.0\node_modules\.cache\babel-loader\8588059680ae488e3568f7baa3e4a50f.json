{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\page.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\page.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFN3aXRjaFJvbGVzIGZyb20gJy4vY29tcG9uZW50cy9Td2l0Y2hSb2xlcyc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUGFnZVBlcm1pc3Npb24nLAogIGNvbXBvbmVudHM6IHsKICAgIFN3aXRjaFJvbGVzOiBTd2l0Y2hSb2xlcwogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlUm9sZXNDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVJvbGVzQ2hhbmdlKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogJy9wZXJtaXNzaW9uL2luZGV4PycgKyArbmV3IERhdGUoKQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["SwitchRoles", "name", "components", "methods", "handleRolesChange", "$router", "push", "path", "Date"], "sources": ["src/views/permission/page.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <switch-roles @change=\"handleRolesChange\" />\n  </div>\n</template>\n\n<script>\nimport SwitchRoles from './components/SwitchRoles'\n\nexport default {\n  name: 'PagePermission',\n  components: { SwitchRoles },\n  methods: {\n    handleRolesChange() {\n      this.$router.push({ path: '/permission/index?' + +new Date() })\n    }\n  }\n}\n</script>\n"], "mappings": "AAOA,OAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,WAAA,EAAAA;EAAA;EACAG,OAAA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA,8BAAAC,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}