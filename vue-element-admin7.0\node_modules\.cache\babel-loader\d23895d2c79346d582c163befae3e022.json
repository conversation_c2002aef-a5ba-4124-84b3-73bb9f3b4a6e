{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\settings.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\settings.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHZhcmlhYmxlcyBmcm9tICdAL3N0eWxlcy9lbGVtZW50LXZhcmlhYmxlcy5zY3NzJzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHNob3dTZXR0aW5ncyA9IGRlZmF1bHRTZXR0aW5ncy5zaG93U2V0dGluZ3MsCiAgdGFnc1ZpZXcgPSBkZWZhdWx0U2V0dGluZ3MudGFnc1ZpZXcsCiAgZml4ZWRIZWFkZXIgPSBkZWZhdWx0U2V0dGluZ3MuZml4ZWRIZWFkZXIsCiAgc2lkZWJhckxvZ28gPSBkZWZhdWx0U2V0dGluZ3Muc2lkZWJhckxvZ287CnZhciBzdGF0ZSA9IHsKICB0aGVtZTogdmFyaWFibGVzLnRoZW1lLAogIHNob3dTZXR0aW5nczogc2hvd1NldHRpbmdzLAogIHRhZ3NWaWV3OiB0YWdzVmlldywKICBmaXhlZEhlYWRlcjogZml4ZWRIZWFkZXIsCiAgc2lkZWJhckxvZ286IHNpZGViYXJMb2dvCn07CnZhciBtdXRhdGlvbnMgPSB7CiAgQ0hBTkdFX1NFVFRJTkc6IGZ1bmN0aW9uIENIQU5HRV9TRVRUSU5HKHN0YXRlLCBfcmVmKSB7CiAgICB2YXIga2V5ID0gX3JlZi5rZXksCiAgICAgIHZhbHVlID0gX3JlZi52YWx1ZTsKICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1wcm90b3R5cGUtYnVpbHRpbnMKICAgIGlmIChzdGF0ZS5oYXNPd25Qcm9wZXJ0eShrZXkpKSB7CiAgICAgIHN0YXRlW2tleV0gPSB2YWx1ZTsKICAgIH0KICB9Cn07CnZhciBhY3Rpb25zID0gewogIGNoYW5nZVNldHRpbmc6IGZ1bmN0aW9uIGNoYW5nZVNldHRpbmcoX3JlZjIsIGRhdGEpIHsKICAgIHZhciBjb21taXQgPSBfcmVmMi5jb21taXQ7CiAgICBjb21taXQoJ0NIQU5HRV9TRVRUSU5HJywgZGF0YSk7CiAgfQp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZXNwYWNlZDogdHJ1ZSwKICBzdGF0ZTogc3RhdGUsCiAgbXV0YXRpb25zOiBtdXRhdGlvbnMsCiAgYWN0aW9uczogYWN0aW9ucwp9Ow=="}, {"version": 3, "names": ["variables", "defaultSettings", "showSettings", "tagsView", "fixedHeader", "sidebarLogo", "state", "theme", "mutations", "CHANGE_SETTING", "_ref", "key", "value", "hasOwnProperty", "actions", "changeSetting", "_ref2", "data", "commit", "namespaced"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/store/modules/settings.js"], "sourcesContent": ["import variables from '@/styles/element-variables.scss'\nimport defaultSettings from '@/settings'\n\nconst { showSettings, tagsView, fixedHeader, sidebarLogo } = defaultSettings\n\nconst state = {\n  theme: variables.theme,\n  showSettings: showSettings,\n  tagsView: tagsView,\n  fixedHeader: fixedHeader,\n  sidebarLogo: sidebarLogo\n}\n\nconst mutations = {\n  CHANGE_SETTING: (state, { key, value }) => {\n    // eslint-disable-next-line no-prototype-builtins\n    if (state.hasOwnProperty(key)) {\n      state[key] = value\n    }\n  }\n}\n\nconst actions = {\n  changeSetting({ commit }, data) {\n    commit('CHANGE_SETTING', data)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iCAAiC;AACvD,OAAOC,eAAe,MAAM,YAAY;AAExC,IAAQC,YAAY,GAAyCD,eAAe,CAApEC,YAAY;EAAEC,QAAQ,GAA+BF,eAAe,CAAtDE,QAAQ;EAAEC,WAAW,GAAkBH,eAAe,CAA5CG,WAAW;EAAEC,WAAW,GAAKJ,eAAe,CAA/BI,WAAW;AAExD,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAEP,SAAS,CAACO,KAAK;EACtBL,YAAY,EAAEA,YAAY;EAC1BC,QAAQ,EAAEA,QAAQ;EAClBC,WAAW,EAAEA,WAAW;EACxBC,WAAW,EAAEA;AACf,CAAC;AAED,IAAMG,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAGH,KAAK,EAAAI,IAAA,EAAqB;IAAA,IAAjBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAClC;IACA,IAAIN,KAAK,CAACO,cAAc,CAACF,GAAG,CAAC,EAAE;MAC7BL,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,KAAA,CAANE,MAAM;IACpBA,MAAM,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAChC;AACF,CAAC;AAED,eAAe;EACbE,UAAU,EAAE,IAAI;EAChBb,KAAK,EAALA,KAAK;EACLE,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}