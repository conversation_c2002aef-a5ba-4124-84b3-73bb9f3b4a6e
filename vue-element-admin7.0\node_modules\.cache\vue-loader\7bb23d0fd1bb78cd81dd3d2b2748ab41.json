{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\TransmitDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\TransmitDetail.vue", "mtime": 1747731078864}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgQ2hhcnQgZnJvbSAnQC9jb21wb25lbnRzL0NoYXJ0cy9UcmFuc21pdERldGFpbCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnVHJhbnNtaXREZXRhaWwnLA0KICBjb21wb25lbnRzOiB7IENoYXJ0IH0NCn0NCg=="}, {"version": 3, "sources": ["TransmitDetail.vue"], "names": [], "mappings": ";AAOA;;AAEA;AACA;AACA;AACA", "file": "TransmitDetail.vue", "sourceRoot": "src/views/charts", "sourcesContent": ["<template>\r\n    <div class=\"chart-container\">\r\n      <chart height=\"100%\" width=\"100%\" />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport Chart from '@/components/Charts/TransmitDetail'\r\n\r\nexport default {\r\n  name: 'TransmitDetail',\r\n  components: { Chart }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chart-container{\r\n  position: relative;\r\n  width: 100%;\r\n  height: calc(100vh - 84px);\r\n}\r\n</style>\r\n"]}]}