{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\main.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\main.js", "mtime": 1747748935263}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Cookies", "Element", "App", "store", "router", "filters", "process", "env", "NODE_ENV", "_require", "require", "mockXHR", "use", "size", "get", "Object", "keys", "for<PERSON>ach", "key", "filter", "config", "productionTip", "el", "render", "h"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/main.js"], "sourcesContent": ["import Vue from 'vue'\n\nimport Cookies from 'js-cookie'\n\nimport 'normalize.css/normalize.css' // a modern alternative to CSS resets\n\nimport Element from 'element-ui'\nimport './styles/element-variables.scss'\n\nimport '@/styles/index.scss' // global css\n\nimport App from './App'\nimport store from './store'\nimport router from './router'\n\nimport './icons' // icon\nimport './permission' // permission control\nimport './utils/error-log' // error log\n\nimport * as filters from './filters' // global filters\n\n/**\n * If you don't want to use mock-server\n * you want to use MockJs for mock api\n * you can execute: mockXHR()\n *\n * Currently MockJs will be used in the production environment,\n * please remove it before going online ! ! !\n */\nif (process.env.NODE_ENV === 'production') {\n  const { mockXHR } = require('../mock')\n  mockXHR()\n}\n\nVue.use(Element, {\n  size: Cookies.get('size') || 'medium' // set element-ui default size\n\n})\n\n// register global utility filters\nObject.keys(filters).forEach(key => {\n  Vue.filter(key, filters[key])\n})\n\nVue.config.productionTip = false\n\nnew Vue({\n  el: '#app',\n  router,\n  store,\n  render: h => h(App)\n})\n"], "mappings": ";;;;;;;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AAErB,OAAOC,OAAO,MAAM,WAAW;AAE/B,OAAO,6BAA6B,EAAC;;AAErC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAO,iCAAiC;AAExC,OAAO,qBAAqB,EAAC;;AAE7B,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,SAAS,EAAC;AACjB,OAAO,cAAc,EAAC;AACtB,OAAO,mBAAmB,EAAC;;AAE3B,OAAO,KAAKC,OAAO,MAAM,WAAW,EAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,IAAAC,QAAA,GAAoBC,OAAO,CAAC,SAAS,CAAC;IAA9BC,OAAO,GAAAF,QAAA,CAAPE,OAAO;EACfA,OAAO,CAAC,CAAC;AACX;AAEAZ,GAAG,CAACa,GAAG,CAACX,OAAO,EAAE;EACfY,IAAI,EAAEb,OAAO,CAACc,GAAG,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC;AAExC,CAAC,CAAC;;AAEF;AACAC,MAAM,CAACC,IAAI,CAACX,OAAO,CAAC,CAACY,OAAO,CAAC,UAAAC,GAAG,EAAI;EAClCnB,GAAG,CAACoB,MAAM,CAACD,GAAG,EAAEb,OAAO,CAACa,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEFnB,GAAG,CAACqB,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAItB,GAAG,CAAC;EACNuB,EAAE,EAAE,MAAM;EACVlB,MAAM,EAANA,MAAM;EACND,KAAK,EAALA,KAAK;EACLoB,MAAM,EAAE,SAARA,MAAMA,CAAEC,CAAC;IAAA,OAAIA,CAAC,CAACtB,GAAG,CAAC;EAAA;AACrB,CAAC,CAAC", "ignoreList": []}]}