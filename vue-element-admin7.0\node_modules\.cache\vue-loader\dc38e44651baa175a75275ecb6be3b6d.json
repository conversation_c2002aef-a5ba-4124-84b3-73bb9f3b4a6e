{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue?vue&type=style&index=0&id=5fa13db8&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue", "mtime": 1748922947668}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RateGraph.vue"], "names": [], "mappings": ";AAyRA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RateGraph.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" :disabled=\"!value\" @click=\"confirmSelection\">确认</el-button>\r\n    <el-button type=\"info\" :disabled=\"!value\" @click=\"resetWeights\">重置权重</el-button>\r\n\r\n    <!-- 权重设置区域 -->\r\n    <div class=\"weight-settings\">\r\n      <h3>标签权重设置</h3>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>快进快出：</span>\r\n          <el-slider v-model=\"weights.flag2\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>高频交易：</span>\r\n          <el-slider v-model=\"weights.flag3\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>时间集中：</span>\r\n          <el-slider v-model=\"weights.flag4\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>小额测试：</span>\r\n          <el-slider v-model=\"weights.flag5\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card style=\"margin: 20px; min-height: 520px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>嫌疑排行榜</span>\r\n      </div>\r\n\r\n      <div class=\"scroll-container\">\r\n        <div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n          <el-table :data=\"rankData\" style=\"width: 100%\">\r\n            <el-table-column type=\"index\" label=\"排名\" width=\"80\" />\r\n            <el-table-column prop=\"name\" label=\"姓名 / 公司名称\">\r\n              <template #default=\"scope\">\r\n                <el-link\r\n                  type=\"primary\"\r\n                  @click=\"handleNameClick(scope.row.name)\"\r\n                >\r\n                  {{ scope.row.name }}\r\n                </el-link>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fused_score\" label=\"嫌疑分值\">\r\n              <template #default=\"scope\">\r\n                {{ Number(scope.row.fused_score).toFixed(3) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"count\" label=\"命中标签数\">\r\n              <template #default=\"scope\">{{ scope.row.count }}</template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"flag2\" label=\"快进快出\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag2 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag3\" label=\"高频交易\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag3 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag4\" label=\"时间集中\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag4 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag5\" label=\"小额测试\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag5 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <div\r\n          v-if=\"isBottom\"\r\n          class=\"bottom-notice\"\r\n          :class=\"{ 'show-notice': isBottom }\"\r\n        >\r\n          已经到达末尾\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport { mapState, mapActions } from 'vuex'\r\n\r\nexport default {\r\n  name: 'RateGraph',\r\n  data() {\r\n    return {\r\n      options: [],\r\n      dateRange: [],\r\n      username: null,\r\n      isBottom: false,\r\n      weights: {\r\n        flag2: 1,\r\n        flag3: 1,\r\n        flag4: 1,\r\n        flag5: 1\r\n      },\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      rankData: state => state.rateGraph.rankData,\r\n      weights: state => state.rateGraph.weights,\r\n      dataLoaded: state => state.rateGraph.dataLoaded,\r\n      selectedTable: state => state.rateGraph.selectedTable\r\n    }),\r\n    value: {\r\n      get() {\r\n        return this.selectedTable\r\n      },\r\n      set(value) {\r\n        this.selectTable(value)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch()\r\n    this.scrollContainer = this.$refs.scrollContainer\r\n\r\n    // If we already have a selected table, update the dropdown\r\n    if (this.selectedTable) {\r\n      this.value = this.selectedTable\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      updateRankData: 'rateGraph/updateRankData',\r\n      selectTable: 'rateGraph/selectTable',\r\n      updateWeights: 'rateGraph/updateWeights',\r\n      resetWeightsAction: 'rateGraph/resetWeights'\r\n    }),\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      if (value) {\r\n        this.$message.info('已选择数据表，请点击「确认」按钮生成排行榜')\r\n      }\r\n    },\r\n    handleNameClick(name) {\r\n      this.$router.push({\r\n        path: '/charts/transmit-detail',\r\n        query: {\r\n          name: encodeURIComponent(name),\r\n          timeUnit: 'month'\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item,\r\n            value: item\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    },\r\n    async generateRankData(weights) {\r\n      try {\r\n        console.log(11)\r\n        if (weights) {\r\n          console.log('发送权重到后端:', weights)\r\n          try {\r\n            const res = await axios.post('http://127.0.0.1:8000/tianye_demo',\r\n              { weights: [this.weights.flag2, this.weights.flag3, this.weights.flag4, this.weights.flag5] }, // 数据\r\n              { // 配置\r\n                headers: {\r\n                  'Content-Type': 'application/json'\r\n                }\r\n              })\r\n            console.log('后端响应:', res)\r\n            console.log('后端', res.data.data)\r\n            this.updateRankData(res.data.data)\r\n          } catch (postError) {\r\n            const getRes = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n            this.updateRankData(getRes.data)\r\n          }\r\n        }\r\n        // } else {\r\n        //   console.log('默认')\r\n        //   try {\r\n        //     const res = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n        //     console.log('获取默认数据成功:', res)\r\n        //     this.updateRankData(res.data.data)\r\n        //   } catch (error) {\r\n        //     console.error('请求失败详细信息:', error)\r\n        //     if (error.response) {\r\n        //       console.error('服务器响应:', error.response.status, error.response.data)\r\n        //     } else if (error.request) {\r\n        //       console.error('没有收到响应，请求详情:', error.request)\r\n        //     } else {\r\n        //       console.error('请求设置错误:', error.message)\r\n        //     }\r\n        //     this.$message.error('连接后端失败，请检查网络和服务器状态')\r\n        //     throw error\r\n        //   }\r\n        // }\r\n        return Promise.resolve()\r\n      } catch (error) {\r\n        console.error('Error fetching rank data:', error)\r\n        this.$message.error('获取数据失败:', error.message)\r\n        return Promise.reject(error)\r\n      }\r\n    },\r\n    confirmSelection() {\r\n      // Only fetch data if we haven't loaded it yet or if the user explicitly wants to refresh\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: '正在处理数据...',\r\n        spinner: 'el-icon-loading',\r\n        background: 'rgba(255, 255, 255, 0.7)'\r\n      })\r\n\r\n      // Update the weights in the store\r\n      this.updateWeights(this.weights)\r\n\r\n      this.generateRankData(this.weights)\r\n        .then(() => {\r\n          loading.close()\r\n          this.$message.success('数据处理成功')\r\n        })\r\n        .catch(() => {\r\n          loading.close()\r\n        })\r\n    },\r\n    resetWeights() {\r\n      this.resetWeightsAction()\r\n      this.$message.info('已重置权重为默认值，点击「确认」按钮生效')\r\n    },\r\n    handleScroll({ scrollTop, scrollHeight, clientHeight }) {\r\n      this.isBottom = scrollTop + clientHeight >= scrollHeight - 5\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* Element UI 表格样式覆盖 */\r\n.el-table {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.scroll-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: calc(100vh - 200px); /* 根据实际情况调整 */\r\n  min-height: 380px;\r\n}\r\n\r\n/* 调整滚动条样式 */\r\n.custom-scrollbar {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  margin-bottom: 10px; /* 增加与底部提示的间距 */\r\n}\r\n\r\n/* 底部提示样式优化 */\r\n.bottom-notice {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  text-align: center;\r\n  color: #909399;\r\n  background: #f5f7fa;\r\n  border-top: 1px solid #dfe6ec;\r\n  transition: all 0.3s;\r\n  opacity: 0;\r\n}\r\n\r\n.show-notice {\r\n  opacity: 1;\r\n  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n/* 表格列样式微调 */\r\n.el-table-column {\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 权重设置区域样式 */\r\n.weight-settings {\r\n  margin: 12px;\r\n  padding: 12px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.weight-settings h3 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.weight-item {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;  /* 关键：平均分配宽度 */\r\n  height: 32px;\r\n}\r\n\r\n/* 新增：确保滑块容器占满剩余空间 */\r\n.weight-item .el-slider {\r\n  flex: 1;\r\n  min-width: 120px;  /* 防止内容过窄 */\r\n}\r\n\r\n.weight-item span {\r\n  width: 70px;\r\n  text-align: right;\r\n  margin-right: 10px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 保持滑块细节样式 */\r\n.weight-item .el-slider__runway {\r\n  height: 4px !important;\r\n}\r\n.weight-item .el-slider__button {\r\n  width: 12px !important;\r\n  height: 12px !important;\r\n}\r\n</style>\r\n"]}]}