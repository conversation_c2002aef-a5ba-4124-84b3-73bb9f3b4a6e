{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\editor\\index.vue", "mtime": 1731833050000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgUGFuVGh1bWIgZnJvbSAnQC9jb21wb25lbnRzL1BhblRodW1iJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEYXNoYm9hcmRFZGl0b3InLAogIGNvbXBvbmVudHM6IHsgUGFuVGh1bWIgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZW1wdHlHaWY6ICdodHRwczovL3dwaW1nLndhbGxzdGNuLmNvbS8wZTAzYjdkYS1kYjllLTQ4MTktYmExMC05MDE2ZGRmZGFlZDMnCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgLi4ubWFwR2V0dGVycyhbCiAgICAgICduYW1lJywKICAgICAgJ2F2YXRhcicsCiAgICAgICdyb2xlcycKICAgIF0pCiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard/editor", "sourcesContent": ["<template>\n  <div class=\"dashboard-editor-container\">\n    <div class=\" clearfix\">\n      <p>哈哈哈哈</p>\n      <pan-thumb :image=\"avatar\" style=\"float: left\">\n        Your roles:\n        <span v-for=\"item in roles\" :key=\"item\" class=\"pan-info-roles\">{{ item }}</span>\n      </pan-thumb>\n      <div class=\"info-container\">\n        <span class=\"display_name\">{{ name }}</span>\n        <span style=\"font-size:20px;padding-top:20px;display:inline-block;\">Editor's Dashboard</span>\n      </div>\n    </div>\n    <div>\n      <img :src=\"emptyGif\" class=\"emptyGif\">\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport PanThumb from '@/components/PanThumb'\n\nexport default {\n  name: 'DashboardEditor',\n  components: { PanThumb },\n  data() {\n    return {\n      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3'\n    }\n  },\n  computed: {\n    ...mapGetters([\n      'name',\n      'avatar',\n      'roles'\n    ])\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .emptyGif {\n    display: block;\n    width: 45%;\n    margin: 0 auto;\n  }\n\n  .dashboard-editor-container {\n    background-color: #e3e3e3;\n    min-height: 100vh;\n    padding: 50px 60px 0px;\n    .pan-info-roles {\n      font-size: 12px;\n      font-weight: 700;\n      color: #333;\n      display: block;\n    }\n    .info-container {\n      position: relative;\n      margin-left: 190px;\n      height: 150px;\n      line-height: 200px;\n      .display_name {\n        font-size: 48px;\n        line-height: 48px;\n        color: #212121;\n        position: absolute;\n        top: 25px;\n      }\n    }\n  }\n</style>\n"]}]}