{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\lineChart.vue?vue&type=style&index=0&id=705be72a&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\lineChart.vue", "mtime": 1747748935261}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucXVlcnktZm9ybXsNCiAgICBtYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KDQouYmFuay1hY2NvdW50IHsNCiAgbWFyZ2luLWxlZnQ6IDE1cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7IC8qIOWeguebtOWxheS4reWvuem9kCAqLw0KfQ0K"}, {"version": 3, "sources": ["lineChart.vue"], "names": [], "mappings": ";AAqUA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "lineChart.vue", "sourceRoot": "src/views/charts", "sourcesContent": ["<template>\r\n    <div>\r\n      <!-- 查询条件表单 -->\r\n      <el-form :inline=\"true\" :model=\"queryForm\" label-width=\"100px\" class=\"query-form\">\r\n        <!-- 数据表输入 -->\r\n        <el-select\r\n            v-model=\"value\"\r\n            placeholder=\"数据表\"\r\n            no-data-text=\"已经没有数据表了\"\r\n            style=\"margin-left: 20px;\"\r\n            @focus=\"handleSearch\"\r\n            @change=\"handleSelectChange\"\r\n        >\r\n            <el-option\r\n            v-for=\"item in options\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.value\"\n/>\r\n        </el-select>\r\n        <!-- 用户名输入 -->\r\n        <el-form-item style=\"margin-left: 20px;\">\r\n          <el-input v-model=\"queryForm.username\" placeholder=\"请输入用户名\" clearable />\r\n        </el-form-item>\r\n\r\n        <!-- 日期时间区间选择 -->\r\n        <el-form-item style=\"margin-left: 20px;\">\r\n          <el-date-picker\r\n            v-model=\"queryForm.dateRange\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期时间\"\r\n            end-placeholder=\"结束日期时间\"\r\n            format=\"yyyy-MM-dd HH:mm:ss\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 350px;\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 查询刻度选择 -->\r\n        <el-form-item label=\"查询刻度\">\r\n          <el-select v-model=\"queryForm.timeScale\" placeholder=\"请选择刻度\" clearable>\r\n            <el-option label=\"按小时\" value=\"hour\" />\r\n            <el-option label=\"按日\" value=\"day\" />\r\n            <el-option label=\"按月\" value=\"month\" />\r\n            <el-option label=\"按年\" value=\"year\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 查询按钮 -->\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"onSearch\">查询</el-button>\r\n          <!-- <el-button @click=\"onReset\">重置</el-button> -->\r\n        </el-form-item>\r\n\r\n        <!-- 二级查询栏 -->\r\n        <el-form-item v-if=\"showSecondaryQuery\" :inline=\"true\" label=\"银行账户\" class=\"bank-account\">\r\n          <el-checkbox v-model=\"checkAll\" :indeterminate=\"isIndeterminate\" style=\"padding-left: 30px;\" @change=\"handleCheckAllChange\">全选</el-checkbox>\r\n            <el-checkbox-group v-model=\"checkedAccounts\" style=\"padding-left: 30px\" @change=\"handleCheckedCitiesChange\">\r\n            <el-checkbox v-for=\"account in accounts\" :key=\"account\" :label=\"account\">{{ account }}</el-checkbox>\r\n          </el-checkbox-group>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n      <!-- 折线图展示 -->\r\n      <div ref=\"chart\" style=\"width: 100%; height: 600px; margin-top: 0px;\" />\r\n    </div>\r\n  </template>\r\n<script>\r\nimport axios from 'axios'\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      value: '', // 选中的数据表\r\n      dealData: [],\r\n      // 查询表单数据\r\n      checkAll: true,\r\n      checkedAccounts: [],\r\n      accounts: [],\r\n      isIndeterminate: false,\r\n      queryForm: {\r\n        username: '', // 用户名\r\n        dateRange: [], // 日期时间区间\r\n        timeScale: 'hour', // 查询刻度，默认为“按小时”\r\n        secondaryOption: ''\r\n      },\r\n      showSecondaryQuery: false,\r\n      chart: null // 折线图实例\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart()\r\n  },\r\n  methods: {\r\n    // 初始化折线图\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chart)\r\n    },\r\n    // 查询按钮点击事件\r\n    async onSearch() {\r\n      if (!this.queryForm.username) {\r\n        this.$message.error('请输入用户名')\r\n        return\r\n      }\r\n      if (!this.queryForm.dateRange.length) {\r\n        this.$message.error('请选择日期时间区间')\r\n        return\r\n      }\r\n      if (!this.queryForm.timeScale) {\r\n        this.$message.error('请选择查询刻度')\r\n        return\r\n      }\r\n      this.showSecondaryQuery = true // 显示二级查询栏\r\n      // 模拟数据查询和更新图表\r\n      await this.fetchChartData()\r\n    },\r\n    // 重置按钮点击事件\r\n    onReset() {\r\n      this.queryForm.username = ''\r\n      this.queryForm.dateRange = []\r\n      this.queryForm.timeScale = 'hour'\r\n      this.showSecondaryQuery = false // 隐藏二级查询栏\r\n    },\r\n    // 模拟查询数据并更新图表\r\n    async fetchChartData() {\r\n      const [startDate, endDate] = this.queryForm.dateRange\r\n      const timeScale = this.queryForm.timeScale\r\n      const username = this.queryForm.username\r\n      const selectedAccounts = this.checkedAccounts\r\n      const params = {\r\n        'tableName': this.value,\r\n        'username': username,\r\n        'startDate': startDate,\r\n        'endDate': endDate,\r\n        'timeScale': timeScale,\r\n        'selectedAccounts': selectedAccounts\r\n      }\r\n      axios.post('http://127.0.0.1:8000/transaction_history', params)\r\n        .then(response => {\r\n          this.accounts = response.data.accounts\r\n          console.log(this.checkedAccounts)\r\n          if (params.selectedAccounts.length === 0 && this.checkAll === true) {\r\n            this.checkedAccounts = this.accounts\r\n          }\r\n          this.dealData = []\r\n          response.data.deal.forEach(item => {\r\n            const [date, amount1, amount2, amount3] = item\r\n            console.log(item)\r\n            this.dealData.push({\r\n              'date': date,\r\n              'amount1': amount1,\r\n              'amount2': amount2,\r\n              'amount3': amount3\r\n            })\r\n\r\n            // 更新图表\n          })\r\n          // 进行排序\r\n          this.dealData.sort((a, b) => {\r\n            return Number(a.date) - Number(b.date)\r\n          })\r\n          this.updateChart()\r\n        })\r\n        .catch(error => {\r\n          console.error('error:' + error)\r\n          alert(error)\r\n        })\r\n        .finally(() => {\r\n          // 模拟请求时间\r\n          setTimeout(() => {\r\n            this.listLoading = false\r\n          }, 1500)\r\n        })\n    },\r\n    // 格式化日期时间\r\n    formatDateTime(date, format) {\r\n      const o = {\r\n        'M+': date.getMonth() + 1, // 月份\r\n        'd+': date.getDate(), // 日\r\n        'H+': date.getHours(), // 小时\r\n        'm+': date.getMinutes(), // 分\r\n        's+': date.getSeconds() // 秒\r\n      }\r\n      if (/(y+)/.test(format)) {\r\n        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))\r\n      }\r\n      for (const k in o) {\r\n        if (new RegExp('(' + k + ')').test(format)) {\r\n          format = format.replace(\r\n            RegExp.$1,\r\n            RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)\r\n          )\r\n        }\r\n      }\r\n      return format\r\n    },\r\n\r\n    formattedDateTime(input) {\r\n      // 确保输入是一个字符串\r\n      const dateString = input.toString()\r\n\r\n      // 提取各个部分\r\n      const year = dateString.substring(0, 4)\r\n      const month = dateString.length >= 6 ? dateString.substring(4, 6) : ''\r\n      const day = dateString.length >= 8 ? dateString.substring(6, 8) : ''\r\n      const hour = dateString.length === 10 ? dateString.substring(8, 10) : ''\r\n\r\n      // 根据输入的长度格式化日期\r\n      if (dateString.length === 4) {\r\n        return `${year}`\r\n      } else if (dateString.length === 6) {\r\n        return `${year}-${month}`\r\n      } else if (dateString.length === 8) {\r\n        return `${year}-${month}-${day}`\r\n      } else if (dateString.length === 10) {\r\n        return `${year}-${month}-${day} ${hour}`\r\n      } else {\r\n        throw new Error('Invalid date format')\r\n      }\r\n    },\r\n    // 更新折线图\r\n    updateChart() {\r\n      this.chart.clear()\r\n      let xData = []\r\n      let yData1 = []\r\n      let yData2 = []\r\n      let yData3 = []\r\n      xData = this.dealData.map(item => item.date)\r\n      yData1 = this.dealData.map(item => item.amount1)\r\n      yData2 = this.dealData.map(item => item.amount2)\r\n      yData3 = this.dealData.map(item => item.amount3)\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          formatter: (params) => {\r\n            const data = params[0]\r\n            return `日期: ${data.axisValue}<br>金额: ￥${data.data}`\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['交易总额', '收入金额', '支出金额'], // 图例名称\r\n          right: '10%', // 位置调整\r\n          top: '5%' // 位置调整\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xData\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '交易金额',\r\n          axisLabel: {\r\n            formatter: (value) => `￥${value}`\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '交易总额',\r\n            type: 'line',\r\n            data: yData1,\r\n            smooth: true,\r\n            itemStyle: {\r\n              color: 'black' // 第一组的颜色\r\n            }\r\n          },\r\n          {\r\n            name: '收入金额',\r\n            type: 'line',\r\n            data: yData2,\r\n            smooth: true,\r\n            itemStyle: {\r\n              color: 'red' // 第二组的颜色\r\n            }\r\n          },\r\n          {\r\n            name: '支出金额',\r\n            type: 'line',\r\n            data: yData3,\r\n            smooth: true,\r\n            itemStyle: {\r\n              color: 'green' // 第三组的颜色\r\n            }\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.chart.setOption(option)\r\n    },\r\n    handleCheckAllChange(val) { // 处理复选框\r\n      this.checkedAccounts = val ? this.accounts : []\r\n      this.isIndeterminate = false\r\n      this.fetchChartData() // 同步查询数据\r\n    },\r\n    handleCheckedCitiesChange(value) {\r\n      this.checkedAccounts = value // 将选中的城市保存到 checkedCities\r\n      const checkedCount = value.length\r\n      this.checkAll = checkedCount === this.accounts.length\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.accounts.length\r\n      // console.log(\"this.checkedCities:\" + this.checkedCities)\r\n      this.fetchChartData() // 同步查询数据\r\n    },\r\n    handleSearch() {\r\n      console.log('1')\r\n      // 发送交易数据到后端\r\n      axios.get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.query-form{\r\n    margin-top: 20px;\r\n}\r\n\r\n.bank-account {\r\n  margin-left: 15px;\r\n  display: flex;\r\n  align-items: center; /* 垂直居中对齐 */\r\n}\r\n</style>\n"]}]}