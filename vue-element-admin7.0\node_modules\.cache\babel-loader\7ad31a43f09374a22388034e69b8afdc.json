{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\table.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\table.js", "mtime": 1747748935262}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Layout", "tableRouter", "path", "component", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard", "require"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/router/modules/table.js"], "sourcesContent": ["/** When your routing table is too long, you can split it into small modules **/\n\nimport Layout from '@/layout'\n\nconst tableRouter = {\n  path: '/table',\n  component: Layout,\n  redirect: '/table/complex-table',\n  name: 'Table',\n  meta: {\n    title: '交易数据展示',\n    icon: 'table'\n  },\n  children: [\n    // {\n    //   path: 'dynamic-table',\n    //   component: () => import('@/views/table/dynamic-table/index'),\n    //   name: 'DynamicTable',\n    //   meta: { title: 'Dynamic Table' }\n    // },\n    // {\n    //   path: 'drag-table',\n    //   component: () => import('@/views/table/drag-table'),\n    //   name: 'DragTable',\n    //   meta: { title: 'Drag Table' }\n    // },\n    // {\n    //   path: 'inline-edit-table',\n    //   component: () => import('@/views/table/inline-edit-table'),\n    //   name: 'InlineEditTable',\n    //   meta: { title: 'Inline Edit' }\n    // },\n    {\n      path: 'complex-table',\n      component: () => import('@/views/table/complex-table'),\n      name: 'ComplexTable',\n      meta: { title: '交易数据展示' }\n    }\n    // {\n    //   path: 'search-table',\n    //   component: () => import('@/views/table/search-table'),\n    //   name: 'SearchTable',\n    //   meta: { title: '查询表'}\n    // }\n  ]\n}\nexport default tableRouter\n"], "mappings": ";;;;AAAA;;AAEA,OAAOA,MAAM,MAAM,UAAU;AAE7B,IAAMC,WAAW,GAAG;EAClBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,sBAAsB;EAChCC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE;IACJC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACEP,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDT,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ,CAAC;AACD,eAAeN,WAAW", "ignoreList": []}]}