{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue", "mtime": 1749177859018}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["RelationGraph", "axios", "name", "components", "data", "options", "value", "minAmount", "date<PERSON><PERSON><PERSON>", "username", "nodes", "lines", "isShow", "isShowCodePanel", "graphOptions", "debug", "defaultNodeColor", "defaultNodeBorderWidth", "defaultNodeBorderColor", "defaultLineColor", "defaultNodeFontColor", "defaultLineFontColor", "hideNodeContentByZoom", "created", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "w", "_context", "n", "showGraph", "a", "mounted", "handleSearch", "methods", "handleSelectChange", "console", "log", "fetchData", "_this2", "_callee2", "payload", "response", "_t", "_context2", "tableName", "Number", "p", "post", "v", "state", "error", "Error", "concat", "_this3", "_callee3", "__graph_json_data", "_t2", "_context3", "length", "$message", "warning", "rootId", "$refs", "graphRef", "setJsonData", "graphInstance", "setOptions", "layout", "onNodeClick", "nodeObject", "$event", "text", "onLineClick", "lineObject", "linkObject", "_this4", "get", "then", "all_tables", "map", "item", "label", "catch"], "sources": ["src/components/Charts/KnowledgeGraph.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-date-picker\r\n      v-model=\"dateRange\"\r\n      type=\"datetimerange\"\r\n      range-separator=\"至\"\r\n      start-placeholder=\"起始日期时间\"\r\n      end-placeholder=\"结束日期时间\"\r\n      format=\"yyyy-MM-dd HH:mm:ss\"\r\n      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n      style=\"width: 350px;margin-top: 15px;margin-right: 15px;margin-left: 15px;\"\r\n    />\r\n\r\n    <el-input\r\n      v-model=\"minAmount\"\r\n      placeholder=\"请输入查询的最低额度\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" @click=\"showGraph\">确认</el-button>\r\n    <!-- <el-button\r\n      type=\"info\"\r\n      style=\"margin-left: 10px\"\r\n      @click=\"toggleIndirectTransactions\"\r\n    >\r\n    展示间接交易\r\n\r\n    </el-button> -->\r\n\r\n    <div v-if=\"isShow\" style=\"height: calc(100vh)\">\r\n      <RelationGraph\r\n        ref=\"graphRef\"\r\n        :options=\"graphOptions\"\r\n        :on-node-click=\"onNodeClick\"\r\n        :on-line-click=\"onLineClick\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RelationGraph from 'relation-graph'\r\nimport axios from 'axios'\r\nexport default {\r\n  name: 'Demo',\r\n  components: { RelationGraph },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      value: '', // 选中的数据表\r\n      minAmount: null,\r\n      dateRange: [],\r\n      username: null,\r\n      nodes: [],\r\n      lines: [],\r\n      isShow: false,\r\n      isShowCodePanel: false,\r\n      graphOptions: {\r\n        debug: true,\r\n        // defaultNodeBorderWidth: 0,\r\n        defaultNodeColor: 'rgba(64, 158, 255, 0.8)', // 主蓝色节点\r\n        defaultNodeBorderWidth: 2,\r\n        defaultNodeBorderColor: 'rgba(64, 158, 255, 1)',\r\n        // 线条也使用蓝色系，但深度不同，避免绿色\r\n        defaultLineColor: 'rgba(96, 175, 255, 0.6)', // 浅蓝色线条\r\n        // 文字颜色\r\n        defaultNodeFontColor: '#ffffff', // 白色文字\r\n        defaultLineFontColor: '#303133', // 深灰色文字，更易读\r\n        hideNodeContentByZoom: true // 根据缩放比例隐藏节点内容\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.showGraph()\r\n  },\r\n  mounted() {\r\n    this.handleSearch() // 初始化加载表名\r\n  // 如果需默认加载数据，可在此调用 this.showGraph();\r\n  },\r\n  // mounted() {\r\n  //   this.showGraph();\r\n  // },\r\n  methods: {\r\n    // fetchDefaultData(){\r\n    //   alert('default');\r\n    //   axios.get('http://127.0.0.1:8000/all_relation')\r\n    //   .then(response => {\r\n    //     if (response.data.state === 200) {\r\n    //       // 保存 nodes 和 lines 数据\r\n    //       this.nodes = response.data.data.nodes;\r\n    //       this.lines = response.data.data.lines;\r\n    //       console.log('this.nodes', this.nodes);\r\n    //       console.log('this.lines', this.lines);\r\n    //     } else {\r\n    //       console.error('查询失败: ', response.data.state);\r\n    //     }\r\n    //   })\r\n    //   .catch(error => {\r\n    //     console.error('Error:', error);\r\n    //   });\r\n    // },\r\n    // showDefaultGraph(){\r\n    //   fetchDefaultData();\r\n    //   this.isShow = true;\r\n    //   const __graph_json_data = {\r\n    //     rootId: '0',\r\n    //     nodes: this.nodes,\r\n    //     lines: this.lines\r\n    //   };\r\n    //   //用来显示图形，必须存在\r\n    //   this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n    //       graphInstance.render(); // 重新渲染图形\r\n    //   });\r\n    // },\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      this.showGraph() // 选择变化时重新加载图表\r\n    },\r\n    async fetchData() {\r\n      const payload = {\r\n        tableName: this.value || null,\r\n        username: this.username || null,\r\n        minAmount: Number(this.minAmount) || 0,\r\n        dateRange: this.dateRange || []\r\n      }\r\n      console.log('payload是:', payload)\r\n      try {\r\n        const response = await axios.post('http://127.0.0.1:8000/find_relation', payload)\r\n        if (response.data.state === 200) {\r\n          this.nodes = response.data.data.nodes\r\n          this.lines = response.data.data.lines\r\n          console.log('接收到的数据:', { nodes: this.nodes, lines: this.lines })\r\n        } else {\r\n          console.log('测试失败')\r\n          console.error('服务器返回异常:', response.data)\r\n          throw new Error(`查询失败，服务器返回状态码: ${response.data.state}`)\r\n        }\r\n      } catch (error) {\r\n        console.log('这里的问题')\r\n        console.error('请求失败:', error)\r\n        throw error // 抛出错误，让调用者知道请求失败\r\n      }\r\n    },\r\n    async showGraph() {\r\n      try {\r\n        console.log('日期范围参数:', this.dateRange)\r\n        // 等待 fetchData 完成\r\n        this.isShow = true\r\n        await this.fetchData()\r\n        if (this.nodes.length === 0 || this.lines.length === 0) {\r\n          this.$message.warning('没有数据可显示')\r\n          return\r\n        }\r\n        // 确保数据加载完成后，再执行后续逻辑\r\n\r\n        const __graph_json_data = {\r\n          rootId: '0',\r\n          nodes: this.nodes,\r\n          lines: this.lines\r\n        }\r\n\r\n        // 直接调用render方法，无需回调\r\n        this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n          // 可在此回调中添加布局配置（如果需要）\r\n          graphInstance.setOptions({ layout: 'force' })\r\n        })\r\n      } catch (error) {\r\n        // 捕获并处理错误\r\n        console.error('Failed to fetch data or render graph:', error)\r\n        this.isShow = false // 如果失败，可以将 isShow 设置为 false 或其他逻辑\r\n      }\r\n    },\r\n    onNodeClick(nodeObject, $event) {\r\n      this.$message(`人物: ${nodeObject.text}`)\r\n    },\r\n    onLineClick(lineObject, linkObject, $event) {\r\n      this.$message(`交易额: ${lineObject.text}`)\r\n    },\r\n    handleSearch() {\r\n      // 发送交易数据到后端\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then((response) => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map((item) => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch((error) => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;AAwEA,OAAAA,aAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAH,aAAA,EAAAA;EAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MAAA;MACAC,SAAA;MACAC,SAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,MAAA;MACAC,eAAA;MACAC,YAAA;QACAC,KAAA;QACA;QACAC,gBAAA;QAAA;QACAC,sBAAA;QACAC,sBAAA;QACA;QACAC,gBAAA;QAAA;QACA;QACAC,oBAAA;QAAA;QACAC,oBAAA;QAAA;QACAC,qBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;MAAA,OAAAF,YAAA,GAAAG,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OACAP,KAAA,CAAAQ,SAAA;UAAA;YAAA,OAAAF,QAAA,CAAAG,CAAA;QAAA;MAAA,GAAAL,OAAA;IAAA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA;EACA;EACA;EACA;EACA;EACAC,OAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA/B,KAAA;MACAgC,OAAA,CAAAC,GAAA,YAAAjC,KAAA;MACA,KAAA0B,SAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,OAAAhB,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAe,SAAA;QAAA,IAAAC,OAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,OAAAnB,YAAA,GAAAG,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cACAY,OAAA;gBACAI,SAAA,EAAAN,MAAA,CAAAnC,KAAA;gBACAG,QAAA,EAAAgC,MAAA,CAAAhC,QAAA;gBACAF,SAAA,EAAAyC,MAAA,CAAAP,MAAA,CAAAlC,SAAA;gBACAC,SAAA,EAAAiC,MAAA,CAAAjC,SAAA;cACA;cACA8B,OAAA,CAAAC,GAAA,cAAAI,OAAA;cAAAG,SAAA,CAAAG,CAAA;cAAAH,SAAA,CAAAf,CAAA;cAAA,OAEA9B,KAAA,CAAAiD,IAAA,wCAAAP,OAAA;YAAA;cAAAC,QAAA,GAAAE,SAAA,CAAAK,CAAA;cAAA,MACAP,QAAA,CAAAxC,IAAA,CAAAgD,KAAA;gBAAAN,SAAA,CAAAf,CAAA;gBAAA;cAAA;cACAU,MAAA,CAAA/B,KAAA,GAAAkC,QAAA,CAAAxC,IAAA,CAAAA,IAAA,CAAAM,KAAA;cACA+B,MAAA,CAAA9B,KAAA,GAAAiC,QAAA,CAAAxC,IAAA,CAAAA,IAAA,CAAAO,KAAA;cACA2B,OAAA,CAAAC,GAAA;gBAAA7B,KAAA,EAAA+B,MAAA,CAAA/B,KAAA;gBAAAC,KAAA,EAAA8B,MAAA,CAAA9B;cAAA;cAAAmC,SAAA,CAAAf,CAAA;cAAA;YAAA;cAEAO,OAAA,CAAAC,GAAA;cACAD,OAAA,CAAAe,KAAA,aAAAT,QAAA,CAAAxC,IAAA;cAAA,MACA,IAAAkD,KAAA,oFAAAC,MAAA,CAAAX,QAAA,CAAAxC,IAAA,CAAAgD,KAAA;YAAA;cAAAN,SAAA,CAAAf,CAAA;cAAA;YAAA;cAAAe,SAAA,CAAAG,CAAA;cAAAJ,EAAA,GAAAC,SAAA,CAAAK,CAAA;cAGAb,OAAA,CAAAC,GAAA;cACAD,OAAA,CAAAe,KAAA,UAAAR,EAAA;cAAA,MAAAA,EAAA;YAAA;cAAA,OAAAC,SAAA,CAAAb,CAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IAGA;IACAV,SAAA,WAAAA,UAAA;MAAA,IAAAwB,MAAA;MAAA,OAAA/B,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA8B,SAAA;QAAA,IAAAC,iBAAA,EAAAC,GAAA;QAAA,OAAAjC,YAAA,GAAAG,CAAA,WAAA+B,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,CAAA;YAAA;cAAA6B,SAAA,CAAAX,CAAA;cAEAX,OAAA,CAAAC,GAAA,YAAAiB,MAAA,CAAAhD,SAAA;cACA;cACAgD,MAAA,CAAA5C,MAAA;cAAAgD,SAAA,CAAA7B,CAAA;cAAA,OACAyB,MAAA,CAAAhB,SAAA;YAAA;cAAA,MACAgB,MAAA,CAAA9C,KAAA,CAAAmD,MAAA,UAAAL,MAAA,CAAA7C,KAAA,CAAAkD,MAAA;gBAAAD,SAAA,CAAA7B,CAAA;gBAAA;cAAA;cACAyB,MAAA,CAAAM,QAAA,CAAAC,OAAA;cAAA,OAAAH,SAAA,CAAA3B,CAAA;YAAA;cAGA;cAEAyB,iBAAA;gBACAM,MAAA;gBACAtD,KAAA,EAAA8C,MAAA,CAAA9C,KAAA;gBACAC,KAAA,EAAA6C,MAAA,CAAA7C;cACA,GAEA;cACA6C,MAAA,CAAAS,KAAA,CAAAC,QAAA,CAAAC,WAAA,CAAAT,iBAAA,YAAAU,aAAA;gBACA;gBACAA,aAAA,CAAAC,UAAA;kBAAAC,MAAA;gBAAA;cACA;cAAAV,SAAA,CAAA7B,CAAA;cAAA;YAAA;cAAA6B,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAEA;cACAb,OAAA,CAAAe,KAAA,0CAAAM,GAAA;cACAH,MAAA,CAAA5C,MAAA;YAAA;cAAA,OAAAgD,SAAA,CAAA3B,CAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IAEA;IACAc,WAAA,WAAAA,YAAAC,UAAA,EAAAC,MAAA;MACA,KAAAX,QAAA,kBAAAP,MAAA,CAAAiB,UAAA,CAAAE,IAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,UAAA,EAAAC,UAAA,EAAAJ,MAAA;MACA,KAAAX,QAAA,wBAAAP,MAAA,CAAAqB,UAAA,CAAAF,IAAA;IACA;IACAvC,YAAA,WAAAA,aAAA;MAAA,IAAA2C,MAAA;MACA;MACA7E,KAAA,CACA8E,GAAA,qCACAC,IAAA,WAAApC,QAAA;QACAN,OAAA,CAAAC,GAAA,CAAAK,QAAA,CAAAxC,IAAA;QACA,IAAAA,IAAA,GAAAwC,QAAA,CAAAxC,IAAA,CAAA6E,UAAA;QACAH,MAAA,CAAAzE,OAAA,GAAAD,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YACAC,KAAA,EAAAD,IAAA;YAAA;YACA7E,KAAA,EAAA6E,IAAA;UACA;QAAA;MACA,GACAE,KAAA,WAAAhC,KAAA;QACAyB,MAAA,CAAAhB,QAAA,CAAAT,KAAA,UAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}