{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue", "mtime": 1749178684895}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["RelationGraph", "require", "axios", "name", "components", "data", "options", "value", "minAmount", "date<PERSON><PERSON><PERSON>", "username", "nodes", "lines", "isShow", "isShowCodePanel", "graphOptions", "debug", "defaultNodeColor", "defaultNodeBorderWidth", "defaultNodeBorderColor", "defaultLineColor", "defaultNodeFontColor", "defaultLineFontColor", "hideNodeContentByZoom", "created", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "w", "_context", "n", "showGraph", "a", "mounted", "handleSearch", "methods", "handleSelectChange", "console", "log", "fetchData", "_this2", "_callee2", "payload", "response", "_t", "_context2", "tableName", "Number", "p", "post", "v", "state", "error", "Error", "concat", "_this3", "_callee3", "__graph_json_data", "_t2", "_context3", "length", "$message", "warning", "rootId", "$refs", "graphRef", "setJsonData", "graphInstance", "setOptions", "layout", "onNodeClick", "nodeObject", "$event", "text", "onLineClick", "lineObject", "linkObject", "_this4", "get", "then", "all_tables", "map", "item", "label", "catch"], "sources": ["src/components/Charts/KnowledgeGraph.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-date-picker\r\n      v-model=\"dateRange\"\r\n      type=\"datetimerange\"\r\n      range-separator=\"至\"\r\n      start-placeholder=\"起始日期时间\"\r\n      end-placeholder=\"结束日期时间\"\r\n      format=\"yyyy-MM-dd HH:mm:ss\"\r\n      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n      style=\"width: 350px;margin-top: 15px;margin-right: 15px;margin-left: 15px;\"\r\n    />\r\n\r\n    <el-input\r\n      v-model=\"minAmount\"\r\n      placeholder=\"请输入查询的最低额度\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" @click=\"showGraph\">确认</el-button>\r\n    <!-- <el-button\r\n      type=\"info\"\r\n      style=\"margin-left: 10px\"\r\n      @click=\"toggleIndirectTransactions\"\r\n    >\r\n    展示间接交易\r\n\r\n    </el-button> -->\r\n\r\n    <div v-if=\"isShow\" style=\"height: calc(100vh)\">\r\n      <RelationGraph\r\n        ref=\"graphRef\"\r\n        :options=\"graphOptions\"\r\n        :on-node-click=\"onNodeClick\"\r\n        :on-line-click=\"onLineClick\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 使用UMD版本避免ES模块兼容性问题\r\nconst RelationGraph = require('relation-graph/lib/vue2/relation-graph.umd.js')\r\nimport axios from 'axios'\r\nexport default {\r\n  name: 'Demo',\r\n  components: { RelationGraph },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      value: '', // 选中的数据表\r\n      minAmount: null,\r\n      dateRange: [],\r\n      username: null,\r\n      nodes: [],\r\n      lines: [],\r\n      isShow: false,\r\n      isShowCodePanel: false,\r\n      graphOptions: {\r\n        debug: true,\r\n        // defaultNodeBorderWidth: 0,\r\n        defaultNodeColor: 'rgba(64, 158, 255, 0.8)', // 主蓝色节点\r\n        defaultNodeBorderWidth: 2,\r\n        defaultNodeBorderColor: 'rgba(64, 158, 255, 1)',\r\n        // 线条也使用蓝色系，但深度不同，避免绿色\r\n        defaultLineColor: 'rgba(96, 175, 255, 0.6)', // 浅蓝色线条\r\n        // 文字颜色\r\n        defaultNodeFontColor: '#ffffff', // 白色文字\r\n        defaultLineFontColor: '#303133', // 深灰色文字，更易读\r\n        hideNodeContentByZoom: true // 根据缩放比例隐藏节点内容\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.showGraph()\r\n  },\r\n  mounted() {\r\n    this.handleSearch() // 初始化加载表名\r\n  // 如果需默认加载数据，可在此调用 this.showGraph();\r\n  },\r\n  // mounted() {\r\n  //   this.showGraph();\r\n  // },\r\n  methods: {\r\n    // fetchDefaultData(){\r\n    //   alert('default');\r\n    //   axios.get('http://127.0.0.1:8000/all_relation')\r\n    //   .then(response => {\r\n    //     if (response.data.state === 200) {\r\n    //       // 保存 nodes 和 lines 数据\r\n    //       this.nodes = response.data.data.nodes;\r\n    //       this.lines = response.data.data.lines;\r\n    //       console.log('this.nodes', this.nodes);\r\n    //       console.log('this.lines', this.lines);\r\n    //     } else {\r\n    //       console.error('查询失败: ', response.data.state);\r\n    //     }\r\n    //   })\r\n    //   .catch(error => {\r\n    //     console.error('Error:', error);\r\n    //   });\r\n    // },\r\n    // showDefaultGraph(){\r\n    //   fetchDefaultData();\r\n    //   this.isShow = true;\r\n    //   const __graph_json_data = {\r\n    //     rootId: '0',\r\n    //     nodes: this.nodes,\r\n    //     lines: this.lines\r\n    //   };\r\n    //   //用来显示图形，必须存在\r\n    //   this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n    //       graphInstance.render(); // 重新渲染图形\r\n    //   });\r\n    // },\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      this.showGraph() // 选择变化时重新加载图表\r\n    },\r\n    async fetchData() {\r\n      const payload = {\r\n        tableName: this.value || null,\r\n        username: this.username || null,\r\n        minAmount: Number(this.minAmount) || 0,\r\n        dateRange: this.dateRange || []\r\n      }\r\n      console.log('payload是:', payload)\r\n      try {\r\n        const response = await axios.post('http://127.0.0.1:8000/find_relation', payload)\r\n        if (response.data.state === 200) {\r\n          this.nodes = response.data.data.nodes\r\n          this.lines = response.data.data.lines\r\n          console.log('接收到的数据:', { nodes: this.nodes, lines: this.lines })\r\n        } else {\r\n          console.log('测试失败')\r\n          console.error('服务器返回异常:', response.data)\r\n          throw new Error(`查询失败，服务器返回状态码: ${response.data.state}`)\r\n        }\r\n      } catch (error) {\r\n        console.log('这里的问题')\r\n        console.error('请求失败:', error)\r\n        throw error // 抛出错误，让调用者知道请求失败\r\n      }\r\n    },\r\n    async showGraph() {\r\n      try {\r\n        console.log('日期范围参数:', this.dateRange)\r\n        // 等待 fetchData 完成\r\n        this.isShow = true\r\n        await this.fetchData()\r\n        if (this.nodes.length === 0 || this.lines.length === 0) {\r\n          this.$message.warning('没有数据可显示')\r\n          return\r\n        }\r\n        // 确保数据加载完成后，再执行后续逻辑\r\n\r\n        const __graph_json_data = {\r\n          rootId: '0',\r\n          nodes: this.nodes,\r\n          lines: this.lines\r\n        }\r\n\r\n        // 直接调用render方法，无需回调\r\n        this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n          // 可在此回调中添加布局配置（如果需要）\r\n          graphInstance.setOptions({ layout: 'force' })\r\n        })\r\n      } catch (error) {\r\n        // 捕获并处理错误\r\n        console.error('Failed to fetch data or render graph:', error)\r\n        this.isShow = false // 如果失败，可以将 isShow 设置为 false 或其他逻辑\r\n      }\r\n    },\r\n    onNodeClick(nodeObject, $event) {\r\n      this.$message(`人物: ${nodeObject.text}`)\r\n    },\r\n    onLineClick(lineObject, linkObject, $event) {\r\n      this.$message(`交易额: ${lineObject.text}`)\r\n    },\r\n    handleSearch() {\r\n      // 发送交易数据到后端\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then((response) => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map((item) => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch((error) => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;AAwEA;AACA,IAAAA,aAAA,GAAAC,OAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAJ,aAAA,EAAAA;EAAA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MAAA;MACAC,SAAA;MACAC,SAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,MAAA;MACAC,eAAA;MACAC,YAAA;QACAC,KAAA;QACA;QACAC,gBAAA;QAAA;QACAC,sBAAA;QACAC,sBAAA;QACA;QACAC,gBAAA;QAAA;QACA;QACAC,oBAAA;QAAA;QACAC,oBAAA;QAAA;QACAC,qBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;MAAA,OAAAF,YAAA,GAAAG,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OACAP,KAAA,CAAAQ,SAAA;UAAA;YAAA,OAAAF,QAAA,CAAAG,CAAA;QAAA;MAAA,GAAAL,OAAA;IAAA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA;EACA;EACA;EACA;EACA;EACAC,OAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA/B,KAAA;MACAgC,OAAA,CAAAC,GAAA,YAAAjC,KAAA;MACA,KAAA0B,SAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,OAAAhB,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAe,SAAA;QAAA,IAAAC,OAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,OAAAnB,YAAA,GAAAG,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cACAY,OAAA;gBACAI,SAAA,EAAAN,MAAA,CAAAnC,KAAA;gBACAG,QAAA,EAAAgC,MAAA,CAAAhC,QAAA;gBACAF,SAAA,EAAAyC,MAAA,CAAAP,MAAA,CAAAlC,SAAA;gBACAC,SAAA,EAAAiC,MAAA,CAAAjC,SAAA;cACA;cACA8B,OAAA,CAAAC,GAAA,cAAAI,OAAA;cAAAG,SAAA,CAAAG,CAAA;cAAAH,SAAA,CAAAf,CAAA;cAAA,OAEA9B,KAAA,CAAAiD,IAAA,wCAAAP,OAAA;YAAA;cAAAC,QAAA,GAAAE,SAAA,CAAAK,CAAA;cAAA,MACAP,QAAA,CAAAxC,IAAA,CAAAgD,KAAA;gBAAAN,SAAA,CAAAf,CAAA;gBAAA;cAAA;cACAU,MAAA,CAAA/B,KAAA,GAAAkC,QAAA,CAAAxC,IAAA,CAAAA,IAAA,CAAAM,KAAA;cACA+B,MAAA,CAAA9B,KAAA,GAAAiC,QAAA,CAAAxC,IAAA,CAAAA,IAAA,CAAAO,KAAA;cACA2B,OAAA,CAAAC,GAAA;gBAAA7B,KAAA,EAAA+B,MAAA,CAAA/B,KAAA;gBAAAC,KAAA,EAAA8B,MAAA,CAAA9B;cAAA;cAAAmC,SAAA,CAAAf,CAAA;cAAA;YAAA;cAEAO,OAAA,CAAAC,GAAA;cACAD,OAAA,CAAAe,KAAA,aAAAT,QAAA,CAAAxC,IAAA;cAAA,MACA,IAAAkD,KAAA,oFAAAC,MAAA,CAAAX,QAAA,CAAAxC,IAAA,CAAAgD,KAAA;YAAA;cAAAN,SAAA,CAAAf,CAAA;cAAA;YAAA;cAAAe,SAAA,CAAAG,CAAA;cAAAJ,EAAA,GAAAC,SAAA,CAAAK,CAAA;cAGAb,OAAA,CAAAC,GAAA;cACAD,OAAA,CAAAe,KAAA,UAAAR,EAAA;cAAA,MAAAA,EAAA;YAAA;cAAA,OAAAC,SAAA,CAAAb,CAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IAGA;IACAV,SAAA,WAAAA,UAAA;MAAA,IAAAwB,MAAA;MAAA,OAAA/B,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA8B,SAAA;QAAA,IAAAC,iBAAA,EAAAC,GAAA;QAAA,OAAAjC,YAAA,GAAAG,CAAA,WAAA+B,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,CAAA;YAAA;cAAA6B,SAAA,CAAAX,CAAA;cAEAX,OAAA,CAAAC,GAAA,YAAAiB,MAAA,CAAAhD,SAAA;cACA;cACAgD,MAAA,CAAA5C,MAAA;cAAAgD,SAAA,CAAA7B,CAAA;cAAA,OACAyB,MAAA,CAAAhB,SAAA;YAAA;cAAA,MACAgB,MAAA,CAAA9C,KAAA,CAAAmD,MAAA,UAAAL,MAAA,CAAA7C,KAAA,CAAAkD,MAAA;gBAAAD,SAAA,CAAA7B,CAAA;gBAAA;cAAA;cACAyB,MAAA,CAAAM,QAAA,CAAAC,OAAA;cAAA,OAAAH,SAAA,CAAA3B,CAAA;YAAA;cAGA;cAEAyB,iBAAA;gBACAM,MAAA;gBACAtD,KAAA,EAAA8C,MAAA,CAAA9C,KAAA;gBACAC,KAAA,EAAA6C,MAAA,CAAA7C;cACA,GAEA;cACA6C,MAAA,CAAAS,KAAA,CAAAC,QAAA,CAAAC,WAAA,CAAAT,iBAAA,YAAAU,aAAA;gBACA;gBACAA,aAAA,CAAAC,UAAA;kBAAAC,MAAA;gBAAA;cACA;cAAAV,SAAA,CAAA7B,CAAA;cAAA;YAAA;cAAA6B,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAEA;cACAb,OAAA,CAAAe,KAAA,0CAAAM,GAAA;cACAH,MAAA,CAAA5C,MAAA;YAAA;cAAA,OAAAgD,SAAA,CAAA3B,CAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IAEA;IACAc,WAAA,WAAAA,YAAAC,UAAA,EAAAC,MAAA;MACA,KAAAX,QAAA,kBAAAP,MAAA,CAAAiB,UAAA,CAAAE,IAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,UAAA,EAAAC,UAAA,EAAAJ,MAAA;MACA,KAAAX,QAAA,wBAAAP,MAAA,CAAAqB,UAAA,CAAAF,IAAA;IACA;IACAvC,YAAA,WAAAA,aAAA;MAAA,IAAA2C,MAAA;MACA;MACA7E,KAAA,CACA8E,GAAA,qCACAC,IAAA,WAAApC,QAAA;QACAN,OAAA,CAAAC,GAAA,CAAAK,QAAA,CAAAxC,IAAA;QACA,IAAAA,IAAA,GAAAwC,QAAA,CAAAxC,IAAA,CAAA6E,UAAA;QACAH,MAAA,CAAAzE,OAAA,GAAAD,IAAA,CAAA8E,GAAA,WAAAC,IAAA;UAAA;YACAC,KAAA,EAAAD,IAAA;YAAA;YACA7E,KAAA,EAAA6E,IAAA;UACA;QAAA;MACA,GACAE,KAAA,WAAAhC,KAAA;QACAyB,MAAA,CAAAhB,QAAA,CAAAT,KAAA,UAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}