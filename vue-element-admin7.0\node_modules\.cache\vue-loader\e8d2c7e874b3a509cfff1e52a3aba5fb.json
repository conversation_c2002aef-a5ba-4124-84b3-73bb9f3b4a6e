{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\role.vue?vue&type=style&index=0&id=37039a8c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\role.vue", "mtime": 1732096978000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hcHAtY29udGFpbmVyIHsKICAucm9sZXMtdGFibGUgewogICAgbWFyZ2luLXRvcDogMzBweDsKICB9CiAgLnBlcm1pc3Npb24tdHJlZSB7CiAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwogIH0KfQo="}, {"version": 3, "sources": ["role.vue"], "names": [], "mappings": ";AAqQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "role.vue", "sourceRoot": "src/views/permission", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-button type=\"primary\" @click=\"handleAddRole\">New Role</el-button>\n\n    <el-table :data=\"rolesList\" style=\"width: 100%;margin-top:30px;\" border>\n      <el-table-column align=\"center\" label=\"Role Key\" width=\"220\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.key }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"center\" label=\"Role Name\" width=\"220\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.name }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"header-center\" label=\"Description\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.description }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"center\" label=\"Operations\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"primary\" size=\"small\" @click=\"handleEdit(scope)\">Edit</el-button>\n          <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope)\">Delete</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <el-dialog :visible.sync=\"dialogVisible\" :title=\"dialogType==='edit'?'Edit Role':'New Role'\">\n      <el-form :model=\"role\" label-width=\"80px\" label-position=\"left\">\n        <el-form-item label=\"Name\">\n          <el-input v-model=\"role.name\" placeholder=\"Role Name\" />\n        </el-form-item>\n        <el-form-item label=\"Desc\">\n          <el-input\n            v-model=\"role.description\"\n            :autosize=\"{ minRows: 2, maxRows: 4}\"\n            type=\"textarea\"\n            placeholder=\"Role Description\"\n          />\n        </el-form-item>\n        <el-form-item label=\"Menus\">\n          <el-tree\n            ref=\"tree\"\n            :check-strictly=\"checkStrictly\"\n            :data=\"routesData\"\n            :props=\"defaultProps\"\n            show-checkbox\n            node-key=\"path\"\n            class=\"permission-tree\"\n          />\n        </el-form-item>\n      </el-form>\n      <div style=\"text-align:right;\">\n        <el-button type=\"danger\" @click=\"dialogVisible=false\">Cancel</el-button>\n        <el-button type=\"primary\" @click=\"confirmRole\">Confirm</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport path from 'path'\nimport { deepClone } from '@/utils'\nimport { getRoutes, getRoles, addRole, deleteRole, updateRole } from '@/api/role'\n\nconst defaultRole = {\n  key: '',\n  name: '',\n  description: '',\n  routes: []\n}\n\nexport default {\n  data() {\n    return {\n      role: Object.assign({}, defaultRole),\n      routes: [],\n      rolesList: [],\n      dialogVisible: false,\n      dialogType: 'new',\n      checkStrictly: false,\n      defaultProps: {\n        children: 'children',\n        label: 'title'\n      }\n    }\n  },\n  computed: {\n    routesData() {\n      return this.routes\n    }\n  },\n  created() {\n    // Mock: get all routes and roles list from server\n    this.getRoutes()\n    this.getRoles()\n  },\n  methods: {\n    async getRoutes() {\n      const res = await getRoutes()\n      this.serviceRoutes = res.data\n      this.routes = this.generateRoutes(res.data)\n    },\n    async getRoles() {\n      const res = await getRoles()\n      this.rolesList = res.data\n    },\n\n    // Reshape the routes structure so that it looks the same as the sidebar\n    generateRoutes(routes, basePath = '/') {\n      const res = []\n\n      for (let route of routes) {\n        // skip some route\n        if (route.hidden) { continue }\n\n        const onlyOneShowingChild = this.onlyOneShowingChild(route.children, route)\n\n        if (route.children && onlyOneShowingChild && !route.alwaysShow) {\n          route = onlyOneShowingChild\n        }\n\n        const data = {\n          path: path.resolve(basePath, route.path),\n          title: route.meta && route.meta.title\n\n        }\n\n        // recursive child routes\n        if (route.children) {\n          data.children = this.generateRoutes(route.children, data.path)\n        }\n        res.push(data)\n      }\n      return res\n    },\n    generateArr(routes) {\n      let data = []\n      routes.forEach(route => {\n        data.push(route)\n        if (route.children) {\n          const temp = this.generateArr(route.children)\n          if (temp.length > 0) {\n            data = [...data, ...temp]\n          }\n        }\n      })\n      return data\n    },\n    handleAddRole() {\n      this.role = Object.assign({}, defaultRole)\n      if (this.$refs.tree) {\n        this.$refs.tree.setCheckedNodes([])\n      }\n      this.dialogType = 'new'\n      this.dialogVisible = true\n    },\n    handleEdit(scope) {\n      this.dialogType = 'edit'\n      this.dialogVisible = true\n      this.checkStrictly = true\n      this.role = deepClone(scope.row)\n      this.$nextTick(() => {\n        const routes = this.generateRoutes(this.role.routes)\n        this.$refs.tree.setCheckedNodes(this.generateArr(routes))\n        // set checked state of a node not affects its father and child nodes\n        this.checkStrictly = false\n      })\n    },\n    handleDelete({ $index, row }) {\n      this.$confirm('Confirm to remove the role?', 'Warning', {\n        confirmButtonText: 'Confirm',\n        cancelButtonText: 'Cancel',\n        type: 'warning'\n      })\n        .then(async() => {\n          await deleteRole(row.key)\n          this.rolesList.splice($index, 1)\n          this.$message({\n            type: 'success',\n            message: 'Delete succed!'\n          })\n        })\n        .catch(error => { console.log(error) })\n    },\n    generateTree(routes, basePath = '/', checkedKeys) {\n      const res = []\n\n      for (const route of routes) {\n        const routePath = path.resolve(basePath, route.path)\n\n        // recursive child routes\n        if (route.children) {\n          route.children = this.generateTree(route.children, routePath, checkedKeys)\n        }\n\n        if (checkedKeys.includes(routePath) || (route.children && route.children.length >= 1)) {\n          res.push(route)\n        }\n      }\n      return res\n    },\n    async confirmRole() {\n      const isEdit = this.dialogType === 'edit'\n\n      const checkedKeys = this.$refs.tree.getCheckedKeys()\n      this.role.routes = this.generateTree(deepClone(this.serviceRoutes), '/', checkedKeys)\n\n      if (isEdit) {\n        await updateRole(this.role.key, this.role)\n        for (let index = 0; index < this.rolesList.length; index++) {\n          if (this.rolesList[index].key === this.role.key) {\n            this.rolesList.splice(index, 1, Object.assign({}, this.role))\n            break\n          }\n        }\n      } else {\n        const { data } = await addRole(this.role)\n        this.role.key = data.key\n        this.rolesList.push(this.role)\n      }\n\n      const { description, key, name } = this.role\n      this.dialogVisible = false\n      this.$notify({\n        title: 'Success',\n        dangerouslyUseHTMLString: true,\n        message: `\n            <div>Role Key: ${key}</div>\n            <div>Role Name: ${name}</div>\n            <div>Description: ${description}</div>\n          `,\n        type: 'success'\n      })\n    },\n    // reference: src/view/layout/components/Sidebar/SidebarItem.vue\n    onlyOneShowingChild(children = [], parent) {\n      let onlyOneChild = null\n      const showingChildren = children.filter(item => !item.hidden)\n\n      // When there is only one child route, the child route is displayed by default\n      if (showingChildren.length === 1) {\n        onlyOneChild = showingChildren[0]\n        onlyOneChild.path = path.resolve(parent.path, onlyOneChild.path)\n        return onlyOneChild\n      }\n\n      // Show parent if there are no child route to display\n      if (showingChildren.length === 0) {\n        onlyOneChild = { ... parent, path: '', noShowingChildren: true }\n        return onlyOneChild\n      }\n\n      return false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .roles-table {\n    margin-top: 30px;\n  }\n  .permission-tree {\n    margin-bottom: 30px;\n  }\n}\n</style>\n"]}]}