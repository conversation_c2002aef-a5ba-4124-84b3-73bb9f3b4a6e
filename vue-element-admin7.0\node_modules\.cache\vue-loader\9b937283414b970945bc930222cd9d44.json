{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\HeaderSearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\HeaderSearch\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeaderSearch", "sourcesContent": ["<template>\n  <div :class=\"{'show':show}\" class=\"header-search\">\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\n    <el-select\n      ref=\"headerSearchSelect\"\n      v-model=\"search\"\n      :remote-method=\"querySearch\"\n      filterable\n      default-first-option\n      remote\n      placeholder=\"Search\"\n      class=\"header-search-select\"\n      @change=\"change\"\n    >\n      <el-option v-for=\"item in options\" :key=\"item.path\" :value=\"item\" :label=\"item.title.join(' > ')\" />\n    </el-select>\n  </div>\n</template>\n\n<script>\n// fuse is a lightweight fuzzy-search module\n// make search results more in line with expectations\nimport Fuse from 'fuse.js'\nimport path from 'path'\n\nexport default {\n  name: 'HeaderSearch',\n  data() {\n    return {\n      search: '',\n      options: [],\n      searchPool: [],\n      show: false,\n      fuse: undefined\n    }\n  },\n  computed: {\n    routes() {\n      return this.$store.getters.permission_routes\n    }\n  },\n  watch: {\n    routes() {\n      this.searchPool = this.generateRoutes(this.routes)\n    },\n    searchPool(list) {\n      this.initFuse(list)\n    },\n    show(value) {\n      if (value) {\n        document.body.addEventListener('click', this.close)\n      } else {\n        document.body.removeEventListener('click', this.close)\n      }\n    }\n  },\n  mounted() {\n    this.searchPool = this.generateRoutes(this.routes)\n  },\n  methods: {\n    click() {\n      this.show = !this.show\n      if (this.show) {\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\n      }\n    },\n    close() {\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\n      this.options = []\n      this.show = false\n    },\n    change(val) {\n      this.$router.push(val.path)\n      this.search = ''\n      this.options = []\n      this.$nextTick(() => {\n        this.show = false\n      })\n    },\n    initFuse(list) {\n      this.fuse = new Fuse(list, {\n        shouldSort: true,\n        threshold: 0.4,\n        location: 0,\n        distance: 100,\n        maxPatternLength: 32,\n        minMatchCharLength: 1,\n        keys: [{\n          name: 'title',\n          weight: 0.7\n        }, {\n          name: 'path',\n          weight: 0.3\n        }]\n      })\n    },\n    // Filter out the routes that can be displayed in the sidebar\n    // And generate the internationalized title\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\n      let res = []\n\n      for (const router of routes) {\n        // skip hidden router\n        if (router.hidden) { continue }\n\n        const data = {\n          path: path.resolve(basePath, router.path),\n          title: [...prefixTitle]\n        }\n\n        if (router.meta && router.meta.title) {\n          data.title = [...data.title, router.meta.title]\n\n          if (router.redirect !== 'noRedirect') {\n            // only push the routes with title\n            // special case: need to exclude parent router without redirect\n            res.push(data)\n          }\n        }\n\n        // recursive child routes\n        if (router.children) {\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\n          if (tempRoutes.length >= 1) {\n            res = [...res, ...tempRoutes]\n          }\n        }\n      }\n      return res\n    },\n    querySearch(query) {\n      if (query !== '') {\n        this.options = this.fuse.search(query)\n      } else {\n        this.options = []\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.header-search {\n  font-size: 0 !important;\n\n  .search-icon {\n    cursor: pointer;\n    font-size: 18px;\n    vertical-align: middle;\n  }\n\n  .header-search-select {\n    font-size: 18px;\n    transition: width 0.2s;\n    width: 0;\n    overflow: hidden;\n    background: transparent;\n    border-radius: 0;\n    display: inline-block;\n    vertical-align: middle;\n\n    ::v-deep .el-input__inner {\n      border-radius: 0;\n      border: 0;\n      padding-left: 0;\n      padding-right: 0;\n      box-shadow: none !important;\n      border-bottom: 1px solid #d9d9d9;\n      vertical-align: middle;\n    }\n  }\n\n  &.show {\n    .header-search-select {\n      width: 210px;\n      margin-left: 10px;\n    }\n  }\n}\n</style>\n"]}]}