{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\databaseManage\\databaseManage1.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\databaseManage\\databaseManage1.vue", "mtime": 1747749164993}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["databaseManage1.vue"], "names": [], "mappings": ";AAoHA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "databaseManage1.vue", "sourceRoot": "src/views/databaseManage", "sourcesContent": ["<template>\r\n  <div>\r\n    <div style=\"margin-top: 20px;\">\r\n        <el-select\r\n            v-model=\"value\"\r\n            placeholder=\"数据表\"\r\n            no-data-text=\"已经没有数据表了\"\r\n            style=\"margin-left: 20px;\"\r\n            @focus=\"handleSearch\"\r\n            @change=\"handleSelectChange\"\r\n        >\r\n            <el-option\r\n            v-for=\"item in options\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.value\"\r\n/>\r\n        </el-select>\r\n        <el-button\r\n        type=\"danger\"\r\n        style=\"margin-left: 10px;\"\r\n        @click=\"handleDelete\"\r\n        >\r\n        删除\r\n        </el-button>\r\n        <el-button\r\n        type=\"primary\"\r\n        style=\"margin-left: 10px;\"\r\n        @click=\"handleFilter\"\r\n        >\r\n        筛选\r\n        </el-button>\r\n    </div>\r\n\r\n    <div v-if=\"showSelectButton\" style=\"margin-top: 20px; position: relative;\">\r\n        <div v-for=\"(item, index) in inputGroups\" :key=\"index\" style=\"margin-bottom: 10px;\">\r\n            <el-select\r\n                v-model=\"item.attribute\"\r\n                placeholder=\"属性\"\r\n                no-data-text=\"已经没有数据了\"\r\n                style=\"margin-left: 20px;\"\r\n            >\r\n                <el-option\r\n                    v-for=\"option in options1\"\r\n                    :key=\"option.value\"\r\n                    :label=\"option.label\"\r\n                    :value=\"option.value\"\r\n/>\r\n            </el-select>\r\n\r\n            <el-select\r\n                v-model=\"item.logical\"\r\n                placeholder=\"逻辑\"\r\n                no-data-text=\"已经没有数据了\"\r\n                style=\"margin-left: 10px;\"\r\n            >\r\n                <el-option\r\n                    v-for=\"option in options2\"\r\n                    :key=\"option.value\"\r\n                    :label=\"option.label\"\r\n                    :value=\"option.value\"\r\n/>\r\n            </el-select>\r\n\r\n            <el-input\r\n                v-model=\"item.content\"\r\n                placeholder=\"内容\"\r\n                style=\"margin-left: 10px; width: 200px;\"\r\n/>\r\n        </div>\r\n        <div style=\"position: absolute; top: 0px; left: 651px;\">\r\n            <el-button\r\n                type=\"primary\"\r\n                style=\"margin-left: 10px;\"\r\n                @click=\"handleAnd\"\r\n            >\r\n            且\r\n            </el-button>\r\n\r\n            <el-button\r\n                type=\"primary\"\r\n                style=\"margin-left: 10px;\"\r\n                @click=\"handleOr\"\r\n            >\r\n            或\r\n            </el-button>\r\n\r\n            <el-button\r\n                type=\"primary\"\r\n                style=\"margin-left: 10px;\"\r\n                @click=\"handleFulfill\"\r\n            >\r\n            添加完成\r\n            </el-button>\r\n        </div>\r\n    </div>\r\n\r\n    <div v-if=\"showSubmitButton\" style=\"margin-top: 20px; margin-left: 10px;\">\r\n        <el-input\r\n            v-model=\"newTable\"\r\n            placeholder=\"数据表名\"\r\n            style=\"margin-left: 10px; width: 200px;\"\r\n/>\r\n\r\n        <el-button\r\n            type=\"primary\"\r\n            style=\"margin-left: 10px;\"\r\n            @click=\"submit\"\r\n        >\r\n        确认\r\n        </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      newTable: '',\r\n      value: '', // 选中的数据表\r\n      options: [], // 选择框中的选项\r\n      loading: false, // 加载状态\r\n      showSelectButton: false,\r\n      showSubmitButton: false,\r\n      inputGroups: [\r\n        { attribute: '', logical: '', content: '' } // 初始输入组\r\n      ],\r\n      options1: [ // 属性选项\r\n        { label: '交易户名', value: '交易户名' },\r\n        { label: '交易卡号', value: '交易卡号' },\r\n        { label: '交易账号', value: '交易账号' },\r\n        { label: '交易时间', value: '交易时间' },\r\n        { label: '交易金额', value: '交易金额' },\r\n        { label: '交易余额', value: '交易余额' },\r\n        { label: '收付标志', value: '收付标志' },\r\n        { label: '对手账号', value: '对手账号' },\r\n        { label: '现金标志', value: '现金标志' },\r\n        { label: '对手户名', value: '对手户名' },\r\n        { label: '对手身份证号', value: '对手身份证号' },\r\n        { label: '对手开户银行', value: '对手开户银行' },\r\n        { label: '摘要说明', value: '摘要说明' },\r\n        { label: '交易币种', value: '交易币种' },\r\n        { label: '交易网点名称', value: '交易网点名称' },\r\n        { label: '交易发生地', value: '交易发生地' },\r\n        { label: '交易是否成功', value: '交易是否成功' },\r\n        { label: '传票号', value: '传票号' },\r\n        { label: 'IP地址', value: 'IP地址' },\r\n        { label: 'MAC地址', value: 'MAC地址' },\r\n        { label: '对手交易余额', value: '对手交易余额' },\r\n        { label: '交易流水号', value: '交易流水号' },\r\n        { label: '日志号', value: '日志号' },\r\n        { label: '凭证种类', value: '凭证种类' },\r\n        { label: '凭证号', value: '凭证号' },\r\n        { label: '交易柜员号', value: '交易柜员号' },\r\n        { label: '备注', value: '备注' },\r\n        { label: '查询反馈结果原因', value: '查询反馈结果原因' }\r\n      ],\r\n      options2: [ // 逻辑选项\r\n        { label: '大于', value: '大于' },\r\n        { label: '大于等于', value: '大于等于' },\r\n        { label: '小于', value: '小于' },\r\n        { label: '小于等于', value: '小于等于' },\r\n        { label: '等于', value: '等于' },\r\n        { label: '不等于', value: '不等于' },\r\n        { label: '是', value: '是' },\r\n        { label: '非', value: '非' }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      // 发送交易数据到后端\r\n      axios.get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    },\r\n    handleDelete() {\r\n      if (this.value.length > 0) {\r\n        axios.post('http://127.0.0.1:8000/drop_table', { table4drop: this.value })\r\n          .then(response => {\r\n            const is_success = response.data.isSuccess\r\n            if (is_success) {\r\n              this.$message.success('已删除选中的表')\r\n              // 清空选中的值\r\n              this.value = []\r\n            } else {\r\n              this.$message.warning('删除失败，请联系管理员...')\r\n            }\r\n          })\r\n          .catch(error => {\r\n            this.$message.error('上传失败:', error)\r\n          })\r\n      } else {\r\n        this.$message.warning('没有选中的项可删除')\r\n      }\r\n    },\r\n    handleFilter() {\r\n      if (this.value) {\r\n        this.showSelectButton = true\r\n      }\r\n    },\r\n    handleSelectChange() {\r\n      // 当选择框内容变化时隐藏“添加完成”按钮\r\n      this.showSelectButton = false\r\n      this.showSubmitButton = false\r\n    },\r\n    handleAnd() {\r\n      // 处理且按钮的逻辑\r\n      this.inputGroups.push({ attribute: '', logical: '', content: '', connect: '且' })\r\n    },\r\n    handleOr() {\r\n      // 处理或按钮的逻辑\r\n      this.inputGroups.push({ attribute: '', logical: '', content: '', connect: '或' })\r\n    },\r\n    handleFulfill() {\r\n      this.showSubmitButton = true\r\n    },\r\n    submit() {\r\n      const newDictionary = {\r\n        original_form: this.value,\r\n        logical: this.inputGroups,\r\n        new_form: this.newTable\r\n      }\r\n\r\n      axios.post('http://127.0.0.1:8000/logical_filter', newDictionary)\r\n        .then(response => {\r\n          const result = response.data.result\r\n\r\n          if (result === 0) {\r\n            this.$message.success('成功')\r\n          } else {\r\n            this.$message.error('上传失败，错误码为：', result)\r\n          }\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}