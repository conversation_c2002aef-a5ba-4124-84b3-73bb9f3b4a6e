{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\auth-redirect.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\auth-redirect.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc2xpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zZWFyY2guanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0F1dGhSZWRpcmVjdCcsCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBoYXNoID0gd2luZG93LmxvY2F0aW9uLnNlYXJjaC5zbGljZSgxKTsKICAgIGlmICh3aW5kb3cubG9jYWxTdG9yYWdlKSB7CiAgICAgIHdpbmRvdy5sb2NhbFN0b3JhZ2Uuc2V0SXRlbSgneC1hZG1pbi1vYXV0aC1jb2RlJywgaGFzaCk7CiAgICAgIHdpbmRvdy5jbG9zZSgpOwogICAgfQogIH0sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCkgewogICAgcmV0dXJuIGgoKTsgLy8gYXZvaWQgd2FybmluZyBtZXNzYWdlCiAgfQp9Ow=="}, {"version": 3, "names": ["name", "created", "hash", "window", "location", "search", "slice", "localStorage", "setItem", "close", "render", "h"], "sources": ["src/views/login/auth-redirect.vue"], "sourcesContent": ["<script>\nexport default {\n  name: 'AuthRedirect',\n  created() {\n    const hash = window.location.search.slice(1)\n    if (window.localStorage) {\n      window.localStorage.setItem('x-admin-oauth-code', hash)\n      window.close()\n    }\n  },\n  render: function(h) {\n    return h() // avoid warning message\n  }\n}\n</script>\n"], "mappings": ";;;AACA;EACAA,IAAA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,IAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,CAAAC,KAAA;IACA,IAAAH,MAAA,CAAAI,YAAA;MACAJ,MAAA,CAAAI,YAAA,CAAAC,OAAA,uBAAAN,IAAA;MACAC,MAAA,CAAAM,KAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAAC,CAAA;IACA,OAAAA,CAAA;EACA;AACA", "ignoreList": []}]}