{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\logManager\\logManager1.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\logManager\\logManager1.vue", "mtime": 1747749486341}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["logManager1.vue"], "names": [], "mappings": ";AA6GA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "logManager1.vue", "sourceRoot": "src/views/logManager", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <div class=\"filter-container\">\r\n        <!-- <el-input\r\n          v-model=\"time\"\r\n          placeholder=\"时间\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        /> -->\r\n        <el-date-picker\r\n            v-model=\"time\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期时间\"\r\n            end-placeholder=\"结束日期时间\"\r\n            format=\"yyyy-MM-dd HH:mm:ss\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 350px; margin-top: 7px;\"\r\n        />\r\n\r\n        <el-input\r\n          v-model=\"username\"\r\n          placeholder=\"用户名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n\r\n        <el-input\r\n          v-model=\"fileName\"\r\n          placeholder=\"文件名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n        <el-input\r\n          v-model=\"databaseName\"\r\n          placeholder=\"数据库名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n        <el-button\r\n          class=\"filter-item search_submit\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          @click=\"handleFilter\"\r\n        >\r\n          搜索\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :key=\"tableKey\"\r\n        v-loading=\"listLoading\"\r\n        :data=\"list\"\r\n        border\r\n        fit\r\n        highlight-current-row\r\n        style=\"width: 100%\"\r\n        @sort-change=\"sortChange\"\r\n      >\r\n        <el-table-column\r\n          label=\"时间\"\r\n          prop=\"time\"\r\n          width=\"150px\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.time }}</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"用户名\" prop=\"username\" min-width=\"150px\">\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.username }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文件名\" prop=\"fileName\" min-width=\"150px\">\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.fileName }}</span>\r\n            <!-- <span class=\"link-cardNumber\" @click=\"handleUpdate(row)\">{{ row.title }}</span>\r\n            <el-tag>{{ row.type | typeFilter }}</el-tag> -->\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"数据库名\"\r\n          prop=\"databaseName\"\r\n          width=\"160px\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.databaseName }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"listQuery.page\"\r\n        :limit.sync=\"listQuery.pagelimit\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n  </template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport waves from '@/directive/waves' // waves directive\r\n// import { parseTime } from '@/utils'\r\nimport Pagination from '@/components/Pagination' // secondary package based on el-pagination\r\n\r\nexport default {\r\n  name: 'LogManager',\r\n  components: { Pagination },\r\n  directives: { waves },\r\n  data() {\r\n    return {\r\n      list: null,\r\n      rawData: null,\r\n      tableKey: 0,\r\n      total: 0,\r\n      listLoading: true,\r\n      time: [],\r\n      username: '',\r\n      fileName: '',\r\n      databaseName: '',\r\n      listQuery: {\r\n        page: 1,\r\n        pagelimit: 10,\r\n        time: null,\r\n        username: null,\r\n        fileName: null,\r\n        databaseName: null\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.listLoading = true\r\n      axios\r\n        .post('http://127.0.0.1:8000/all_log_history', this.listQuery) // 使用 POST 请求\r\n        .then((response) => {\r\n          this.rawData = response.data.items\r\n          this.total = response.data.total\r\n          this.list = this.rawData.map((item) => {\r\n            return {\r\n              time: item[0] === 'None' ? '空' : item[0],\r\n              username: item[1] === 'None' ? '空' : item[1],\r\n              fileName: item[2] === 'None' ? '空' : item[2],\r\n              databaseName: item[3] === 'None' ? '空' : item[3]\r\n            }\r\n          })\r\n        })\r\n        .catch((error) => {\r\n          console.error('Error fetching the list:', error)\r\n        })\r\n        .finally(() => {\r\n          // 模拟请求时间\r\n          setTimeout(() => {\r\n            this.listLoading = false\r\n          }, 1500)\r\n        })\r\n    },\r\n    handleFilter() {\r\n      console.log('handleFilter called')\r\n      this.listQuery.page = 1\r\n      this.listQuery.time = this.time || null\r\n      this.listQuery.username = this.username || null\r\n      this.listQuery.fileName = this.fileName || null\r\n      this.listQuery.databaseName = this.databaseName\r\n      console.log(this.listQuery)\r\n      this.getList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style>\r\n  .filter-item {\r\n    margin: 0 15px;\r\n  }\r\n  .search_submit {\r\n    position: relative; /* 相对定位，以便伪元素定位 */\r\n    overflow: hidden; /* 隐藏溢出部分 */\r\n    border: none; /* 去掉按钮边框 */\r\n    background-color: #007bffd5; /* 按钮背景颜色 */\r\n    color: white; /* 字体颜色 */\r\n    padding: 10px 20px; /* 按钮内边距 */\r\n    border-radius: 4px; /* 圆角 */\r\n    cursor: pointer; /* 鼠标指针 */\r\n    transition: background-color 0.3s; /* 背景颜色过渡 */\r\n  }\r\n  </style>\r\n"]}]}