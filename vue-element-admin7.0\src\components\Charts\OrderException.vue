<template>
<div class="app-container">
<div class="file-selection-container">
<!-- 文件选择区域 -->
<div class="selection-section">
<div class="section-header">
<h3>选择需要的文件</h3>
</div>

<!-- 文件列表展示 -->
<div class="file-list-container">
<div class="file-table-wrapper">
<el-table
ref="fileTable"
:data="availableFiles"
border
fit
highlight-current-row
style="width: 100%"
height="200"
@selection-change="handleSelectionChange"
>
<el-table-column
type="selection"
width="55"
align="center"
/>
<el-table-column prop="fileName" label="文件名" min-width="250">
<template #default="{row}">
<i class="el-icon-document" />
<span style="margin-left: 8px;">{{ row.fileName }}</span>
</template>
</el-table-column>
<el-table-column prop="uploadDate" label="上传时间" width="180" align="center" />
<el-table-column prop="recordCount" label="记录数" width="120" align="center">
<template #default="{row}">
<span class="record-count">{{ row.recordCount.toLocaleString() }}</span>
</template>
</el-table-column>
<el-table-column label="状态" width="100" align="center">
<template #default="{row}">
<el-tag :type="row.status === 'available' ? 'success' : 'info'" size="small">
{{ row.status === 'available' ? '可用' : '处理中' }}
</el-tag>
</template>
</el-table-column>
</el-table>
</div>
</div>
</div>

<!-- 已选择文件显示 -->
<div v-if="selectedFiles.length > 0" class="selected-files-section">
<div class="selected-header">
<span>已选择 {{ selectedFiles.length }} 个文件</span>
<el-button type="text" @click="clearSelection">清空选择</el-button>
</div>
<div class="selected-files-list">
<el-tag
v-for="file in selectedFiles"
:key="file.id"
closable
style="margin: 4px;"
@close="removeSelectedFile(file)"
>
{{ file.fileName }}
</el-tag>
</div>
</div>

<!-- 操作按钮区域 -->
<div class="action-buttons">
<el-button
type="primary"
icon="el-icon-refresh"
:loading="loadingFiles"
@click="loadAvailableFiles"
>
刷新文件列表
</el-button>
<el-button
type="success"
icon="el-icon-s-data"
:loading="processing"
:disabled="selectedFiles.length === 0"
@click="processSelectedFiles"
>
{{ processing ? '处理中...' : '合并分析数据' }}
</el-button>
<el-button
icon="el-icon-delete"
:disabled="selectedFiles.length === 0"
@click="clearSelection"
>
清空选择
</el-button>
</div>

<!-- 进度显示 -->
<div v-if="processing" class="progress-section">
<el-progress
:percentage="processProgress"
:status="processProgress === 100 ? 'success' : ''"
:stroke-width="8"
/>
<p class="progress-text">{{ progressText }}</p>
</div>
</div>

<el-card class="box-card">
<div slot="header" class="clearfix">
<span>异常物流订单列表</span>
</div>
<div class="scroll-container">
<div ref="scrollContainer" class="custom-scrollbar" @scroll="handleScroll">
<el-table
:data="exceptionList"
border
fit
highlight-current-row
style="width: 100%; height: 100%"
>
<el-table-column prop="orderNo" label="订单号" width="180" align="center" />
<el-table-column prop="specs" label="商品规格" width="180" />
<el-table-column prop="unitPrice" label="单价" align="right" width="110">
<template #default="{row}">
¥{{ row.unitPrice.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="quantity" label="数量" width="80" align="center" />
<el-table-column prop="totalAmount" label="订单金额" align="right" width="130">
<template #default="{row}">
¥{{ row.totalAmount.toFixed(2) }}
</template>
</el-table-column>
<el-table-column prop="payerName" label="支付人" width="120" />
<el-table-column prop="idNumber" label="身份证号" width="180" />
<el-table-column prop="phone" label="联系电话" width="130" />
<el-table-column prop="orderDate" label="下单日期" width="120" />
<el-table-column prop="paymentDate" label="支付日期" width="120" />
<el-table-column prop="logisticsNo" label="物流单号" width="180" />
</el-table>
</div>
</div>
</el-card>
</div>

</template>

<script>
// import axios from 'axios'

export default {
  name: 'OrderException',
  data() {
    return {
      // 文件选择相关
      availableFiles: [
        {
          id: 1,
          fileName: '订单数据_2024Q1.xlsx',
          uploadDate: '2024-01-15 10:30:00',
          recordCount: 1250,
          status: 'available'
        },
        {
          id: 2,
          fileName: '物流信息_2024Q1.xlsx',
          uploadDate: '2024-01-20 14:20:00',
          recordCount: 980,
          status: 'available'
        },
        {
          id: 3,
          fileName: '订单数据_2024Q2.xlsx',
          uploadDate: '2024-04-10 09:15:00',
          recordCount: 1680,
          status: 'available'
        },
        {
          id: 4,
          fileName: '物流信息_2024Q2.xlsx',
          uploadDate: '2024-04-15 16:45:00',
          recordCount: 1420,
          status: 'available'
        },
        {
          id: 5,
          fileName: '订单数据_2024Q3.xlsx',
          uploadDate: '2024-07-08 11:30:00',
          recordCount: 2100,
          status: 'available'
        },
        {
          id: 6,
          fileName: '物流信息_2024Q3.xlsx',
          uploadDate: '2024-07-12 15:20:00',
          recordCount: 1890,
          status: 'available'
        },
        {
          id: 7,
          fileName: '订单数据_2024Q4.xlsx',
          uploadDate: '2024-10-05 09:45:00',
          recordCount: 2350,
          status: 'available'
        },
        {
          id: 8,
          fileName: '物流信息_2024Q4.xlsx',
          uploadDate: '2024-10-08 14:30:00',
          recordCount: 2180,
          status: 'available'
        },
        {
          id: 9,
          fileName: '客户信息_2024年度.xlsx',
          uploadDate: '2024-12-01 10:15:00',
          recordCount: 5600,
          status: 'available'
        },
        {
          id: 10,
          fileName: '供应商数据_2024年度.xlsx',
          uploadDate: '2024-12-02 11:20:00',
          recordCount: 890,
          status: 'available'
        },
        {
          id: 11,
          fileName: '退货记录_2024Q1-Q2.xlsx',
          uploadDate: '2024-06-30 16:45:00',
          recordCount: 320,
          status: 'available'
        },
        {
          id: 12,
          fileName: '退货记录_2024Q3-Q4.xlsx',
          uploadDate: '2024-12-15 09:30:00',
          recordCount: 280,
          status: 'available'
        },
        {
          id: 13,
          fileName: '异常订单_历史数据.xlsx',
          uploadDate: '2024-11-20 13:15:00',
          recordCount: 156,
          status: 'available'
        },
        {
          id: 14,
          fileName: '物流跟踪_详细记录.xlsx',
          uploadDate: '2024-12-10 08:45:00',
          recordCount: 4200,
          status: 'processing'
        },
        {
          id: 15,
          fileName: '订单统计_月度汇总.xlsx',
          uploadDate: '2024-12-18 14:20:00',
          recordCount: 720,
          status: 'available'
        }
      ],
      selectedFiles: [],
      loadingFiles: false,
      processing: false,
      processProgress: 0,
      progressText: '',

      // 异常数据列表
      exceptionList: [
        {
          orderNo: 'DD20240715001',
          category: '电子产品',
          specs: '笔记本电脑/16GB 512GB',
          unitPrice: 8999.00,
          quantity: 1,
          totalAmount: 8999.00,
          payerName: '李四',
          idNumber: '310***********5678',
          phone: '13900139000',
          orderDate: '2024-07-15',
          orderTime: '10:15',
          paymentDate: '2024-07-15',
          paymentTime: '10:20',
          logisticsNo: 'WL987654321'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        },
        {
          orderNo: 'DD20240715002',
          category: '服饰',
          specs: '男士T恤/XL码 黑色',
          unitPrice: 89.90,
          quantity: 3,
          totalAmount: 269.70,
          payerName: '王五',
          idNumber: '320***********1234',
          phone: '13800138000',
          orderDate: '2024-07-14',
          orderTime: '14:30',
          paymentDate: '2024-07-14',
          paymentTime: '14:35',
          logisticsNo: 'WL123456789'
        }
      ],
      scrollContainer: null
    }
  },
  mounted() {
    // 初始化时清空异常数据列表，等待用户选择文件
    this.exceptionList = []
    // 加载可用文件列表
    this.loadAvailableFiles()
  },
  methods: {
    // 加载可用文件列表
    async loadAvailableFiles() {
      this.loadingFiles = true
      try {
        // 这里将来连接后端API获取文件列表
        // const response = await axios.get('http://127.0.0.1:8000/available-files')
        // this.availableFiles = response.data.files || []

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        this.$message.success('文件列表加载完成')
      } catch (error) {
        console.error('加载文件列表失败:', error)
        this.$message.error('加载文件列表失败')
      } finally {
        this.loadingFiles = false
      }
    },

    // 处理文件选择变化
    handleSelectionChange(selection) {
      this.selectedFiles = selection
      console.log('已选择文件:', selection)
    },

    // 移除已选择的文件
    removeSelectedFile(file) {
      const index = this.selectedFiles.findIndex(f => f.id === file.id)
      if (index > -1) {
        this.selectedFiles.splice(index, 1)
      }
      // 同时更新表格选择状态
      this.$nextTick(() => {
        const table = this.$refs.fileTable
        if (table) {
          table.toggleRowSelection(file, false)
        }
      })
    },

    // 清空选择
    clearSelection() {
      this.selectedFiles = []
      // 清空表格选择
      this.$nextTick(() => {
        const table = this.$refs.fileTable
        if (table) {
          table.clearSelection()
        }
      })
      this.$message.info('已清空文件选择')
    },

    // 处理选中的文件
    async processSelectedFiles() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning('请先选择要处理的文件')
        return
      }

      this.processing = true
      this.processProgress = 0
      this.progressText = '开始处理文件...'

      try {
        // 模拟进度更新
        const progressInterval = setInterval(() => {
          if (this.processProgress < 90) {
            this.processProgress += Math.random() * 15
            const currentStep = Math.floor(this.processProgress / 30)
            const steps = ['正在读取文件...', '正在合并数据...', '正在分析异常...']
            this.progressText = steps[currentStep] || '处理中...'
          }
        }, 300)

        // 这里将来连接后端API处理文件
        // const fileIds = this.selectedFiles.map(f => f.id)
        // const response = await axios.post('http://127.0.0.1:8000/process-files', {
        //   fileIds: fileIds
        // })

        // 模拟处理时间
        await new Promise(resolve => setTimeout(resolve, 3000))

        clearInterval(progressInterval)
        this.processProgress = 100
        this.progressText = '数据处理完成！'

        // 模拟生成异常数据
        const mockExceptions = [
          {
            orderNo: 'DD20240715001',
            category: '电子产品',
            specs: '笔记本电脑/16GB 512GB',
            unitPrice: 8999.00,
            quantity: 1,
            totalAmount: 8999.00,
            payerName: '李四',
            idNumber: '310***********5678',
            phone: '13900139000',
            orderDate: '2024-07-15',
            orderTime: '10:15',
            paymentDate: '2024-07-15',
            paymentTime: '10:20',
            logisticsNo: 'WL987654321'
          },
          {
            orderNo: 'DD20240715002',
            category: '服饰',
            specs: '男士T恤/XL码 黑色',
            unitPrice: 89.90,
            quantity: 3,
            totalAmount: 269.70,
            payerName: '王五',
            idNumber: '320***********1234',
            phone: '13800138000',
            orderDate: '2024-07-14',
            orderTime: '14:30',
            paymentDate: '2024-07-14',
            paymentTime: '14:35',
            logisticsNo: 'WL123456789'
          }
        ]

        this.exceptionList = mockExceptions
        this.$message.success(`成功处理 ${this.selectedFiles.length} 个文件，发现 ${this.exceptionList.length} 条异常数据`)
      } catch (error) {
        console.error('处理失败:', error)
        this.processProgress = 0
        this.progressText = ''
        this.$message.error(`处理失败: ${error.message}`)
      } finally {
        this.processing = false
        setTimeout(() => {
          this.processProgress = 0
          this.progressText = ''
        }, 3000)
      }
    },

    handleScroll(event) {
      // 处理滚动事件
      console.log('Scrolling...', event)
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 文件选择容器样式 */
.file-selection-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.selection-section {
  margin-bottom: 20px;
  height:250px;
}

.section-header {
  margin-bottom: 20px;
  height:-10px;
}

.section-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.section-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 文件列表容器 */
.file-list-container {
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.file-table-wrapper {
  position: relative;
  max-height: 400px;
  overflow: auto;
}

/* 自定义表格滚动条样式 */
.file-table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.file-table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.file-table-wrapper::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.file-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}

/* 已选择文件区域 */
.selected-files-section {
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 600;
  color: #409eff;
}

.selected-files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.action-buttons .el-button {
  padding: 12px 20px;
  font-size: 14px;
}

/* 进度显示区域 */
.progress-section {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.progress-text {
  margin: 10px 0 0 0;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

/* 卡片样式 */
.box-card {
  margin-top: 20px;
}

.el-table {
  margin-top: 15px;
}

/* 滚动容器 */
.scroll-container {
  height: 600px;
  position: relative;
}

.custom-scrollbar {
  height: 100%;
  overflow: auto;
  padding-right: 12px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}

/* 表格样式优化 */
.file-list-container .el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.file-list-container .el-table td {
  padding: 12px 0;
}

.file-list-container .el-table .el-icon-document {
  color: #67c23a;
  font-size: 16px;
}

/* 表格行悬停效果 */
.file-list-container .el-table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 记录数样式 */
.file-list-container .el-table .record-count {
  font-weight: 600;
  color: #409eff;
}

/* 状态标签样式调整 */
.file-list-container .el-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
