<template>
<div class="app-container">
<!-- 文件上传区域 -->
<div class="upload-section">
<div class="section-header">
<h3>本地文件上传</h3>
<p class="section-desc">上传Excel文件到系统中</p>
</div>
<el-upload
  ref="upload"
  class="upload-demo"
  drag
  action=""
  :auto-upload="false"
  :on-change="handleFileChange"
  :file-list="uploadFileList"
  accept=".xlsx,.xls"
  multiple
>
  <i class="el-icon-upload"></i>
  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
  <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
</el-upload>
<div class="upload-actions">
  <el-button
    type="primary"
    :loading="uploading"
    :disabled="uploadFileList.length === 0"
    @click="uploadFiles"
  >
    {{ uploading ? '上传中...' : '开始上传' }}
  </el-button>
  <el-button @click="clearUploadFiles">清空文件</el-button>
</div>
</div>

<div class="file-selection-container">
<!-- 文件选择区域 -->
<div class="selection-section">
<div class="section-header">
<h3>选择需要检测的文件</h3>
<p class="section-desc">从数据库中选择要进行异常检测的文件</p>
</div>

<!-- 文件列表展示 -->
<div class="file-list-container">
<div class="file-table-wrapper">
<el-table
ref="fileTable"
:data="availableFiles"
border
fit
highlight-current-row
style="width: 100%"
height="200"
@selection-change="handleSelectionChange"
>
<el-table-column
type="selection"
width="55"
align="center"
/>
<el-table-column prop="fileName" label="文件名" min-width="300">
<template #default="{row}">
<i class="el-icon-document" />
<span style="margin-left: 8px;">{{ row.fileName }}</span>
</template>
</el-table-column>
<el-table-column label="状态" width="100" align="center">
<template>
<el-tag type="success" size="small">可用</el-tag>
</template>
</el-table-column>
</el-table>
</div>
</div>
</div>

<!-- 已选择文件显示 -->
<div v-if="selectedFiles.length > 0" class="selected-files-section">
<div class="selected-header">
<span>已选择 {{ selectedFiles.length }} 个文件</span>
<el-button type="text" @click="clearSelection">清空选择</el-button>
</div>
<div class="selected-files-list">
<el-tag
v-for="file in selectedFiles"
:key="file.id"
closable
style="margin: 4px;"
@close="removeSelectedFile(file)"
>
{{ file.fileName }}
</el-tag>
</div>
</div>

<!-- 操作按钮区域 -->
<div class="action-buttons">
<el-button
type="primary"
icon="el-icon-refresh"
:loading="loadingFiles"
@click="loadAvailableFiles"
>
刷新文件列表
</el-button>
<el-button
type="success"
icon="el-icon-s-data"
:loading="processing"
:disabled="selectedFiles.length === 0"
@click="processSelectedFiles"
>
{{ processing ? '检测中...' : '开始异常检测' }}
</el-button>
<el-button
icon="el-icon-delete"
:disabled="selectedFiles.length === 0"
@click="clearSelection"
>
清空选择
</el-button>
</div>

<!-- 进度显示 -->
<div v-if="processing" class="progress-section">
<el-progress
:percentage="processProgress"
:status="processProgress === 100 ? 'success' : ''"
:stroke-width="8"
/>
<p class="progress-text">{{ progressText }}</p>
</div>
</div>

<!-- 异常检测结果展示 -->
<div v-if="exceptionResults && Object.keys(exceptionResults).length > 0" class="results-container">
<el-card class="box-card">
<div slot="header" class="clearfix">
<span>异常检测结果</span>
<span class="result-summary">共发现 {{ getTotalExceptions() }} 条异常记录</span>
</div>

<!-- 异常类型标签页 -->
<el-tabs v-model="activeTab" type="card">
<el-tab-pane
  v-for="(data, type) in exceptionResults"
  :key="type"
  :label="`${type} (${data.length})`"
  :name="type"
>
<div class="scroll-container">
<div class="custom-scrollbar">
<el-table
:data="data"
border
fit
highlight-current-row
style="width: 100%"
max-height="400"
>
<el-table-column prop="订单号" label="订单号" width="180" align="center" />
<el-table-column prop="支付人姓名" label="支付人姓名" width="120" />
<el-table-column prop="支付人身份证号" label="支付人身份证号" width="200" />
<el-table-column prop="物流单号" label="物流单号" width="180" />
</el-table>
</div>
</div>
</el-tab-pane>
</el-tabs>
</el-card>
</div>

<!-- 空状态提示 -->
<div v-else class="empty-state">
<el-empty description="请选择文件并进行异常检测">
<el-button type="primary" @click="loadAvailableFiles">刷新文件列表</el-button>
</el-empty>
</div>
</div>

</template>

<script>
import axios from 'axios'

export default {
  name: 'OrderException',
  data() {
    return {
      // 文件上传相关
      uploadFileList: [],
      uploading: false,

      // 文件选择相关
      availableFiles: [],
      selectedFiles: [],
      loadingFiles: false,
      processing: false,
      processProgress: 0,
      progressText: '',

      // 异常检测结果
      exceptionResults: {},
      activeTab: ''
    }
  },
  mounted() {
    // 加载可用文件列表
    this.loadAvailableFiles()
  },
  methods: {
    // 文件上传相关方法
    handleFileChange(file, fileList) {
      this.uploadFileList = fileList
    },

    clearUploadFiles() {
      this.uploadFileList = []
      this.$refs.upload.clearFiles()
      this.$message.info('已清空上传文件')
    },

    async uploadFiles() {
      if (this.uploadFileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      this.uploading = true
      try {
        // 这里可以实现真实的文件上传逻辑
        // 目前只是模拟上传过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)
        this.clearUploadFiles()
        // 上传完成后刷新文件列表
        this.loadAvailableFiles()
      } catch (error) {
        console.error('上传失败:', error)
        this.$message.error('文件上传失败')
      } finally {
        this.uploading = false
      }
    },

    // 加载可用文件列表
    async loadAvailableFiles() {
      this.loadingFiles = true
      try {
        const response = await axios.get('http://127.0.0.1:8000/get_all_TrackingNum')
        const paths = response.data.paths || []

        // 将路径转换为文件对象
        this.availableFiles = paths.map((path, index) => {
          const fileName = path.split('\\').pop() || path.split('/').pop()
          return {
            id: index + 1,
            fileName: fileName,
            fullPath: path
          }
        })

        this.$message.success(`加载完成，共找到 ${this.availableFiles.length} 个文件`)
      } catch (error) {
        console.error('加载文件列表失败:', error)
        this.$message.error('加载文件列表失败，请检查后端服务是否正常')
      } finally {
        this.loadingFiles = false
      }
    },

    // 处理文件选择变化
    handleSelectionChange(selection) {
      this.selectedFiles = selection
      console.log('已选择文件:', selection)
    },

    // 移除已选择的文件
    removeSelectedFile(file) {
      const index = this.selectedFiles.findIndex(f => f.id === file.id)
      if (index > -1) {
        this.selectedFiles.splice(index, 1)
      }
      // 同时更新表格选择状态
      this.$nextTick(() => {
        const table = this.$refs.fileTable
        if (table) {
          table.toggleRowSelection(file, false)
        }
      })
    },

    // 清空选择
    clearSelection() {
      this.selectedFiles = []
      // 清空表格选择
      this.$nextTick(() => {
        const table = this.$refs.fileTable
        if (table) {
          table.clearSelection()
        }
      })
      this.$message.info('已清空文件选择')
    },

    // 处理选中的文件
    async processSelectedFiles() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning('请先选择要处理的文件')
        return
      }

      this.processing = true
      this.processProgress = 0
      this.progressText = '开始异常检测...'

      try {
        // 进度更新
        const progressInterval = setInterval(() => {
          if (this.processProgress < 90) {
            this.processProgress += Math.random() * 15
            const currentStep = Math.floor(this.processProgress / 30)
            const steps = ['正在读取文件...', '正在合并数据...', '正在检测异常...']
            this.progressText = steps[currentStep] || '检测中...'
          }
        }, 300)

        // 调用后端API进行异常检测
        const filePaths = this.selectedFiles.map(f => f.fullPath)
        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {
          filenames: filePaths
        })

        clearInterval(progressInterval)
        this.processProgress = 100
        this.progressText = '异常检测完成！'

        // 处理后端返回的异常结果
        this.exceptionResults = response.data || {}

        // 设置默认激活的标签页
        const resultKeys = Object.keys(this.exceptionResults)
        if (resultKeys.length > 0) {
          this.activeTab = resultKeys[0]
        }

        const totalExceptions = this.getTotalExceptions()
        this.$message.success(`成功检测 ${this.selectedFiles.length} 个文件，发现 ${totalExceptions} 条异常记录`)
      } catch (error) {
        console.error('检测失败:', error)
        this.processProgress = 0
        this.progressText = ''
        this.$message.error(`异常检测失败: ${error.message || '请检查后端服务是否正常'}`)
      } finally {
        this.processing = false
        setTimeout(() => {
          this.processProgress = 0
          this.progressText = ''
        }, 3000)
      }
    },

    // 计算异常总数
    getTotalExceptions() {
      let total = 0
      for (const key in this.exceptionResults) {
        if (Array.isArray(this.exceptionResults[key])) {
          total += this.exceptionResults[key].length
        }
      }
      return total
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 文件上传区域样式 */
.upload-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #b3d8ff;
}

.upload-demo {
  margin-bottom: 15px;
}

.upload-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 文件选择容器样式 */
.file-selection-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.selection-section {
  margin-bottom: 20px;
  height:250px;
}

.section-header {
  margin-bottom: 20px;
  height:-10px;
}

.section-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.section-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 文件列表容器 */
.file-list-container {
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.file-table-wrapper {
  position: relative;
  max-height: 400px;
  overflow: auto;
}

/* 自定义表格滚动条样式 */
.file-table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.file-table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.file-table-wrapper::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.file-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}

/* 已选择文件区域 */
.selected-files-section {
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 600;
  color: #409eff;
}

.selected-files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.action-buttons .el-button {
  padding: 12px 20px;
  font-size: 14px;
}

/* 进度显示区域 */
.progress-section {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.progress-text {
  margin: 10px 0 0 0;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

/* 结果展示区域样式 */
.results-container {
  margin-top: 20px;
}

.result-summary {
  float: right;
  color: #409eff;
  font-weight: 600;
}

.empty-state {
  margin-top: 20px;
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

/* 卡片样式 */
.box-card {
  margin-top: 20px;
}

.el-table {
  margin-top: 15px;
}

/* 滚动容器 */
.scroll-container {
  height: 600px;
  position: relative;
}

.custom-scrollbar {
  height: 100%;
  overflow: auto;
  padding-right: 12px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8aeb3;
}

/* 表格样式优化 */
.file-list-container .el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.file-list-container .el-table td {
  padding: 12px 0;
}

.file-list-container .el-table .el-icon-document {
  color: #67c23a;
  font-size: 16px;
}

/* 表格行悬停效果 */
.file-list-container .el-table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 记录数样式 */
.file-list-container .el-table .record-count {
  font-weight: 600;
  color: #409eff;
}

/* 状态标签样式调整 */
.file-list-container .el-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
