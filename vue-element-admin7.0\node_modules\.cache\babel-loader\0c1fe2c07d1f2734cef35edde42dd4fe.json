{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\@babel\\runtime\\helpers\\esm\\nonIterableSpread.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\@babel\\runtime\\helpers\\esm\\nonIterableSpread.js", "mtime": 1749148890689}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZnVuY3Rpb24gX25vbkl0ZXJhYmxlU3ByZWFkKCkgewogIHRocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBzcHJlYWQgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpOwp9CmV4cG9ydCB7IF9ub25JdGVyYWJsZVNwcmVhZCBhcyBkZWZhdWx0IH07"}, {"version": 3, "names": ["_nonIterableSpread", "TypeError", "default"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js"], "sourcesContent": ["function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };"], "mappings": "AAAA,SAASA,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AACA,SAASD,kBAAkB,IAAIE,OAAO", "ignoreList": []}]}