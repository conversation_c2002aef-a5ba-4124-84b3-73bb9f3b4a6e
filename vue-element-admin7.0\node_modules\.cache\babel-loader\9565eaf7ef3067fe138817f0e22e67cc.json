{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749173686857}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "ExceptionResults", "name", "components", "data", "uploadFileList", "uploading", "uploadProgress", "uploadProgressText", "availableTables", "selectedTables", "loadingFiles", "processing", "processProgress", "progressText", "exceptionList", "exceptionColumns", "scrollContainer", "mounted", "loadAvailableFiles", "methods", "handleFileChange", "file", "fileList", "console", "log", "handleFileRemove", "beforeUpload", "isExcel", "type", "isLt10M", "size", "$message", "error", "clearUploadFiles", "$refs", "upload", "clearFiles", "info", "handleUpload", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "formData", "progressInterval", "response", "_response$data", "_t", "_t2", "w", "_context", "n", "length", "warning", "a", "p", "FormData", "for<PERSON>ach", "fileItem", "index", "append", "raw", "setInterval", "Math", "random", "concat", "round", "post", "headers", "timeout", "v", "success", "Error", "message", "status", "warn", "Promise", "resolve", "setTimeout", "clearInterval", "f", "_this2", "_callee2", "_t3", "_context2", "paths", "map", "filePath", "fileName", "split", "pop", "tableName", "replace", "id", "createDate", "recordCount", "handleSelectionChange", "selection", "removeSelectedTable", "table", "_this3", "findIndex", "t", "splice", "$nextTick", "tableRef", "tableList", "toggleRowSelection", "clearSelection", "_this4", "processSelectedTables", "_this5", "_callee3", "filePaths", "_error$response$data", "_t4", "_context3", "currentStep", "floor", "steps", "filenames", "Object", "keys", "exceptionType", "exceptions", "item", "exception", "_objectSpread", "异常类型", "push", "prop", "label", "width", "align", "request", "handleScroll", "event", "getExceptionTypeColor", "colorMap", "getColumnWidth", "columnName", "widthMap", "getColumnAlign", "alignMap", "handleExportResults", "handleClearResults", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "then", "catch"], "sources": ["src/components/Charts/OrderException.vue"], "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"300\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<div class=\"header-actions\">\r\n<span v-if=\"selectedTables.length > 8\" class=\"scroll-tip\">可滚动查看更多</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nsize=\"small\"\r\ntype=\"info\"\r\nstyle=\"margin: 2px 4px 2px 0;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n</div>\r\n\r\n<!-- 操作按钮区域 - 移到容器外部 -->\r\n<div class=\"action-buttons-wrapper\">\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\nsize=\"medium\"\r\n>\r\n{{ processing ? '处理中...' : '🔍 异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n  :percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n  :stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<!-- 异常结果展示组件 -->\r\n<exception-results\r\n  :exception-data=\"exceptionList\"\r\n  @export-results=\"handleExportResults\"\r\n  @clear-results=\"handleClearResults\"\r\n/>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport ExceptionResults from './ExceptionResults.vue'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  components: {\r\n    ExceptionResults\r\n  },\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // Excel文件选择相关\r\n      availableTables: [], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      exceptionColumns: [], // 动态生成的表格列\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 真正调用后端API上传文件\r\n        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传\r\n        try {\r\n          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            timeout: 60000\r\n          })\r\n\r\n          // 检查上传结果\r\n          if (!response.data || !response.data.success) {\r\n            throw new Error(response.data?.message || '上传失败')\r\n          }\r\n        } catch (uploadError) {\r\n          // 如果上传接口不存在，使用模拟上传\r\n          if (uploadError.response && uploadError.response.status === 404) {\r\n            console.warn('上传接口不存在，使用模拟上传')\r\n            await new Promise(resolve => setTimeout(resolve, 2000))\r\n          } else {\r\n            throw uploadError\r\n          }\r\n        }\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 上传成功后，重新加载服务器上的Excel文件列表\r\n        await this.loadAvailableFiles()\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n        console.log('这些文件来自path_default文件夹:', response.data.paths)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('选中的表格数据:', this.selectedTables)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n        console.log('这些路径来自path_default文件夹:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          console.log('后端返回的原始数据结构:', response.data)\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            console.log(`异常类型 ${exceptionType} 的数据:`, exceptions)\r\n\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 直接使用后端返回的数据，添加异常类型\r\n                const exception = {\r\n                  异常类型: exceptionType, // 添加异常类型字段\r\n                  ...item // 展开后端返回的所有字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          // 根据detectTrade函数的返回结构，固定列配置\r\n          this.exceptionColumns = [\r\n            {\r\n              prop: '异常类型',\r\n              label: '异常类型',\r\n              width: 150,\r\n              align: 'center',\r\n              type: 'tag'\r\n            },\r\n            {\r\n              prop: '订单号',\r\n              label: '订单号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人姓名',\r\n              label: '支付人姓名',\r\n              width: 120,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人身份证号',\r\n              label: '支付人身份证号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '物流单号',\r\n              label: '物流单号',\r\n              width: 180,\r\n              align: 'center'\r\n            }\r\n          ]\r\n\r\n          this.exceptionList = exceptionList\r\n          console.log('处理后的异常数据列表:', this.exceptionList)\r\n          console.log('表格列配置:', this.exceptionColumns)\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success',\r\n        // 添加更多可能的异常类型\r\n        '重复订单': 'danger',\r\n        '异常物流': 'warning',\r\n        '身份证异常': 'danger',\r\n        '姓名异常': 'warning'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    },\r\n\r\n    // 根据列名获取列宽度\r\n    getColumnWidth(columnName) {\r\n      const widthMap = {\r\n        '订单号': 180,\r\n        '支付人姓名': 120,\r\n        '支付人身份证号': 180,\r\n        '物流单号': 180,\r\n        '异常类型': 150\r\n      }\r\n      return widthMap[columnName] || 120\r\n    },\r\n\r\n    // 根据列名获取对齐方式\r\n    getColumnAlign(columnName) {\r\n      const alignMap = {\r\n        '订单号': 'center',\r\n        '支付人姓名': 'center',\r\n        '支付人身份证号': 'center',\r\n        '物流单号': 'center',\r\n        '异常类型': 'center'\r\n      }\r\n      return alignMap[columnName] || 'left'\r\n    },\r\n\r\n    // 处理导出结果\r\n    handleExportResults(data) {\r\n      console.log('导出异常结果:', data)\r\n      // 这里可以实现具体的导出逻辑\r\n      this.$message.success('导出功能开发中...')\r\n    },\r\n\r\n    // 处理清空结果\r\n    handleClearResults() {\r\n      this.$confirm('确定要清空所有异常检测结果吗？', '确认清空', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.exceptionList = []\r\n        this.exceptionColumns = []\r\n        this.$message.success('已清空异常检测结果')\r\n      }).catch(() => {\r\n        this.$message.info('已取消清空操作')\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 20px; /* 从30px减少到20px */\r\n  padding: 15px; /* 从20px减少到15px */\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100px; /* 从180px减少到100px */\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 40px; /* 从67px减少到40px */\r\n  color: #c0c4cc;\r\n  margin: 15px 0 8px; /* 从40px 0 16px调整到15px 0 8px */\r\n  line-height: 30px; /* 从50px减少到30px */\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 13px; /* 从14px减少到13px */\r\n  text-align: center;\r\n  margin-top: -5px; /* 向上调整位置 */\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 15px; /* 从20px减少到15px */\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 15px 0; /* 从20px减少到15px */\r\n  padding: 12px; /* 从15px减少到12px */\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n  max-height: 120px; /* 从200px减少到120px */\r\n  overflow: hidden; /* 隐藏溢出内容 */\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.scroll-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  font-weight: normal;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px; /* 从8px减少到6px */\r\n  max-height: 80px; /* 从120px减少到80px */\r\n  overflow-y: auto; /* 添加垂直滚动条 */\r\n  padding-right: 8px; /* 为滚动条留出空间 */\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.selected-tables-list::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 操作按钮包装器 */\r\n.action-buttons-wrapper {\r\n  margin: 20px 0;\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #ffffff, #f8f9fa);\r\n  border: 2px solid #409eff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.action-buttons::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  background: linear-gradient(45deg, #409eff, #67c23a, #e6a23c, #f56c6c);\r\n  border-radius: 12px;\r\n  z-index: -1;\r\n  animation: borderGlow 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes borderGlow {\r\n  0%, 100% { opacity: 0.6; }\r\n  50% { opacity: 1; }\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 14px 24px;\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-buttons .el-button--success {\r\n  background: linear-gradient(135deg, #67c23a, #85ce61);\r\n  border: none;\r\n  box-shadow: 0 3px 10px rgba(103, 194, 58, 0.4);\r\n  font-size: 16px;\r\n  padding: 16px 32px;\r\n}\r\n\r\n.action-buttons .el-button--success:hover {\r\n  background: linear-gradient(135deg, #85ce61, #67c23a);\r\n  transform: translateY(-2px) scale(1.05);\r\n  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.5);\r\n}\r\n\r\n.action-buttons .el-button--primary {\r\n  background: linear-gradient(135deg, #409eff, #66b3ff);\r\n  border: none;\r\n  box-shadow: 0 3px 10px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n.action-buttons .el-button--primary:hover {\r\n  background: linear-gradient(135deg, #66b3ff, #409eff);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 15px;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);\r\n  border-radius: 10px;\r\n  border: 1px solid #b3d8ff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.progress-text {\r\n  margin: 15px 0 0 0;\r\n  font-size: 15px;\r\n  color: #409eff;\r\n  text-align: center;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n/* 异常结果组件容器 */\r\n.exception-results-wrapper {\r\n  margin-top: 30px;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n   .action-buttons {\r\n     flex-direction: column;\r\n   }\r\n   .action-buttons .el-button {\r\n     width: 100%;\r\n   }\r\n \r\n   .exception-header {\r\n     padding: 12px 15px;\r\n     margin: -15px -15px 15px -15px;\r\n   }\r\n \r\n   .header-title {\r\n     font-size: 16px;\r\n   }\r\n \r\n   .scroll-container {\r\n     height: 400px; \r\n   }\r\n \r\n   .table-summary {\r\n     padding: 12px 15px;\r\n   }\r\n\r\n   .summary-text {\r\n     font-size: 14px;\r\n   }\r\n\r\n   .exception-count {\r\n     font-size: 16px; \r\n   }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA6KA,OAAAA,KAAA;AACA,OAAAC,gBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF,gBAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA;MACAC,SAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,eAAA;MAAA;MACAC,cAAA;MACAC,YAAA;MACAC,UAAA;MACAC,eAAA;MACAC,YAAA;MAEA;MACAC,aAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAH,aAAA;IACA;IACA,KAAAI,kBAAA;EACA;EACAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,QAAA;MACA,KAAAlB,cAAA,GAAAkB,QAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,QAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAAJ,IAAA,EAAAC,QAAA;MACA,KAAAlB,cAAA,GAAAkB,QAAA;MACAC,OAAA,CAAAC,GAAA,WAAAH,IAAA,CAAApB,IAAA;IACA;IAEAyB,YAAA,WAAAA,aAAAL,IAAA;MACA,IAAAM,OAAA,GAAAN,IAAA,CAAAO,IAAA,4EACAP,IAAA,CAAAO,IAAA;MACA,IAAAC,OAAA,GAAAR,IAAA,CAAAS,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAI,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAH,OAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEAC,gBAAA,WAAAA,iBAAA;MACA,KAAA7B,cAAA;MACA,KAAA8B,KAAA,CAAAC,MAAA,CAAAC,UAAA;MACA,KAAAL,QAAA,CAAAM,IAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,gBAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAR,YAAA,GAAAS,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAA,MACAb,KAAA,CAAAnC,cAAA,CAAAiD,MAAA;gBAAAF,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACAb,KAAA,CAAAR,QAAA,CAAAuB,OAAA;cAAA,OAAAH,QAAA,CAAAI,CAAA;YAAA;cAIAhB,KAAA,CAAAlC,SAAA;cACAkC,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;cAAA4C,QAAA,CAAAK,CAAA;cAGAZ,QAAA,OAAAa,QAAA,IAEA;cACAlB,KAAA,CAAAnC,cAAA,CAAAsD,OAAA,WAAAC,QAAA,EAAAC,KAAA;gBACAhB,QAAA,CAAAiB,MAAA,UAAAF,QAAA,CAAAG,GAAA;cACA;;cAEA;cACAjB,gBAAA,GAAAkB,WAAA;gBACA,IAAAxB,KAAA,CAAAjC,cAAA;kBACAiC,KAAA,CAAAjC,cAAA,IAAA0D,IAAA,CAAAC,MAAA;kBACA1B,KAAA,CAAAhC,kBAAA,8CAAA2D,MAAA,CAAAF,IAAA,CAAAG,KAAA,CAAA5B,KAAA,CAAAjC,cAAA;gBACA;cACA,SAEA;cACA;cAAA6C,QAAA,CAAAK,CAAA;cAAAL,QAAA,CAAAC,CAAA;cAAA,OAEArD,KAAA,CAAAqE,IAAA,uCAAAxB,QAAA;gBACAyB,OAAA;kBACA;gBACA;gBACAC,OAAA;cACA;YAAA;cALAxB,QAAA,GAAAK,QAAA,CAAAoB,CAAA;cAAA,MAQA,CAAAzB,QAAA,CAAA3C,IAAA,KAAA2C,QAAA,CAAA3C,IAAA,CAAAqE,OAAA;gBAAArB,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,MACA,IAAAqB,KAAA,GAAA1B,cAAA,GAAAD,QAAA,CAAA3C,IAAA,cAAA4C,cAAA,uBAAAA,cAAA,CAAA2B,OAAA;YAAA;cAAAvB,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAK,CAAA;cAAAR,EAAA,GAAAG,QAAA,CAAAoB,CAAA;cAAA,MAIAvB,EAAA,CAAAF,QAAA,IAAAE,EAAA,CAAAF,QAAA,CAAA6B,MAAA;gBAAAxB,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACA7B,OAAA,CAAAqD,IAAA;cAAAzB,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAyB,OAAA,WAAAC,OAAA;gBAAA,OAAAC,UAAA,CAAAD,OAAA;cAAA;YAAA;cAAA3B,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAA,MAAAJ,EAAA;YAAA;cAMAgC,aAAA,CAAAnC,gBAAA;cACAN,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;;cAEA;cAAA4C,QAAA,CAAAC,CAAA;cAAA,OACAb,KAAA,CAAArB,kBAAA;YAAA;cAEAqB,KAAA,CAAAR,QAAA,CAAAyC,OAAA,6BAAAN,MAAA,CAAA3B,KAAA,CAAAnC,cAAA,CAAAiD,MAAA;cACAd,KAAA,CAAAN,gBAAA;cAAAkB,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAK,CAAA;cAAAP,GAAA,GAAAE,QAAA,CAAAoB,CAAA;cAEAhD,OAAA,CAAAS,KAAA,UAAAiB,GAAA;cACAV,KAAA,CAAAjC,cAAA;cACAiC,KAAA,CAAAhC,kBAAA;cACAgC,KAAA,CAAAR,QAAA,CAAAC,KAAA,8BAAAkC,MAAA,CAAAjB,GAAA,CAAAyB,OAAA;YAAA;cAAAvB,QAAA,CAAAK,CAAA;cAEAjB,KAAA,CAAAlC,SAAA;cACA0E,UAAA;gBACAxC,KAAA,CAAAjC,cAAA;gBACAiC,KAAA,CAAAhC,kBAAA;cACA;cAAA,OAAA4C,QAAA,CAAA8B,CAAA;YAAA;cAAA,OAAA9B,QAAA,CAAAI,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAzB,kBAAA,WAAAA,mBAAA;MAAA,IAAAgE,MAAA;MAAA,OAAA1C,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAyC,SAAA;QAAA,IAAArC,QAAA,EAAAsC,GAAA;QAAA,OAAA3C,YAAA,GAAAS,CAAA,WAAAmC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,CAAA;YAAA;cACA8B,MAAA,CAAAxE,YAAA;cAAA2E,SAAA,CAAA7B,CAAA;cAAA6B,SAAA,CAAAjC,CAAA;cAAA,OAGArD,KAAA,CAAAqE,IAAA;YAAA;cAAAtB,QAAA,GAAAuC,SAAA,CAAAd,CAAA;cACAhD,OAAA,CAAAC,GAAA,oBAAAsB,QAAA,CAAA3C,IAAA;cACAoB,OAAA,CAAAC,GAAA,2BAAAsB,QAAA,CAAA3C,IAAA,CAAAmF,KAAA;cAEA,IAAAxC,QAAA,CAAA3C,IAAA,IAAA2C,QAAA,CAAA3C,IAAA,CAAAmF,KAAA;gBACA;gBACAJ,MAAA,CAAA1E,eAAA,GAAAsC,QAAA,CAAA3C,IAAA,CAAAmF,KAAA,CAAAC,GAAA,WAAAC,QAAA,EAAA5B,KAAA;kBACA;kBACA,IAAA6B,QAAA,GAAAD,QAAA,CAAAE,KAAA,OAAAC,GAAA,MAAAH,QAAA,CAAAE,KAAA,MAAAC,GAAA;kBACA,IAAAC,SAAA,GAAAH,QAAA,CAAAI,OAAA;;kBAEA;oBACAC,EAAA,EAAAlC,KAAA;oBACAgC,SAAA,EAAAA,SAAA;oBAAA;oBACAJ,QAAA,EAAAA,QAAA;oBAAA;oBACAO,UAAA;oBAAA;oBACAC,WAAA;oBAAA;oBACArB,MAAA;kBACA;gBACA;gBACAO,MAAA,CAAAnD,QAAA,CAAAyC,OAAA,uBAAAN,MAAA,CAAAgB,MAAA,CAAA1E,eAAA,CAAA6C,MAAA;cACA;gBACA6B,MAAA,CAAAnD,QAAA,CAAAuB,OAAA;cACA;cAAA+B,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAA7B,CAAA;cAAA4B,GAAA,GAAAC,SAAA,CAAAd,CAAA;cAEAhD,OAAA,CAAAS,KAAA,mBAAAoD,GAAA;cACAF,MAAA,CAAAnD,QAAA,CAAAC,KAAA,qBAAAoD,GAAA,CAAAV,OAAA;YAAA;cAAAW,SAAA,CAAA7B,CAAA;cAEA0B,MAAA,CAAAxE,YAAA;cAAA,OAAA2E,SAAA,CAAAJ,CAAA;YAAA;cAAA,OAAAI,SAAA,CAAA9B,CAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IAEA;IAEA;IACAc,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzF,cAAA,GAAAyF,SAAA;MACA3E,OAAA,CAAAC,GAAA,gBAAA0E,SAAA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAzC,KAAA,QAAAnD,cAAA,CAAA6F,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAT,EAAA,KAAAM,KAAA,CAAAN,EAAA;MAAA;MACA,IAAAlC,KAAA;QACA,KAAAnD,cAAA,CAAA+F,MAAA,CAAA5C,KAAA;MACA;MACA;MACA,KAAA6C,SAAA;QACA,IAAAC,QAAA,GAAAL,MAAA,CAAAnE,KAAA,CAAAyE,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAE,kBAAA,CAAAR,KAAA;QACA;MACA;IACA;IAEA;IACAS,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAArG,cAAA;MACA;MACA,KAAAgG,SAAA;QACA,IAAAC,QAAA,GAAAI,MAAA,CAAA5E,KAAA,CAAAyE,SAAA;QACA,IAAAD,QAAA;UACAA,QAAA,CAAAG,cAAA;QACA;MACA;MACA,KAAA9E,QAAA,CAAAM,IAAA;IACA;IACA0E,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAAA,OAAAxE,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAuE,SAAA;QAAA,IAAApE,gBAAA,EAAAqE,SAAA,EAAApE,QAAA,EAAAhC,aAAA,EAAAqG,oBAAA,EAAAC,GAAA;QAAA,OAAA3E,YAAA,GAAAS,CAAA,WAAAmE,SAAA;UAAA,kBAAAA,SAAA,CAAAjE,CAAA;YAAA;cAAA,MACA4D,MAAA,CAAAvG,cAAA,CAAA4C,MAAA;gBAAAgE,SAAA,CAAAjE,CAAA;gBAAA;cAAA;cACA4D,MAAA,CAAAjF,QAAA,CAAAuB,OAAA;cAAA,OAAA+D,SAAA,CAAA9D,CAAA;YAAA;cAIAyD,MAAA,CAAArG,UAAA;cACAqG,MAAA,CAAApG,eAAA;cACAoG,MAAA,CAAAnG,YAAA;cAAAwG,SAAA,CAAA7D,CAAA;cAGA;cACAX,gBAAA,GAAAkB,WAAA;gBACA,IAAAiD,MAAA,CAAApG,eAAA;kBACAoG,MAAA,CAAApG,eAAA,IAAAoD,IAAA,CAAAC,MAAA;kBACA,IAAAqD,WAAA,GAAAtD,IAAA,CAAAuD,KAAA,CAAAP,MAAA,CAAApG,eAAA;kBACA,IAAA4G,KAAA;kBACAR,MAAA,CAAAnG,YAAA,GAAA2G,KAAA,CAAAF,WAAA;gBACA;cACA,SAEA;cACAJ,SAAA,GAAAF,MAAA,CAAAvG,cAAA,CAAA8E,GAAA,WAAAgB,CAAA;gBAAA,OAAAA,CAAA,CAAAf,QAAA;cAAA;cACAjE,OAAA,CAAAC,GAAA,aAAAwF,MAAA,CAAAvG,cAAA;cACAc,OAAA,CAAAC,GAAA,gBAAA0F,SAAA;cACA3F,OAAA,CAAAC,GAAA,2BAAA0F,SAAA;cAEAF,MAAA,CAAAnG,YAAA;;cAEA;cAAAwG,SAAA,CAAAjE,CAAA;cAAA,OACArD,KAAA,CAAAqE,IAAA;gBACAqD,SAAA,EAAAP;cACA;YAAA;cAFApE,QAAA,GAAAuE,SAAA,CAAA9C,CAAA;cAIAS,aAAA,CAAAnC,gBAAA;cACAmE,MAAA,CAAApG,eAAA;cACAoG,MAAA,CAAAnG,YAAA;cAEAU,OAAA,CAAAC,GAAA,iBAAAsB,QAAA,CAAA3C,IAAA;;cAEA;cACA,IAAA2C,QAAA,CAAA3C,IAAA;gBACAW,aAAA;gBAEAS,OAAA,CAAAC,GAAA,iBAAAsB,QAAA,CAAA3C,IAAA;;gBAEA;gBACAuH,MAAA,CAAAC,IAAA,CAAA7E,QAAA,CAAA3C,IAAA,EAAAuD,OAAA,WAAAkE,aAAA;kBACA,IAAAC,UAAA,GAAA/E,QAAA,CAAA3C,IAAA,CAAAyH,aAAA;kBACArG,OAAA,CAAAC,GAAA,6BAAA0C,MAAA,CAAA0D,aAAA,2BAAAC,UAAA;kBAEA,IAAAA,UAAA,IAAAA,UAAA,CAAAxE,MAAA;oBACAwE,UAAA,CAAAnE,OAAA,WAAAoE,IAAA,EAAAlE,KAAA;sBACA;sBACA,IAAAmE,SAAA,GAAAC,aAAA;wBACAC,IAAA,EAAAL;sBAAA,GACAE,IAAA,CACA;sBACAhH,aAAA,CAAAoH,IAAA,CAAAH,SAAA;oBACA;kBACA;gBACA;;gBAEA;gBACAf,MAAA,CAAAjG,gBAAA,IACA;kBACAoH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;kBACA1G,IAAA;gBACA,GACA;kBACAuG,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GACA;kBACAH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GACA;kBACAH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GACA;kBACAH,IAAA;kBACAC,KAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,EACA;gBAEAtB,MAAA,CAAAlG,aAAA,GAAAA,aAAA;gBACAS,OAAA,CAAAC,GAAA,gBAAAwF,MAAA,CAAAlG,aAAA;gBACAS,OAAA,CAAAC,GAAA,WAAAwF,MAAA,CAAAjG,gBAAA;gBAEA,IAAAD,aAAA,CAAAuC,MAAA;kBACA2D,MAAA,CAAAjF,QAAA,CAAAyC,OAAA,6BAAAN,MAAA,CAAA8C,MAAA,CAAAvG,cAAA,CAAA4C,MAAA,iDAAAa,MAAA,CAAApD,aAAA,CAAAuC,MAAA;gBACA;kBACA2D,MAAA,CAAAjF,QAAA,CAAAM,IAAA,6BAAA6B,MAAA,CAAA8C,MAAA,CAAAvG,cAAA,CAAA4C,MAAA;gBACA;cACA;gBACA2D,MAAA,CAAAjF,QAAA,CAAAuB,OAAA;cACA;cAAA+D,SAAA,CAAAjE,CAAA;cAAA;YAAA;cAAAiE,SAAA,CAAA7D,CAAA;cAAA4D,GAAA,GAAAC,SAAA,CAAA9C,CAAA;cAEAhD,OAAA,CAAAS,KAAA,UAAAoF,GAAA;cACAJ,MAAA,CAAApG,eAAA;cACAoG,MAAA,CAAAnG,YAAA;cAEA,IAAAuG,GAAA,CAAAtE,QAAA;gBACAkE,MAAA,CAAAjF,QAAA,CAAAC,KAAA,8BAAAkC,MAAA,CAAAkD,GAAA,CAAAtE,QAAA,CAAA6B,MAAA,SAAAT,MAAA,GAAAiD,oBAAA,GAAAC,GAAA,CAAAtE,QAAA,CAAA3C,IAAA,cAAAgH,oBAAA,uBAAAA,oBAAA,CAAAzC,OAAA,KAAA0C,GAAA,CAAA1C,OAAA;cACA,WAAA0C,GAAA,CAAAmB,OAAA;gBACAvB,MAAA,CAAAjF,QAAA,CAAAC,KAAA;cACA;gBACAgF,MAAA,CAAAjF,QAAA,CAAAC,KAAA,8BAAAkC,MAAA,CAAAkD,GAAA,CAAA1C,OAAA;cACA;YAAA;cAAA2C,SAAA,CAAA7D,CAAA;cAEAwD,MAAA,CAAArG,UAAA;cACAoE,UAAA;gBACAiC,MAAA,CAAApG,eAAA;gBACAoG,MAAA,CAAAnG,YAAA;cACA;cAAA,OAAAwG,SAAA,CAAApC,CAAA;YAAA;cAAA,OAAAoC,SAAA,CAAA9D,CAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA;IAEA;IAEAuB,YAAA,WAAAA,aAAAC,KAAA;MACA;MACAlH,OAAA,CAAAC,GAAA,iBAAAiH,KAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAd,aAAA;MACA,IAAAe,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAf,aAAA;IACA;IAEA;IACAgB,cAAA,WAAAA,eAAAC,UAAA;MACA,IAAAC,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,UAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAF,UAAA;MACA,IAAAG,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAH,UAAA;IACA;IAEA;IACAI,mBAAA,WAAAA,oBAAA9I,IAAA;MACAoB,OAAA,CAAAC,GAAA,YAAArB,IAAA;MACA;MACA,KAAA4B,QAAA,CAAAyC,OAAA;IACA;IAEA;IACA0E,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA1H,IAAA;MACA,GAAA2H,IAAA;QACAJ,MAAA,CAAArI,aAAA;QACAqI,MAAA,CAAApI,gBAAA;QACAoI,MAAA,CAAApH,QAAA,CAAAyC,OAAA;MACA,GAAAgF,KAAA;QACAL,MAAA,CAAApH,QAAA,CAAAM,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}