{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\RateGraph.vue", "mtime": 1748922947668}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "mapState", "mapActions", "name", "data", "options", "date<PERSON><PERSON><PERSON>", "username", "isBottom", "weights", "flag2", "flag3", "flag4", "flag5", "scrollContainer", "computed", "_objectSpread", "rankData", "state", "rateGraph", "dataLoaded", "selectedTable", "value", "get", "set", "selectTable", "mounted", "handleSearch", "$refs", "methods", "updateRankData", "updateWeights", "resetWeightsAction", "handleSelectChange", "console", "log", "$message", "info", "handleNameClick", "$router", "push", "path", "query", "encodeURIComponent", "timeUnit", "_this", "then", "response", "all_tables", "map", "item", "label", "catch", "error", "generateRankData", "_this2", "_asyncToGenerator", "_regenerator", "m", "_callee", "res", "getRes", "_t", "_t2", "w", "_context", "n", "p", "post", "headers", "v", "a", "Promise", "resolve", "message", "reject", "confirmSelection", "_this3", "loading", "$loading", "lock", "text", "spinner", "background", "close", "success", "resetWeights", "handleScroll", "_ref", "scrollTop", "scrollHeight", "clientHeight"], "sources": ["src/components/Charts/RateGraph.vue"], "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" :disabled=\"!value\" @click=\"confirmSelection\">确认</el-button>\r\n    <el-button type=\"info\" :disabled=\"!value\" @click=\"resetWeights\">重置权重</el-button>\r\n\r\n    <!-- 权重设置区域 -->\r\n    <div class=\"weight-settings\">\r\n      <h3>标签权重设置</h3>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>快进快出：</span>\r\n          <el-slider v-model=\"weights.flag2\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>高频交易：</span>\r\n          <el-slider v-model=\"weights.flag3\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"weight-item\">\r\n          <span>时间集中：</span>\r\n          <el-slider v-model=\"weights.flag4\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n        <div class=\"weight-item\">\r\n          <span>小额测试：</span>\r\n          <el-slider v-model=\"weights.flag5\" :min=\"0\" :max=\"5\" :step=\"0.1\" show-input />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card style=\"margin: 20px; min-height: 520px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>嫌疑排行榜</span>\r\n      </div>\r\n\r\n      <div class=\"scroll-container\">\r\n        <div ref=\"scrollContainer\" class=\"custom-scrollbar\" @scroll=\"handleScroll\">\r\n          <el-table :data=\"rankData\" style=\"width: 100%\">\r\n            <el-table-column type=\"index\" label=\"排名\" width=\"80\" />\r\n            <el-table-column prop=\"name\" label=\"姓名 / 公司名称\">\r\n              <template #default=\"scope\">\r\n                <el-link\r\n                  type=\"primary\"\r\n                  @click=\"handleNameClick(scope.row.name)\"\r\n                >\r\n                  {{ scope.row.name }}\r\n                </el-link>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fused_score\" label=\"嫌疑分值\">\r\n              <template #default=\"scope\">\r\n                {{ Number(scope.row.fused_score).toFixed(3) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"count\" label=\"命中标签数\">\r\n              <template #default=\"scope\">{{ scope.row.count }}</template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"flag2\" label=\"快进快出\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag2 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag3\" label=\"高频交易\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag3 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag4\" label=\"时间集中\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag4 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"flag5\" label=\"小额测试\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.flag5 ? '是' : '否' }}\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <div\r\n          v-if=\"isBottom\"\r\n          class=\"bottom-notice\"\r\n          :class=\"{ 'show-notice': isBottom }\"\r\n        >\r\n          已经到达末尾\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport { mapState, mapActions } from 'vuex'\r\n\r\nexport default {\r\n  name: 'RateGraph',\r\n  data() {\r\n    return {\r\n      options: [],\r\n      dateRange: [],\r\n      username: null,\r\n      isBottom: false,\r\n      weights: {\r\n        flag2: 1,\r\n        flag3: 1,\r\n        flag4: 1,\r\n        flag5: 1\r\n      },\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      rankData: state => state.rateGraph.rankData,\r\n      weights: state => state.rateGraph.weights,\r\n      dataLoaded: state => state.rateGraph.dataLoaded,\r\n      selectedTable: state => state.rateGraph.selectedTable\r\n    }),\r\n    value: {\r\n      get() {\r\n        return this.selectedTable\r\n      },\r\n      set(value) {\r\n        this.selectTable(value)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch()\r\n    this.scrollContainer = this.$refs.scrollContainer\r\n\r\n    // If we already have a selected table, update the dropdown\r\n    if (this.selectedTable) {\r\n      this.value = this.selectedTable\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      updateRankData: 'rateGraph/updateRankData',\r\n      selectTable: 'rateGraph/selectTable',\r\n      updateWeights: 'rateGraph/updateWeights',\r\n      resetWeightsAction: 'rateGraph/resetWeights'\r\n    }),\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      if (value) {\r\n        this.$message.info('已选择数据表，请点击「确认」按钮生成排行榜')\r\n      }\r\n    },\r\n    handleNameClick(name) {\r\n      this.$router.push({\r\n        path: '/charts/transmit-detail',\r\n        query: {\r\n          name: encodeURIComponent(name),\r\n          timeUnit: 'month'\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item,\r\n            value: item\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    },\r\n    async generateRankData(weights) {\r\n      try {\r\n        console.log(11)\r\n        if (weights) {\r\n          console.log('发送权重到后端:', weights)\r\n          try {\r\n            const res = await axios.post('http://127.0.0.1:8000/tianye_demo',\r\n              { weights: [this.weights.flag2, this.weights.flag3, this.weights.flag4, this.weights.flag5] }, // 数据\r\n              { // 配置\r\n                headers: {\r\n                  'Content-Type': 'application/json'\r\n                }\r\n              })\r\n            console.log('后端响应:', res)\r\n            console.log('后端', res.data.data)\r\n            this.updateRankData(res.data.data)\r\n          } catch (postError) {\r\n            const getRes = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n            this.updateRankData(getRes.data)\r\n          }\r\n        }\r\n        // } else {\r\n        //   console.log('默认')\r\n        //   try {\r\n        //     const res = await axios.get('http://127.0.0.1:8000/tianye_demo')\r\n        //     console.log('获取默认数据成功:', res)\r\n        //     this.updateRankData(res.data.data)\r\n        //   } catch (error) {\r\n        //     console.error('请求失败详细信息:', error)\r\n        //     if (error.response) {\r\n        //       console.error('服务器响应:', error.response.status, error.response.data)\r\n        //     } else if (error.request) {\r\n        //       console.error('没有收到响应，请求详情:', error.request)\r\n        //     } else {\r\n        //       console.error('请求设置错误:', error.message)\r\n        //     }\r\n        //     this.$message.error('连接后端失败，请检查网络和服务器状态')\r\n        //     throw error\r\n        //   }\r\n        // }\r\n        return Promise.resolve()\r\n      } catch (error) {\r\n        console.error('Error fetching rank data:', error)\r\n        this.$message.error('获取数据失败:', error.message)\r\n        return Promise.reject(error)\r\n      }\r\n    },\r\n    confirmSelection() {\r\n      // Only fetch data if we haven't loaded it yet or if the user explicitly wants to refresh\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: '正在处理数据...',\r\n        spinner: 'el-icon-loading',\r\n        background: 'rgba(255, 255, 255, 0.7)'\r\n      })\r\n\r\n      // Update the weights in the store\r\n      this.updateWeights(this.weights)\r\n\r\n      this.generateRankData(this.weights)\r\n        .then(() => {\r\n          loading.close()\r\n          this.$message.success('数据处理成功')\r\n        })\r\n        .catch(() => {\r\n          loading.close()\r\n        })\r\n    },\r\n    resetWeights() {\r\n      this.resetWeightsAction()\r\n      this.$message.info('已重置权重为默认值，点击「确认」按钮生效')\r\n    },\r\n    handleScroll({ scrollTop, scrollHeight, clientHeight }) {\r\n      this.isBottom = scrollTop + clientHeight >= scrollHeight - 5\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* Element UI 表格样式覆盖 */\r\n.el-table {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.scroll-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: calc(100vh - 200px); /* 根据实际情况调整 */\r\n  min-height: 380px;\r\n}\r\n\r\n/* 调整滚动条样式 */\r\n.custom-scrollbar {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  margin-bottom: 10px; /* 增加与底部提示的间距 */\r\n}\r\n\r\n/* 底部提示样式优化 */\r\n.bottom-notice {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  text-align: center;\r\n  color: #909399;\r\n  background: #f5f7fa;\r\n  border-top: 1px solid #dfe6ec;\r\n  transition: all 0.3s;\r\n  opacity: 0;\r\n}\r\n\r\n.show-notice {\r\n  opacity: 1;\r\n  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n/* 表格列样式微调 */\r\n.el-table-column {\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 权重设置区域样式 */\r\n.weight-settings {\r\n  margin: 12px;\r\n  padding: 12px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.weight-settings h3 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.weight-item {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;  /* 关键：平均分配宽度 */\r\n  height: 32px;\r\n}\r\n\r\n/* 新增：确保滑块容器占满剩余空间 */\r\n.weight-item .el-slider {\r\n  flex: 1;\r\n  min-width: 120px;  /* 防止内容过窄 */\r\n}\r\n\r\n.weight-item span {\r\n  width: 70px;\r\n  text-align: right;\r\n  margin-right: 10px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n/* 保持滑块细节样式 */\r\n.weight-item .el-slider__runway {\r\n  height: 4px !important;\r\n}\r\n.weight-item .el-slider__button {\r\n  width: 12px !important;\r\n  height: 12px !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAyHA,OAAAA,KAAA;AACA,SAAAC,QAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;MACAC,QAAA;MACAC,OAAA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,eAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,CAAAA,aAAA,KACAf,QAAA;IACAgB,QAAA,WAAAA,SAAAC,KAAA;MAAA,OAAAA,KAAA,CAAAC,SAAA,CAAAF,QAAA;IAAA;IACAR,OAAA,WAAAA,QAAAS,KAAA;MAAA,OAAAA,KAAA,CAAAC,SAAA,CAAAV,OAAA;IAAA;IACAW,UAAA,WAAAA,WAAAF,KAAA;MAAA,OAAAA,KAAA,CAAAC,SAAA,CAAAC,UAAA;IAAA;IACAC,aAAA,WAAAA,cAAAH,KAAA;MAAA,OAAAA,KAAA,CAAAC,SAAA,CAAAE,aAAA;IAAA;EACA;IACAC,KAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAF,aAAA;MACA;MACAG,GAAA,WAAAA,IAAAF,KAAA;QACA,KAAAG,WAAA,CAAAH,KAAA;MACA;IACA;EAAA,EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAb,eAAA,QAAAc,KAAA,CAAAd,eAAA;;IAEA;IACA,SAAAO,aAAA;MACA,KAAAC,KAAA,QAAAD,aAAA;IACA;EACA;EACAQ,OAAA,EAAAb,aAAA,CAAAA,aAAA,KACAd,UAAA;IACA4B,cAAA;IACAL,WAAA;IACAM,aAAA;IACAC,kBAAA;EACA;IACAC,kBAAA,WAAAA,mBAAAX,KAAA;MACAY,OAAA,CAAAC,GAAA,YAAAb,KAAA;MACA,IAAAA,KAAA;QACA,KAAAc,QAAA,CAAAC,IAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAAnC,IAAA;MACA,KAAAoC,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UACAvC,IAAA,EAAAwC,kBAAA,CAAAxC,IAAA;UACAyC,QAAA;QACA;MACA;IACA;IACAjB,YAAA,WAAAA,aAAA;MAAA,IAAAkB,KAAA;MACA7C,KAAA,CACAuB,GAAA,qCACAuB,IAAA,WAAAC,QAAA;QACA,IAAA3C,IAAA,GAAA2C,QAAA,CAAA3C,IAAA,CAAA4C,UAAA;QACAH,KAAA,CAAAxC,OAAA,GAAAD,IAAA,CAAA6C,GAAA,WAAAC,IAAA;UAAA;YACAC,KAAA,EAAAD,IAAA;YACA5B,KAAA,EAAA4B;UACA;QAAA;MACA,GACAE,KAAA,WAAAC,KAAA;QACAR,KAAA,CAAAT,QAAA,CAAAiB,KAAA,UAAAA,KAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA7C,OAAA;MAAA,IAAA8C,MAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAC,MAAA,EAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAN,YAAA,GAAAO,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAEAjC,OAAA,CAAAC,GAAA;cAAA,KACA1B,OAAA;gBAAAwD,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACAhC,OAAA,CAAAC,GAAA,aAAA1B,OAAA;cAAAwD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEAlE,KAAA,CAAAoE,IAAA,sCACA;gBAAA3D,OAAA,GAAA8C,MAAA,CAAA9C,OAAA,CAAAC,KAAA,EAAA6C,MAAA,CAAA9C,OAAA,CAAAE,KAAA,EAAA4C,MAAA,CAAA9C,OAAA,CAAAG,KAAA,EAAA2C,MAAA,CAAA9C,OAAA,CAAAI,KAAA;cAAA;cAAA;cACA;gBAAA;gBACAwD,OAAA;kBACA;gBACA;cACA;YAAA;cANAT,GAAA,GAAAK,QAAA,CAAAK,CAAA;cAOApC,OAAA,CAAAC,GAAA,UAAAyB,GAAA;cACA1B,OAAA,CAAAC,GAAA,OAAAyB,GAAA,CAAAxD,IAAA,CAAAA,IAAA;cACAmD,MAAA,CAAAzB,cAAA,CAAA8B,GAAA,CAAAxD,IAAA,CAAAA,IAAA;cAAA6D,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAL,EAAA,GAAAG,QAAA,CAAAK,CAAA;cAAAL,QAAA,CAAAC,CAAA;cAAA,OAEAlE,KAAA,CAAAuB,GAAA;YAAA;cAAAsC,MAAA,GAAAI,QAAA,CAAAK,CAAA;cACAf,MAAA,CAAAzB,cAAA,CAAA+B,MAAA,CAAAzD,IAAA;YAAA;cAAA,OAAA6D,QAAA,CAAAM,CAAA,IAsBAC,OAAA,CAAAC,OAAA;YAAA;cAAAR,QAAA,CAAAE,CAAA;cAAAJ,GAAA,GAAAE,QAAA,CAAAK,CAAA;cAEApC,OAAA,CAAAmB,KAAA,8BAAAU,GAAA;cACAR,MAAA,CAAAnB,QAAA,CAAAiB,KAAA,YAAAU,GAAA,CAAAW,OAAA;cAAA,OAAAT,QAAA,CAAAM,CAAA,IACAC,OAAA,CAAAG,MAAA,CAAAZ,GAAA;UAAA;QAAA,GAAAJ,OAAA;MAAA;IAEA;IACAiB,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,OAAA,QAAAC,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;QACAC,UAAA;MACA;;MAEA;MACA,KAAApD,aAAA,MAAAtB,OAAA;MAEA,KAAA6C,gBAAA,MAAA7C,OAAA,EACAqC,IAAA;QACAgC,OAAA,CAAAM,KAAA;QACAP,MAAA,CAAAzC,QAAA,CAAAiD,OAAA;MACA,GACAjC,KAAA;QACA0B,OAAA,CAAAM,KAAA;MACA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAtD,kBAAA;MACA,KAAAI,QAAA,CAAAC,IAAA;IACA;IACAkD,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,SAAA,GAAAD,IAAA,CAAAC,SAAA;QAAAC,YAAA,GAAAF,IAAA,CAAAE,YAAA;QAAAC,YAAA,GAAAH,IAAA,CAAAG,YAAA;MACA,KAAAnF,QAAA,GAAAiF,SAAA,GAAAE,YAAA,IAAAD,YAAA;IACA;EAAA;AAEA", "ignoreList": []}]}