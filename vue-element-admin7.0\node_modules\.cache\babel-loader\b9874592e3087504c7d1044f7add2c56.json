{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\SvgIcon\\index.vue", "mtime": 1731856724000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gZG9jOiBodHRwczovL3BhbmppYWNoZW4uZ2l0aHViLmlvL3Z1ZS1lbGVtZW50LWFkbWluLXNpdGUvZmVhdHVyZS9jb21wb25lbnQvc3ZnLWljb24uaHRtbCN1c2FnZQppbXBvcnQgeyBpc0V4dGVybmFsIGFzIF9pc0V4dGVybmFsIH0gZnJvbSAnQC91dGlscy92YWxpZGF0ZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3ZnSWNvbicsCiAgcHJvcHM6IHsKICAgIGljb25DbGFzczogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2xhc3NOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsOiBmdW5jdGlvbiBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gX2lzRXh0ZXJuYWwodGhpcy5pY29uQ2xhc3MpOwogICAgfSwKICAgIGljb25OYW1lOiBmdW5jdGlvbiBpY29uTmFtZSgpIHsKICAgICAgcmV0dXJuICIjaWNvbi0iLmNvbmNhdCh0aGlzLmljb25DbGFzcyk7CiAgICB9LAogICAgc3ZnQ2xhc3M6IGZ1bmN0aW9uIHN2Z0NsYXNzKCkgewogICAgICBpZiAodGhpcy5jbGFzc05hbWUpIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uICcgKyB0aGlzLmNsYXNzTmFtZTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uJzsKICAgICAgfQogICAgfSwKICAgIHN0eWxlRXh0ZXJuYWxJY29uOiBmdW5jdGlvbiBzdHlsZUV4dGVybmFsSWNvbigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBtYXNrOiAidXJsKCIuY29uY2F0KHRoaXMuaWNvbkNsYXNzLCAiKSBuby1yZXBlYXQgNTAlIDUwJSIpLAogICAgICAgICctd2Via2l0LW1hc2snOiAidXJsKCIuY29uY2F0KHRoaXMuaWNvbkNsYXNzLCAiKSBuby1yZXBlYXQgNTAlIDUwJSIpCiAgICAgIH07CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["isExternal", "name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "iconName", "concat", "svgClass", "styleExternalIcon", "mask"], "sources": ["src/components/SvgIcon/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\n// doc: https://panjiachen.github.io/vue-element-admin-site/feature/component/svg-icon.html#usage\nimport { isExternal } from '@/utils/validate'\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.iconClass)\n    },\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    },\n    styleExternalIcon() {\n      return {\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n.svg-external-icon {\n  background-color: currentColor;\n  mask-size: cover!important;\n  display: inline-block;\n}\n</style>\n"], "mappings": "AAQA;AACA,SAAAA,UAAA,IAAAA,WAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAC,QAAA;IACAT,UAAA,WAAAA,WAAA;MACA,OAAAA,WAAA,MAAAG,SAAA;IACA;IACAO,QAAA,WAAAA,SAAA;MACA,gBAAAC,MAAA,MAAAR,SAAA;IACA;IACAS,QAAA,WAAAA,SAAA;MACA,SAAAL,SAAA;QACA,0BAAAA,SAAA;MACA;QACA;MACA;IACA;IACAM,iBAAA,WAAAA,kBAAA;MACA;QACAC,IAAA,SAAAH,MAAA,MAAAR,SAAA;QACA,uBAAAQ,MAAA,MAAAR,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}