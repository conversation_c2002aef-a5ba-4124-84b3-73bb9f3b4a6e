{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\index.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\index.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucmVkdWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IucmVkdWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyI7CmltcG9ydCBWdWUgZnJvbSAndnVlJzsKaW1wb3J0IFZ1ZXggZnJvbSAndnVleCc7CmltcG9ydCBnZXR0ZXJzIGZyb20gJy4vZ2V0dGVycyc7ClZ1ZS51c2UoVnVleCk7CgovLyBodHRwczovL3dlYnBhY2suanMub3JnL2d1aWRlcy9kZXBlbmRlbmN5LW1hbmFnZW1lbnQvI3JlcXVpcmVjb250ZXh0CnZhciBtb2R1bGVzRmlsZXMgPSByZXF1aXJlLmNvbnRleHQoJy4vbW9kdWxlcycsIHRydWUsIC9cLmpzJC8pOwoKLy8geW91IGRvIG5vdCBuZWVkIGBpbXBvcnQgYXBwIGZyb20gJy4vbW9kdWxlcy9hcHAnYAovLyBpdCB3aWxsIGF1dG8gcmVxdWlyZSBhbGwgdnVleCBtb2R1bGUgZnJvbSBtb2R1bGVzIGZpbGUKdmFyIG1vZHVsZXMgPSBtb2R1bGVzRmlsZXMua2V5cygpLnJlZHVjZShmdW5jdGlvbiAobW9kdWxlcywgbW9kdWxlUGF0aCkgewogIC8vIHNldCAnLi9hcHAuanMnID0+ICdhcHAnCiAgdmFyIG1vZHVsZU5hbWUgPSBtb2R1bGVQYXRoLnJlcGxhY2UoL15cLlwvKC4qKVwuXHcrJC8sICckMScpOwogIHZhciB2YWx1ZSA9IG1vZHVsZXNGaWxlcyhtb2R1bGVQYXRoKTsKICBtb2R1bGVzW21vZHVsZU5hbWVdID0gdmFsdWUuZGVmYXVsdDsKICByZXR1cm4gbW9kdWxlczsKfSwge30pOwp2YXIgc3RvcmUgPSBuZXcgVnVleC5TdG9yZSh7CiAgbW9kdWxlczogbW9kdWxlcywKICBnZXR0ZXJzOiBnZXR0ZXJzCn0pOwpleHBvcnQgZGVmYXVsdCBzdG9yZTs="}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "getters", "use", "modulesFiles", "require", "context", "modules", "keys", "reduce", "modulePath", "moduleName", "replace", "value", "default", "store", "Store"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\nimport getters from './getters'\n\nVue.use(Vuex)\n\n// https://webpack.js.org/guides/dependency-management/#requirecontext\nconst modulesFiles = require.context('./modules', true, /\\.js$/)\n\n// you do not need `import app from './modules/app'`\n// it will auto require all vuex module from modules file\nconst modules = modulesFiles.keys().reduce((modules, modulePath) => {\n  // set './app.js' => 'app'\n  const moduleName = modulePath.replace(/^\\.\\/(.*)\\.\\w+$/, '$1')\n  const value = modulesFiles(modulePath)\n  modules[moduleName] = value.default\n  return modules\n}, {})\n\nconst store = new Vuex.Store({\n  modules,\n  getters\n})\n\nexport default store\n"], "mappings": ";;;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,WAAW;AAE/BF,GAAG,CAACG,GAAG,CAACF,IAAI,CAAC;;AAEb;AACA,IAAMG,YAAY,GAAGC,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC;;AAEhE;AACA;AACA,IAAMC,OAAO,GAAGH,YAAY,CAACI,IAAI,CAAC,CAAC,CAACC,MAAM,CAAC,UAACF,OAAO,EAAEG,UAAU,EAAK;EAClE;EACA,IAAMC,UAAU,GAAGD,UAAU,CAACE,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC;EAC9D,IAAMC,KAAK,GAAGT,YAAY,CAACM,UAAU,CAAC;EACtCH,OAAO,CAACI,UAAU,CAAC,GAAGE,KAAK,CAACC,OAAO;EACnC,OAAOP,OAAO;AAChB,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,IAAMQ,KAAK,GAAG,IAAId,IAAI,CAACe,KAAK,CAAC;EAC3BT,OAAO,EAAPA,OAAO;EACPL,OAAO,EAAPA;AACF,CAAC,CAAC;AAEF,eAAea,KAAK", "ignoreList": []}]}