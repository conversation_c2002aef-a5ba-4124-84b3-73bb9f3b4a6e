{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\RateGraph.vue?vue&type=style&index=0&id=5ab215e4&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\RateGraph.vue", "mtime": 1747748935261}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY2hhcnQtY29udGFpbmVyew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsNCn0NCg=="}, {"version": 3, "sources": ["RateGraph.vue"], "names": [], "mappings": ";AAgBA;AACA;AACA;AACA;AACA", "file": "RateGraph.vue", "sourceRoot": "src/views/charts", "sourcesContent": ["<template>\r\n    <div class=\"chart-container\">\r\n      <chart height=\"100%\" width=\"100%\" />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport Chart from '@/components/Charts/RateGraph'\r\n\r\nexport default {\r\n  name: 'RateGraph',\r\n  components: { Chart }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chart-container{\r\n  position: relative;\r\n  width: 100%;\r\n  height: calc(100vh - 84px);\r\n}\r\n</style>\n"]}]}