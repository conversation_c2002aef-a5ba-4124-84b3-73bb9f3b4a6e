{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\app.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\app.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Cookies", "state", "sidebar", "opened", "get", "withoutAnimation", "device", "size", "mutations", "TOGGLE_SIDEBAR", "set", "CLOSE_SIDEBAR", "TOGGLE_DEVICE", "SET_SIZE", "actions", "toggleSideBar", "_ref", "commit", "closeSideBar", "_ref2", "_ref3", "toggleDevice", "_ref4", "setSize", "_ref5", "namespaced"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/store/modules/app.js"], "sourcesContent": ["import Cookies from 'js-cookie'\n\nconst state = {\n  sidebar: {\n    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,\n    withoutAnimation: false\n  },\n  device: 'desktop',\n  size: Cookies.get('size') || 'medium'\n}\n\nconst mutations = {\n  TOGGLE_SIDEBAR: state => {\n    state.sidebar.opened = !state.sidebar.opened\n    state.sidebar.withoutAnimation = false\n    if (state.sidebar.opened) {\n      Cookies.set('sidebarStatus', 1)\n    } else {\n      Cookies.set('sidebarStatus', 0)\n    }\n  },\n  CLOSE_SIDEBAR: (state, withoutAnimation) => {\n    Cookies.set('sidebarStatus', 0)\n    state.sidebar.opened = false\n    state.sidebar.withoutAnimation = withoutAnimation\n  },\n  TOGGLE_DEVICE: (state, device) => {\n    state.device = device\n  },\n  SET_SIZE: (state, size) => {\n    state.size = size\n    Cookies.set('size', size)\n  }\n}\n\nconst actions = {\n  toggleSideBar({ commit }) {\n    commit('TOGGLE_SIDEBAR')\n  },\n  closeSideBar({ commit }, { withoutAnimation }) {\n    commit('CLOSE_SIDEBAR', withoutAnimation)\n  },\n  toggleDevice({ commit }, device) {\n    commit('TOGGLE_DEVICE', device)\n  },\n  setSize({ commit }, size) {\n    commit('SET_SIZE', size)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,KAAK,GAAG;EACZC,OAAO,EAAE;IACPC,MAAM,EAAEH,OAAO,CAACI,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAACJ,OAAO,CAACI,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI;IAC7EC,gBAAgB,EAAE;EACpB,CAAC;EACDC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAEP,OAAO,CAACI,GAAG,CAAC,MAAM,CAAC,IAAI;AAC/B,CAAC;AAED,IAAMI,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAER,KAAK,EAAI;IACvBA,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,CAACF,KAAK,CAACC,OAAO,CAACC,MAAM;IAC5CF,KAAK,CAACC,OAAO,CAACG,gBAAgB,GAAG,KAAK;IACtC,IAAIJ,KAAK,CAACC,OAAO,CAACC,MAAM,EAAE;MACxBH,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC,CAAC,MAAM;MACLV,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC;EACF,CAAC;EACDC,aAAa,EAAE,SAAfA,aAAaA,CAAGV,KAAK,EAAEI,gBAAgB,EAAK;IAC1CL,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IAC/BT,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,KAAK;IAC5BF,KAAK,CAACC,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;EACnD,CAAC;EACDO,aAAa,EAAE,SAAfA,aAAaA,CAAGX,KAAK,EAAEK,MAAM,EAAK;IAChCL,KAAK,CAACK,MAAM,GAAGA,MAAM;EACvB,CAAC;EACDO,QAAQ,EAAE,SAAVA,QAAQA,CAAGZ,KAAK,EAAEM,IAAI,EAAK;IACzBN,KAAK,CAACM,IAAI,GAAGA,IAAI;IACjBP,OAAO,CAACU,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC3B;AACF,CAAC;AAED,IAAMO,OAAO,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAAAC,IAAA,EAAa;IAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACpBA,MAAM,CAAC,gBAAgB,CAAC;EAC1B,CAAC;EACDC,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAAC,KAAA,EAAmC;IAAA,IAAhCH,MAAM,GAAAE,KAAA,CAANF,MAAM;IAAA,IAAMZ,gBAAgB,GAAAe,KAAA,CAAhBf,gBAAgB;IACzCY,MAAM,CAAC,eAAe,EAAEZ,gBAAgB,CAAC;EAC3C,CAAC;EACDgB,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAahB,MAAM,EAAE;IAAA,IAAlBW,MAAM,GAAAK,KAAA,CAANL,MAAM;IACnBA,MAAM,CAAC,eAAe,EAAEX,MAAM,CAAC;EACjC,CAAC;EACDiB,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAajB,IAAI,EAAE;IAAA,IAAhBU,MAAM,GAAAO,KAAA,CAANP,MAAM;IACdA,MAAM,CAAC,UAAU,EAAEV,IAAI,CAAC;EAC1B;AACF,CAAC;AAED,eAAe;EACbkB,UAAU,EAAE,IAAI;EAChBxB,KAAK,EAALA,KAAK;EACLO,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}