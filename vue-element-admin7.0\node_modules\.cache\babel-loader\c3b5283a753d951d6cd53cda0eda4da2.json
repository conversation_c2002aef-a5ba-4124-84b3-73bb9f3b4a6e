{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\PanThumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\PanThumb\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdQYW5UaHVtYicsCiAgcHJvcHM6IHsKICAgIGltYWdlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICB6SW5kZXg6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiAxCiAgICB9LAogICAgd2lkdGg6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnMTUwcHgnCiAgICB9LAogICAgaGVpZ2h0OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzE1MHB4JwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "props", "image", "type", "String", "required", "zIndex", "Number", "default", "width", "height"], "sources": ["src/components/PanThumb/index.vue"], "sourcesContent": ["<template>\n  <div :style=\"{zIndex:zIndex,height:height,width:width}\" class=\"pan-item\">\n    <div class=\"pan-info\">\n      <div class=\"pan-info-roles-container\">\n        <slot />\n      </div>\n    </div>\n    <!-- eslint-disable-next-line -->\n    <div :style=\"{backgroundImage: `url(${image})`}\" class=\"pan-thumb\"></div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'PanThumb',\n  props: {\n    image: {\n      type: String,\n      required: true\n    },\n    zIndex: {\n      type: Number,\n      default: 1\n    },\n    width: {\n      type: String,\n      default: '150px'\n    },\n    height: {\n      type: String,\n      default: '150px'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pan-item {\n  width: 200px;\n  height: 200px;\n  border-radius: 50%;\n  display: inline-block;\n  position: relative;\n  cursor: default;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\n}\n\n.pan-info-roles-container {\n  padding: 20px;\n  text-align: center;\n}\n\n.pan-thumb {\n  width: 100%;\n  height: 100%;\n  background-position: center center;\n  background-size: cover;\n  border-radius: 50%;\n  overflow: hidden;\n  position: absolute;\n  transform-origin: 95% 40%;\n  transition: all 0.3s ease-in-out;\n}\n\n/* .pan-thumb:after {\n  content: '';\n  width: 8px;\n  height: 8px;\n  position: absolute;\n  border-radius: 50%;\n  top: 40%;\n  left: 95%;\n  margin: -4px 0 0 -4px;\n  background: radial-gradient(ellipse at center, rgba(14, 14, 14, 1) 0%, rgba(125, 126, 125, 1) 100%);\n  box-shadow: 0 0 1px rgba(255, 255, 255, 0.9);\n} */\n\n.pan-info {\n  position: absolute;\n  width: inherit;\n  height: inherit;\n  border-radius: 50%;\n  overflow: hidden;\n  box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0.05);\n}\n\n.pan-info h3 {\n  color: #fff;\n  text-transform: uppercase;\n  position: relative;\n  letter-spacing: 2px;\n  font-size: 18px;\n  margin: 0 60px;\n  padding: 22px 0 0 0;\n  height: 85px;\n  font-family: 'Open Sans', Arial, sans-serif;\n  text-shadow: 0 0 1px #fff, 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.pan-info p {\n  color: #fff;\n  padding: 10px 5px;\n  font-style: italic;\n  margin: 0 30px;\n  font-size: 12px;\n  border-top: 1px solid rgba(255, 255, 255, 0.5);\n}\n\n.pan-info p a {\n  display: block;\n  color: #333;\n  width: 80px;\n  height: 80px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  color: #fff;\n  font-style: normal;\n  font-weight: 700;\n  text-transform: uppercase;\n  font-size: 9px;\n  letter-spacing: 1px;\n  padding-top: 24px;\n  margin: 7px auto 0;\n  font-family: 'Open Sans', Arial, sans-serif;\n  opacity: 0;\n  transition: transform 0.3s ease-in-out 0.2s, opacity 0.3s ease-in-out 0.2s, background 0.2s linear 0s;\n  transform: translateX(60px) rotate(90deg);\n}\n\n.pan-info p a:hover {\n  background: rgba(255, 255, 255, 0.5);\n}\n\n.pan-item:hover .pan-thumb {\n  transform: rotate(-110deg);\n}\n\n.pan-item:hover .pan-info p a {\n  opacity: 1;\n  transform: translateX(0px) rotate(0deg);\n}\n</style>\n"], "mappings": ";AAaA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAN,IAAA,EAAAC,MAAA;MACAI,OAAA;IACA;IACAE,MAAA;MACAP,IAAA,EAAAC,MAAA;MACAI,OAAA;IACA;EACA;AACA", "ignoreList": []}]}