{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\TransmitDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\TransmitDetail.vue", "mtime": 1747799550426}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0xpbmVHcmFwaFZpZXcnLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBjaGFydDogbnVsbA0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICBjb25zb2xlLmxvZyg4OCkNCiAgICB0aGlzLmZldGNoRGF0YSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBmZXRjaERhdGEoKSB7DQogICAgICBjb25zdCBuYW1lID0gZGVjb2RlVVJJQ29tcG9uZW50KHRoaXMuJHJvdXRlLnF1ZXJ5Lm5hbWUgfHwgJycpDQogICAgICB0cnkgew0KICAgICAgICBjb25zb2xlLmxvZyg2NikNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2xpbmVfZ3JhcGgnLCB7DQogICAgICAgICAgdXNlcjogbmFtZSwNCiAgICAgICAgICB0aW1lX2NsYXNzOiAn5pyIJw0KICAgICAgICB9KQ0KICAgICAgICBjb25zdCB7IHRpbWVTcGFuLCB0cmFkZUFtb3VudCB9ID0gcmVzLmRhdGENCiAgICAgICAgY29uc29sZS5sb2codGltZVNwYW4pDQogICAgICAgIHRoaXMuaW5pdENoYXJ0KHRpbWVTcGFuLCB0cmFkZUFtb3VudCkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaVsOaNruWksei0pScsIGVycm9yKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaW5pdENoYXJ0KHhEYXRhLCB5RGF0YSkgew0KICAgICAgdGhpcy5jaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmNoYXJ0KQ0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0aXRsZTogew0KICAgICAgICAgIHRleHQ6ICfkuqTmmJPph5Hpop3lj5jljJblm74nDQogICAgICAgIH0sDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IHhEYXRhDQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn5Lqk5piT6YeR6aKdJw0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBkYXRhOiB5RGF0YSwNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgIG5hbWU6ICfkuqTmmJPph5Hpop0nLA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7fQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgICAgdGhpcy5jaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["TransmitDetail.vue"], "names": [], "mappings": ";AASA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TransmitDetail.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card>\r\n      <div ref=\"chart\" style=\"width: 100%; height: 400px;\" />\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'LineGraphView',\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    console.log(88)\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    async fetchData() {\r\n      const name = decodeURIComponent(this.$route.query.name || '')\r\n      try {\r\n        console.log(66)\r\n        const res = await axios.post('http://127.0.0.1:8000/line_graph', {\r\n          user: name,\r\n          time_class: '月'\r\n        })\r\n        const { timeSpan, tradeAmount } = res.data\r\n        console.log(timeSpan)\r\n        this.initChart(timeSpan, tradeAmount)\r\n      } catch (error) {\r\n        console.error('加载数据失败', error)\r\n      }\r\n    },\r\n    initChart(xData, yData) {\r\n      this.chart = echarts.init(this.$refs.chart)\r\n      const option = {\r\n        title: {\r\n          text: '交易金额变化图'\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xData\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '交易金额'\r\n        },\r\n        series: [\r\n          {\r\n            data: yData,\r\n            type: 'line',\r\n            smooth: true,\r\n            name: '交易金额',\r\n            areaStyle: {}\r\n          }\r\n        ]\r\n      }\r\n      this.chart.setOption(option)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}