{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\ErrorLog\\index.vue?vue&type=style&index=0&id=cf51e862&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\ErrorLog\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5tZXNzYWdlLXRpdGxlIHsKICBmb250LXNpemU6IDE2cHg7CiAgY29sb3I6ICMzMzM7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgcGFkZGluZy1yaWdodDogOHB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ErrorLog", "sourcesContent": ["<template>\n  <div v-if=\"errorLogs.length>0\">\n    <el-badge :is-dot=\"true\" style=\"line-height: 25px;margin-top: -5px;\" @click.native=\"dialogTableVisible=true\">\n      <el-button style=\"padding: 8px 10px;\" size=\"small\" type=\"danger\">\n        <svg-icon icon-class=\"bug\" />\n      </el-button>\n    </el-badge>\n\n    <el-dialog :visible.sync=\"dialogTableVisible\" width=\"80%\" append-to-body>\n      <div slot=\"title\">\n        <span style=\"padding-right: 10px;\">Error Log</span>\n        <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-delete\" @click=\"clearAll\">Clear All</el-button>\n      </div>\n      <el-table :data=\"errorLogs\" border>\n        <el-table-column label=\"Message\">\n          <template slot-scope=\"{row}\">\n            <div>\n              <span class=\"message-title\">Msg:</span>\n              <el-tag type=\"danger\">\n                {{ row.err.message }}\n              </el-tag>\n            </div>\n            <br>\n            <div>\n              <span class=\"message-title\" style=\"padding-right: 10px;\">Info: </span>\n              <el-tag type=\"warning\">\n                {{ row.vm.$vnode.tag }} error in {{ row.info }}\n              </el-tag>\n            </div>\n            <br>\n            <div>\n              <span class=\"message-title\" style=\"padding-right: 16px;\">Url: </span>\n              <el-tag type=\"success\">\n                {{ row.url }}\n              </el-tag>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"Stack\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.err.stack }}\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ErrorLog',\n  data() {\n    return {\n      dialogTableVisible: false\n    }\n  },\n  computed: {\n    errorLogs() {\n      return this.$store.getters.errorLogs\n    }\n  },\n  methods: {\n    clearAll() {\n      this.dialogTableVisible = false\n      this.$store.dispatch('errorLog/clearErrorLog')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.message-title {\n  font-size: 16px;\n  color: #333;\n  font-weight: bold;\n  padding-right: 8px;\n}\n</style>\n"]}]}