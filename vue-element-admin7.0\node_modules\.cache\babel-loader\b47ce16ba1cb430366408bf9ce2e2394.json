{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\settings.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\settings.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSB7CiAgdGl0bGU6ICdWdWUgRWxlbWVudCBBZG1pbicsCiAgLyoqCiAgICogQHR5cGUge2Jvb2xlYW59IHRydWUgfCBmYWxzZQogICAqIEBkZXNjcmlwdGlvbiBXaGV0aGVyIHNob3cgdGhlIHNldHRpbmdzIHJpZ2h0LXBhbmVsCiAgICovCiAgc2hvd1NldHRpbmdzOiB0cnVlLAogIC8qKgogICAqIEB0eXBlIHtib29sZWFufSB0cnVlIHwgZmFsc2UKICAgKiBAZGVzY3JpcHRpb24gV2hldGhlciBuZWVkIHRhZ3NWaWV3CiAgICovCiAgdGFnc1ZpZXc6IHRydWUsCiAgLyoqCiAgICogQHR5cGUge2Jvb2xlYW59IHRydWUgfCBmYWxzZQogICAqIEBkZXNjcmlwdGlvbiBXaGV0aGVyIGZpeCB0aGUgaGVhZGVyCiAgICovCiAgZml4ZWRIZWFkZXI6IGZhbHNlLAogIC8qKgogICAqIEB0eXBlIHtib29sZWFufSB0cnVlIHwgZmFsc2UKICAgKiBAZGVzY3JpcHRpb24gV2hldGhlciBzaG93IHRoZSBsb2dvIGluIHNpZGViYXIKICAgKi8KICBzaWRlYmFyTG9nbzogZmFsc2UsCiAgLyoqCiAgICogQHR5cGUge3N0cmluZyB8IGFycmF5fSAncHJvZHVjdGlvbicgfCBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQogICAqIEBkZXNjcmlwdGlvbiBOZWVkIHNob3cgZXJyIGxvZ3MgY29tcG9uZW50LgogICAqIFRoZSBkZWZhdWx0IGlzIG9ubHkgdXNlZCBpbiB0aGUgcHJvZHVjdGlvbiBlbnYKICAgKiBJZiB5b3Ugd2FudCB0byBhbHNvIHVzZSBpdCBpbiBkZXYsIHlvdSBjYW4gcGFzcyBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQogICAqLwogIGVycm9yTG9nOiAncHJvZHVjdGlvbicKfTs="}, {"version": 3, "names": ["module", "exports", "title", "showSettings", "tagsView", "fixedHeader", "sidebarLogo", "errorLog"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/settings.js"], "sourcesContent": ["module.exports = {\n  title: 'Vue Element Admin',\n\n  /**\n   * @type {boolean} true | false\n   * @description Whether show the settings right-panel\n   */\n  showSettings: true,\n\n  /**\n   * @type {boolean} true | false\n   * @description Whether need tagsView\n   */\n  tagsView: true,\n\n  /**\n   * @type {boolean} true | false\n   * @description Whether fix the header\n   */\n  fixedHeader: false,\n\n  /**\n   * @type {boolean} true | false\n   * @description Whether show the logo in sidebar\n   */\n  sidebarLogo: false,\n\n  /**\n   * @type {string | array} 'production' | ['production', 'development']\n   * @description Need show err logs component.\n   * The default is only used in the production env\n   * If you want to also use it in dev, you can pass ['production', 'development']\n   */\n  errorLog: 'production'\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACfC,KAAK,EAAE,mBAAmB;EAE1B;AACF;AACA;AACA;EACEC,YAAY,EAAE,IAAI;EAElB;AACF;AACA;AACA;EACEC,QAAQ,EAAE,IAAI;EAEd;AACF;AACA;AACA;EACEC,WAAW,EAAE,KAAK;EAElB;AACF;AACA;AACA;EACEC,WAAW,EAAE,KAAK;EAElB;AACF;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}]}