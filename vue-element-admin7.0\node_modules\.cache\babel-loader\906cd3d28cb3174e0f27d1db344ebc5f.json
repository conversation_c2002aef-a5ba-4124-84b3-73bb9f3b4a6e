{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\components\\mixins\\resize.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\components\\mixins\\resize.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["debounce", "data", "$_sidebarElm", "$_resizeHandler", "mounted", "_this", "chart", "resize", "$_initResizeEvent", "$_initSidebarResizeEvent", "<PERSON><PERSON><PERSON><PERSON>", "$_destroyResizeEvent", "$_destroySidebarResizeEvent", "activated", "deactivated", "methods", "window", "addEventListener", "removeEventListener", "$_sidebarResizeHandler", "e", "propertyName", "document", "getElementsByClassName"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/views/dashboard/admin/components/mixins/resize.js"], "sourcesContent": ["import { debounce } from '@/utils'\n\nexport default {\n  data() {\n    return {\n      $_sidebarElm: null,\n      $_resizeHandler: null\n    }\n  },\n  mounted() {\n    this.$_resizeHandler = debounce(() => {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }, 100)\n    this.$_initResizeEvent()\n    this.$_initSidebarResizeEvent()\n  },\n  beforeDestroy() {\n    this.$_destroyResizeEvent()\n    this.$_destroySidebarResizeEvent()\n  },\n  // to fixed bug when cached by keep-alive\n  // https://github.com/PanJiaChen/vue-element-admin/issues/2116\n  activated() {\n    this.$_initResizeEvent()\n    this.$_initSidebarResizeEvent()\n  },\n  deactivated() {\n    this.$_destroyResizeEvent()\n    this.$_destroySidebarResizeEvent()\n  },\n  methods: {\n    // use $_ for mixins properties\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\n    $_initResizeEvent() {\n      window.addEventListener('resize', this.$_resizeHandler)\n    },\n    $_destroyResizeEvent() {\n      window.removeEventListener('resize', this.$_resizeHandler)\n    },\n    $_sidebarResizeHandler(e) {\n      if (e.propertyName === 'width') {\n        this.$_resizeHandler()\n      }\n    },\n    $_initSidebarResizeEvent() {\n      this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0]\n      this.$_sidebarElm && this.$_sidebarElm.addEventListener('transitionend', this.$_sidebarResizeHandler)\n    },\n    $_destroySidebarResizeEvent() {\n      this.$_sidebarElm && this.$_sidebarElm.removeEventListener('transitionend', this.$_sidebarResizeHandler)\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS;AAElC,eAAe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,IAAI;MAClBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACF,eAAe,GAAGH,QAAQ,CAAC,YAAM;MACpC,IAAIK,KAAI,CAACC,KAAK,EAAE;QACdD,KAAI,CAACC,KAAK,CAACC,MAAM,CAAC,CAAC;MACrB;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACjC,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,2BAA2B,CAAC,CAAC;EACpC,CAAC;EACD;EACA;EACAC,SAAS,WAATA,SAASA,CAAA,EAAG;IACV,IAAI,CAACL,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACjC,CAAC;EACDK,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACH,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,2BAA2B,CAAC,CAAC;EACpC,CAAC;EACDG,OAAO,EAAE;IACP;IACA;IACAP,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClBQ,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACd,eAAe,CAAC;IACzD,CAAC;IACDQ,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrBK,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACf,eAAe,CAAC;IAC5D,CAAC;IACDgB,sBAAsB,WAAtBA,sBAAsBA,CAACC,CAAC,EAAE;MACxB,IAAIA,CAAC,CAACC,YAAY,KAAK,OAAO,EAAE;QAC9B,IAAI,CAAClB,eAAe,CAAC,CAAC;MACxB;IACF,CAAC;IACDM,wBAAwB,WAAxBA,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACP,YAAY,GAAGoB,QAAQ,CAACC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;MAC3E,IAAI,CAACrB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACe,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACE,sBAAsB,CAAC;IACvG,CAAC;IACDP,2BAA2B,WAA3BA,2BAA2BA,CAAA,EAAG;MAC5B,IAAI,CAACV,YAAY,IAAI,IAAI,CAACA,YAAY,CAACgB,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACC,sBAAsB,CAAC;IAC1G;EACF;AACF,CAAC", "ignoreList": []}]}