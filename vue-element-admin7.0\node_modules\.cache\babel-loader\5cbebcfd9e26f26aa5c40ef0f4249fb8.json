{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1747749564455}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFBhbmVsR3JvdXAgZnJvbSAnLi9jb21wb25lbnRzL1BhbmVsR3JvdXAnOwppbXBvcnQgTGluZUNoYXJ0IGZyb20gJy4vY29tcG9uZW50cy9MaW5lQ2hhcnQnOwp2YXIgbGluZUNoYXJ0RGF0YSA9IHsKICBuZXdWaXNpdGlzOiB7CiAgICBleHBlY3RlZERhdGE6IFsxMDAsIDEyMCwgMTYxLCAxMzQsIDEwNSwgMTYwLCAxNjVdLAogICAgYWN0dWFsRGF0YTogWzEyMCwgODIsIDkxLCAxNTQsIDE2MiwgMTQwLCAxNDVdCiAgfSwKICBtZXNzYWdlczogewogICAgZXhwZWN0ZWREYXRhOiBbMjAwLCAxOTIsIDEyMCwgMTQ0LCAxNjAsIDEzMCwgMTQwXSwKICAgIGFjdHVhbERhdGE6IFsxODAsIDE2MCwgMTUxLCAxMDYsIDE0NSwgMTUwLCAxMzBdCiAgfSwKICBwdXJjaGFzZXM6IHsKICAgIGV4cGVjdGVkRGF0YTogWzgwLCAxMDAsIDEyMSwgMTA0LCAxMDUsIDkwLCAxMDBdLAogICAgYWN0dWFsRGF0YTogWzEyMCwgOTAsIDEwMCwgMTM4LCAxNDIsIDEzMCwgMTMwXQogIH0sCiAgc2hvcHBpbmdzOiB7CiAgICBleHBlY3RlZERhdGE6IFsxMzAsIDE0MCwgMTQxLCAxNDIsIDE0NSwgMTUwLCAxNjBdLAogICAgYWN0dWFsRGF0YTogWzEyMCwgODIsIDkxLCAxNTQsIDE2MiwgMTQwLCAxMzBdCiAgfQp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Rhc2hib2FyZEFkbWluJywKICBjb21wb25lbnRzOiB7CiAgICBQYW5lbEdyb3VwOiBQYW5lbEdyb3VwLAogICAgTGluZUNoYXJ0OiBMaW5lQ2hhcnQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsaW5lQ2hhcnREYXRhOiBsaW5lQ2hhcnREYXRhLm5ld1Zpc2l0aXMKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZXRMaW5lQ2hhcnREYXRhOiBmdW5jdGlvbiBoYW5kbGVTZXRMaW5lQ2hhcnREYXRhKHR5cGUpIHsKICAgICAgdGhpcy5saW5lQ2hhcnREYXRhID0gbGluZUNoYXJ0RGF0YVt0eXBlXTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["PanelGroup", "Line<PERSON>hart", "lineChartData", "new<PERSON><PERSON><PERSON>", "expectedData", "actualData", "messages", "purchases", "shoppings", "name", "components", "data", "methods", "handleSetLineChartData", "type"], "sources": ["src/views/dashboard/admin/index.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-editor-container\">\n\n    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />\n\n    <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\n      <line-chart :chart-data=\"lineChartData\" />\n    </el-row>\n\n    <!-- <el-row :gutter=\"32\">\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <raddar-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <pie-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <bar-chart />\n        </div>\n      </el-col>\n    </el-row> -->\n\n    <!-- <el-row :gutter=\"8\">\n      <el-col :xs=\"{span: 24}\" :sm=\"{span: 24}\" :md=\"{span: 24}\" :lg=\"{span: 12}\" :xl=\"{span: 12}\" style=\"padding-right:8px;margin-bottom:30px;\">\n        <transaction-table />\n      </el-col>\n      <el-col :xs=\"{span: 24}\" :sm=\"{span: 12}\" :md=\"{span: 12}\" :lg=\"{span: 6}\" :xl=\"{span: 6}\" style=\"margin-bottom:30px;\">\n        <todo-list />\n      </el-col>\n      <el-col :xs=\"{span: 24}\" :sm=\"{span: 12}\" :md=\"{span: 12}\" :lg=\"{span: 6}\" :xl=\"{span: 6}\" style=\"margin-bottom:30px;\">\n        <box-card />\n      </el-col> -->\n    <!-- </el-row> -->\n  </div>\n</template>\n\n<script>\nimport PanelGroup from './components/PanelGroup'\nimport LineChart from './components/LineChart'\n\nconst lineChartData = {\n  newVisitis: {\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\n    actualData: [120, 82, 91, 154, 162, 140, 145]\n  },\n  messages: {\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\n    actualData: [180, 160, 151, 106, 145, 150, 130]\n  },\n  purchases: {\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\n    actualData: [120, 90, 100, 138, 142, 130, 130]\n  },\n  shoppings: {\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\n    actualData: [120, 82, 91, 154, 162, 140, 130]\n  }\n}\n\nexport default {\n  name: 'DashboardAdmin',\n  components: {\n    PanelGroup,\n    LineChart\n  },\n  data() {\n    return {\n      lineChartData: lineChartData.newVisitis\n    }\n  },\n  methods: {\n    handleSetLineChartData(type) {\n      this.lineChartData = lineChartData[type]\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-editor-container {\n  padding: 32px;\n  background-color: rgb(240, 242, 245);\n  position: relative;\n\n  .chart-wrapper {\n    background: #fff;\n    padding: 16px 16px 0;\n    margin-bottom: 32px;\n  }\n}\n\n@media (max-width:1024px) {\n  .chart-wrapper {\n    padding: 8px;\n  }\n}\n</style>\n"], "mappings": "AA0CA,OAAAA,UAAA;AACA,OAAAC,SAAA;AAEA,IAAAC,aAAA;EACAC,UAAA;IACAC,YAAA;IACAC,UAAA;EACA;EACAC,QAAA;IACAF,YAAA;IACAC,UAAA;EACA;EACAE,SAAA;IACAH,YAAA;IACAC,UAAA;EACA;EACAG,SAAA;IACAJ,YAAA;IACAC,UAAA;EACA;AACA;AAEA;EACAI,IAAA;EACAC,UAAA;IACAV,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAT,aAAA,EAAAA,aAAA,CAAAC;IACA;EACA;EACAS,OAAA;IACAC,sBAAA,WAAAA,uBAAAC,IAAA;MACA,KAAAZ,aAAA,GAAAA,aAAA,CAAAY,IAAA;IACA;EACA;AACA", "ignoreList": []}]}