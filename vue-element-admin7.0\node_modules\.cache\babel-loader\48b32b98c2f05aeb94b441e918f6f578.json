{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\error-log.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\error-log.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CmltcG9ydCBzdG9yZSBmcm9tICdAL3N0b3JlJzsKaW1wb3J0IHsgaXNTdHJpbmcsIGlzQXJyYXkgfSBmcm9tICdAL3V0aWxzL3ZhbGlkYXRlJzsKaW1wb3J0IHNldHRpbmdzIGZyb20gJ0Avc2V0dGluZ3MnOwoKLy8geW91IGNhbiBzZXQgaW4gc2V0dGluZ3MuanMKLy8gZXJyb3JMb2c6J3Byb2R1Y3Rpb24nIHwgWydwcm9kdWN0aW9uJywgJ2RldmVsb3BtZW50J10KdmFyIG5lZWRFcnJvckxvZyA9IHNldHRpbmdzLmVycm9yTG9nOwpmdW5jdGlvbiBjaGVja05lZWQoKSB7CiAgdmFyIGVudiA9IHByb2Nlc3MuZW52Lk5PREVfRU5WOwogIGlmIChpc1N0cmluZyhuZWVkRXJyb3JMb2cpKSB7CiAgICByZXR1cm4gZW52ID09PSBuZWVkRXJyb3JMb2c7CiAgfQogIGlmIChpc0FycmF5KG5lZWRFcnJvckxvZykpIHsKICAgIHJldHVybiBuZWVkRXJyb3JMb2cuaW5jbHVkZXMoZW52KTsKICB9CiAgcmV0dXJuIGZhbHNlOwp9CmlmIChjaGVja05lZWQoKSkgewogIFZ1ZS5jb25maWcuZXJyb3JIYW5kbGVyID0gZnVuY3Rpb24gKGVyciwgdm0sIGluZm8sIGEpIHsKICAgIC8vIERvbid0IGFzayBtZSB3aHkgSSB1c2UgVnVlLm5leHRUaWNrLCBpdCBqdXN0IGEgaGFjay4KICAgIC8vIGRldGFpbCBzZWUgaHR0cHM6Ly9mb3J1bS52dWVqcy5vcmcvdC9kaXNwYXRjaC1pbi12dWUtY29uZmlnLWVycm9yaGFuZGxlci1oYXMtc29tZS1wcm9ibGVtLzIzNTAwCiAgICBWdWUubmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICBzdG9yZS5kaXNwYXRjaCgnZXJyb3JMb2cvYWRkRXJyb3JMb2cnLCB7CiAgICAgICAgZXJyOiBlcnIsCiAgICAgICAgdm06IHZtLAogICAgICAgIGluZm86IGluZm8sCiAgICAgICAgdXJsOiB3aW5kb3cubG9jYXRpb24uaHJlZgogICAgICB9KTsKICAgICAgY29uc29sZS5lcnJvcihlcnIsIGluZm8pOwogICAgfSk7CiAgfTsKfQ=="}, {"version": 3, "names": ["<PERSON><PERSON>", "store", "isString", "isArray", "settings", "needErrorLog", "errorLog", "checkNeed", "env", "process", "NODE_ENV", "includes", "config", "<PERSON><PERSON><PERSON><PERSON>", "err", "vm", "info", "a", "nextTick", "dispatch", "url", "window", "location", "href", "console", "error"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/utils/error-log.js"], "sourcesContent": ["import Vue from 'vue'\nimport store from '@/store'\nimport { isString, isArray } from '@/utils/validate'\nimport settings from '@/settings'\n\n// you can set in settings.js\n// errorLog:'production' | ['production', 'development']\nconst { errorLog: needErrorLog } = settings\n\nfunction checkNeed() {\n  const env = process.env.NODE_ENV\n  if (isString(needErrorLog)) {\n    return env === needErrorLog\n  }\n  if (isArray(needErrorLog)) {\n    return needErrorLog.includes(env)\n  }\n  return false\n}\n\nif (checkNeed()) {\n  Vue.config.errorHandler = function(err, vm, info, a) {\n  // Don't ask me why I use Vue.nextTick, it just a hack.\n  // detail see https://forum.vuejs.org/t/dispatch-in-vue-config-errorhandler-has-some-problem/23500\n    Vue.nextTick(() => {\n      store.dispatch('errorLog/addErrorLog', {\n        err,\n        vm,\n        info,\n        url: window.location.href\n      })\n      console.error(err, info)\n    })\n  }\n}\n"], "mappings": ";;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,EAAEC,OAAO,QAAQ,kBAAkB;AACpD,OAAOC,QAAQ,MAAM,YAAY;;AAEjC;AACA;AACA,IAAkBC,YAAY,GAAKD,QAAQ,CAAnCE,QAAQ;AAEhB,SAASC,SAASA,CAAA,EAAG;EACnB,IAAMC,GAAG,GAAGC,OAAO,CAACD,GAAG,CAACE,QAAQ;EAChC,IAAIR,QAAQ,CAACG,YAAY,CAAC,EAAE;IAC1B,OAAOG,GAAG,KAAKH,YAAY;EAC7B;EACA,IAAIF,OAAO,CAACE,YAAY,CAAC,EAAE;IACzB,OAAOA,YAAY,CAACM,QAAQ,CAACH,GAAG,CAAC;EACnC;EACA,OAAO,KAAK;AACd;AAEA,IAAID,SAAS,CAAC,CAAC,EAAE;EACfP,GAAG,CAACY,MAAM,CAACC,YAAY,GAAG,UAASC,GAAG,EAAEC,EAAE,EAAEC,IAAI,EAAEC,CAAC,EAAE;IACrD;IACA;IACEjB,GAAG,CAACkB,QAAQ,CAAC,YAAM;MACjBjB,KAAK,CAACkB,QAAQ,CAAC,sBAAsB,EAAE;QACrCL,GAAG,EAAHA,GAAG;QACHC,EAAE,EAAFA,EAAE;QACFC,IAAI,EAAJA,IAAI;QACJI,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACvB,CAAC,CAAC;MACFC,OAAO,CAACC,KAAK,CAACX,GAAG,EAAEE,IAAI,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}]}