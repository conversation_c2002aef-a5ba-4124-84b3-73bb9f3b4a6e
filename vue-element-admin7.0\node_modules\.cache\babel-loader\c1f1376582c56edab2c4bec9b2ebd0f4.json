{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Pagination\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Pagination\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["scrollTo", "name", "props", "total", "required", "type", "Number", "page", "default", "limit", "pageSizes", "Array", "layout", "String", "background", "Boolean", "autoScroll", "hidden", "computed", "currentPage", "get", "set", "val", "$emit", "pageSize", "methods", "handleSizeChange", "handleCurrentChange"], "sources": ["src/components/Pagination/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\n    <el-pagination\n      :background=\"background\"\n      :current-page.sync=\"currentPage\"\n      :page-size.sync=\"pageSize\"\n      :layout=\"layout\"\n      :page-sizes=\"pageSizes\"\n      :total=\"total\"\n      v-bind=\"$attrs\"\n      @size-change=\"handleSizeChange\"\n      @current-change=\"handleCurrentChange\"\n    />\n  </div>\n</template>\n\n<script>\nimport { scrollTo } from '@/utils/scroll-to'\n\nexport default {\n  name: 'Pagination',\n  props: {\n    total: {\n      required: true,\n      type: Number\n    },\n    page: {\n      type: Number,\n      default: 1\n    },\n    limit: {\n      type: Number,\n      default: 20\n    },\n    pageSizes: {\n      type: Array,\n      default() {\n        return [10, 20, 30, 50]\n      }\n    },\n    layout: {\n      type: String,\n      default: 'total, sizes, prev, pager, next, jumper'\n    },\n    background: {\n      type: Boolean,\n      default: true\n    },\n    autoScroll: {\n      type: Boolean,\n      default: true\n    },\n    hidden: {\n      type: <PERSON>olean,\n      default: false\n    }\n  },\n  computed: {\n    currentPage: {\n      get() {\n        return this.page\n      },\n      set(val) {\n        this.$emit('update:page', val)\n      }\n    },\n    pageSize: {\n      get() {\n        return this.limit\n      },\n      set(val) {\n        this.$emit('update:limit', val)\n      }\n    }\n  },\n  methods: {\n    handleSizeChange(val) {\n      this.$emit('pagination', { page: this.currentPage, limit: val })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    },\n    handleCurrentChange(val) {\n      this.$emit('pagination', { page: val, limit: this.pageSize })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  background: #fff;\n  padding: 32px 16px;\n}\n.pagination-container.hidden {\n  display: none;\n}\n</style>\n"], "mappings": ";AAiBA,SAAAA,QAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,QAAA;MACAC,IAAA,EAAAC;IACA;IACAC,IAAA;MACAF,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAC,KAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAE,SAAA;MACAL,IAAA,EAAAM,KAAA;MACAH,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAI,MAAA;MACAP,IAAA,EAAAQ,MAAA;MACAL,OAAA;IACA;IACAM,UAAA;MACAT,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAQ,UAAA;MACAX,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAS,MAAA;MACAZ,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;EACA;EACAU,QAAA;IACAC,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAb,IAAA;MACA;MACAc,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,gBAAAD,GAAA;MACA;IACA;IACAE,QAAA;MACAJ,GAAA,WAAAA,IAAA;QACA,YAAAX,KAAA;MACA;MACAY,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,iBAAAD,GAAA;MACA;IACA;EACA;EACAG,OAAA;IACAC,gBAAA,WAAAA,iBAAAJ,GAAA;MACA,KAAAC,KAAA;QAAAhB,IAAA,OAAAY,WAAA;QAAAV,KAAA,EAAAa;MAAA;MACA,SAAAN,UAAA;QACAhB,QAAA;MACA;IACA;IACA2B,mBAAA,WAAAA,oBAAAL,GAAA;MACA,KAAAC,KAAA;QAAAhB,IAAA,EAAAe,GAAA;QAAAb,KAAA,OAAAe;MAAA;MACA,SAAAR,UAAA;QACAhB,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}