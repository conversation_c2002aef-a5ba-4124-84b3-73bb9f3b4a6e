{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Breadcrumb\\index.vue", "mtime": 1734248228000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBwYXRoVG9SZWdleHAgZnJvbSAncGF0aC10by1yZWdleHAnCgpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxldmVsTGlzdDogbnVsbAogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZShyb3V0ZSkgewogICAgICAvLyBpZiB5b3UgZ28gdG8gdGhlIHJlZGlyZWN0IHBhZ2UsIGRvIG5vdCB1cGRhdGUgdGhlIGJyZWFkY3J1bWJzCiAgICAgIGlmIChyb3V0ZS5wYXRoLnN0YXJ0c1dpdGgoJy9yZWRpcmVjdC8nKSkgewogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRCcmVhZGNydW1iKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldEJyZWFkY3J1bWIoKSB7CiAgICAgIC8vIG9ubHkgc2hvdyByb3V0ZXMgd2l0aCBtZXRhLnRpdGxlCiAgICAgIGxldCBtYXRjaGVkID0gdGhpcy4kcm91dGUubWF0Y2hlZC5maWx0ZXIoaXRlbSA9PiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlKQogICAgICBjb25zdCBmaXJzdCA9IG1hdGNoZWRbMF0KCiAgICAgIGlmICghdGhpcy5pc0Rhc2hib2FyZChmaXJzdCkpIHsKICAgICAgICBtYXRjaGVkID0gW3sgcGF0aDogJy9kYXNoYm9hcmQnLCBtZXRhOiB7IHRpdGxlOiAn5Zyw5LiL6ZKx5bqE5aSn5pWw5o2u5pm66IO95YiG5p6Q57O757ufJyB9fV0uY29uY2F0KG1hdGNoZWQpCiAgICAgIH0KCiAgICAgIHRoaXMubGV2ZWxMaXN0ID0gbWF0Y2hlZC5maWx0ZXIoaXRlbSA9PiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlICYmIGl0ZW0ubWV0YS5icmVhZGNydW1iICE9PSBmYWxzZSkKICAgIH0sCiAgICBpc0Rhc2hib2FyZChyb3V0ZSkgewogICAgICBjb25zdCBuYW1lID0gcm91dGUgJiYgcm91dGUubmFtZQogICAgICBpZiAoIW5hbWUpIHsKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQogICAgICByZXR1cm4gbmFtZS50cmltKCkudG9Mb2NhbGVMb3dlckNhc2UoKSA9PT0gJ0Rhc2hib2FyZCcudG9Mb2NhbGVMb3dlckNhc2UoKQogICAgfSwKICAgIHBhdGhDb21waWxlKHBhdGgpIHsKICAgICAgLy8gVG8gc29sdmUgdGhpcyBwcm9ibGVtIGh0dHBzOi8vZ2l0aHViLmNvbS9QYW5KaWFDaGVuL3Z1ZS1lbGVtZW50LWFkbWluL2lzc3Vlcy81NjEKICAgICAgY29uc3QgeyBwYXJhbXMgfSA9IHRoaXMuJHJvdXRlCiAgICAgIHZhciB0b1BhdGggPSBwYXRoVG9SZWdleHAuY29tcGlsZShwYXRoKQogICAgICByZXR1cm4gdG9QYXRoKHBhcmFtcykKICAgIH0sCiAgICBoYW5kbGVMaW5rKGl0ZW0pIHsKICAgICAgY29uc3QgeyByZWRpcmVjdCwgcGF0aCB9ID0gaXRlbQogICAgICBpZiAocmVkaXJlY3QpIHsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChyZWRpcmVjdCkKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh0aGlzLnBhdGhDb21waWxlKHBhdGgpKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAYA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n    <transition-group name=\"breadcrumb\">\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\n      </el-breadcrumb-item>\n    </transition-group>\n  </el-breadcrumb>\n</template>\n\n<script>\nimport pathToRegexp from 'path-to-regexp'\n\nexport default {\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return\n      }\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\n      const first = matched[0]\n\n      if (!this.isDashboard(first)) {\n        matched = [{ path: '/dashboard', meta: { title: '地下钱庄大数据智能分析系统' }}].concat(matched)\n      }\n\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    isDashboard(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()\n    },\n    pathCompile(path) {\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\n      const { params } = this.$route\n      var toPath = pathToRegexp.compile(path)\n      return toPath(params)\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      if (redirect) {\n        this.$router.push(redirect)\n        return\n      }\n      this.$router.push(this.pathCompile(path))\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-breadcrumb.el-breadcrumb {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 50px;\n  margin-left: 8px;\n\n  .no-redirect {\n    color: #97a8be;\n    cursor: text;\n  }\n}\n</style>\n"]}]}