{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\components\\PanelGroup.vue?vue&type=style&index=0&id=48c369af&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\components\\PanelGroup.vue", "mtime": 1731856974000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PanelGroup.vue"], "names": [], "mappings": ";AAyEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PanelGroup.vue", "sourceRoot": "src/views/dashboard/admin/components", "sourcesContent": ["<template>\n  <el-row :gutter=\"40\" class=\"panel-group\">\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('newVisitis')\">\n        <div class=\"card-panel-icon-wrapper icon-people\">\n          <svg-icon icon-class=\"peoples\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            用户数\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"102400\" :duration=\"2600\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('messages')\">\n        <div class=\"card-panel-icon-wrapper icon-message\">\n          <svg-icon icon-class=\"shopping\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            近七日活跃用户数\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"81212\" :duration=\"3000\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('purchases')\">\n        <div class=\"card-panel-icon-wrapper icon-money\">\n          <svg-icon icon-class=\"money\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            近七日交易流水量\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"9280\" :duration=\"3200\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('shoppings')\">\n        <div class=\"card-panel-icon-wrapper icon-shopping\">\n          <svg-icon icon-class=\"message\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            近七日交易记录数\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"13600\" :duration=\"3600\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n  </el-row>\n</template>\n\n<script>\nimport CountTo from 'vue-count-to'\n\nexport default {\n  components: {\n    CountTo\n  },\n  methods: {\n    handleSetLineChartData(type) {\n      this.$emit('handleSetLineChartData', type)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.panel-group {\n  margin-top: 18px;\n\n  .card-panel-col {\n    margin-bottom: 32px;\n  }\n\n  .card-panel {\n    height: 108px;\n    cursor: pointer;\n    font-size: 12px;\n    position: relative;\n    overflow: hidden;\n    color: #666;\n    background: #fff;\n    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);\n    border-color: rgba(0, 0, 0, .05);\n\n    &:hover {\n      .card-panel-icon-wrapper {\n        color: #fff;\n      }\n\n      .icon-people {\n        background: #40c9c6;\n      }\n\n      .icon-message {\n        background: #36a3f7;\n      }\n\n      .icon-money {\n        background: #f4516c;\n      }\n\n      .icon-shopping {\n        background: #34bfa3\n      }\n    }\n\n    .icon-people {\n      color: #40c9c6;\n    }\n\n    .icon-message {\n      color: #36a3f7;\n    }\n\n    .icon-money {\n      color: #f4516c;\n    }\n\n    .icon-shopping {\n      color: #34bfa3\n    }\n\n    .card-panel-icon-wrapper {\n      float: left;\n      margin: 14px 0 0 14px;\n      padding: 16px;\n      transition: all 0.38s ease-out;\n      border-radius: 6px;\n    }\n\n    .card-panel-icon {\n      float: left;\n      font-size: 48px;\n    }\n\n    .card-panel-description {\n      float: right;\n      font-weight: bold;\n      margin: 26px;\n      margin-left: 0px;\n\n      .card-panel-text {\n        line-height: 18px;\n        color: rgba(0, 0, 0, 0.45);\n        font-size: 16px;\n        margin-bottom: 12px;\n      }\n\n      .card-panel-num {\n        font-size: 20px;\n      }\n    }\n  }\n}\n\n@media (max-width:550px) {\n  .card-panel-description {\n    display: none;\n  }\n\n  .card-panel-icon-wrapper {\n    float: none !important;\n    width: 100%;\n    height: 100%;\n    margin: 0 !important;\n\n    .svg-icon {\n      display: block;\n      margin: 14px auto !important;\n      float: none !important;\n    }\n  }\n}\n</style>\n"]}]}