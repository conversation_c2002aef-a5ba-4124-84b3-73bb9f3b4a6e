{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Screenfull\\index.vue", "mtime": 1731739938000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBzY3JlZW5mdWxsIGZyb20gJ3NjcmVlbmZ1bGwnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NjcmVlbmZ1bGwnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5pbml0KCkKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLmRlc3Ryb3koKQogIH0sCiAgbWV0aG9kczogewogICAgY2xpY2soKSB7CiAgICAgIGlmICghc2NyZWVuZnVsbC5lbmFibGVkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAneW91IGJyb3dzZXIgY2FuIG5vdCB3b3JrJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgc2NyZWVuZnVsbC50b2dnbGUoKQogICAgfSwKICAgIGNoYW5nZSgpIHsKICAgICAgdGhpcy5pc0Z1bGxzY3JlZW4gPSBzY3JlZW5mdWxsLmlzRnVsbHNjcmVlbgogICAgfSwKICAgIGluaXQoKSB7CiAgICAgIGlmIChzY3JlZW5mdWxsLmVuYWJsZWQpIHsKICAgICAgICBzY3JlZW5mdWxsLm9uKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSkKICAgICAgfQogICAgfSwKICAgIGRlc3Ryb3koKSB7CiAgICAgIGlmIChzY3JlZW5mdWxsLmVuYWJsZWQpIHsKICAgICAgICBzY3JlZW5mdWxsLm9mZignY2hhbmdlJywgdGhpcy5jaGFuZ2UpCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Screenfull", "sourcesContent": ["<template>\n  <div>\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\n  </div>\n</template>\n\n<script>\nimport screenfull from 'screenfull'\n\nexport default {\n  name: 'Screenfull',\n  data() {\n    return {\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.destroy()\n  },\n  methods: {\n    click() {\n      if (!screenfull.enabled) {\n        this.$message({\n          message: 'you browser can not work',\n          type: 'warning'\n        })\n        return false\n      }\n      screenfull.toggle()\n    },\n    change() {\n      this.isFullscreen = screenfull.isFullscreen\n    },\n    init() {\n      if (screenfull.enabled) {\n        screenfull.on('change', this.change)\n      }\n    },\n    destroy() {\n      if (screenfull.enabled) {\n        screenfull.off('change', this.change)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.screenfull-svg {\n  display: inline-block;\n  cursor: pointer;\n  fill: #5a5e66;;\n  width: 20px;\n  height: 20px;\n  vertical-align: 10px;\n}\n</style>\n"]}]}