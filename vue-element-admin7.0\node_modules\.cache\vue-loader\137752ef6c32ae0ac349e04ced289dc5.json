{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749179875229}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";AA2LA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>本地文件上传</h3>\r\n<p class=\"section-desc\">上传Excel文件到系统中</p>\r\n</div>\r\n<el-upload\r\n  ref=\"upload\"\r\n  class=\"upload-demo\"\r\n  drag\r\n  action=\"\"\r\n  :auto-upload=\"false\"\r\n  :on-change=\"handleFileChange\"\r\n  :file-list=\"uploadFileList\"\r\n  accept=\".xlsx,.xls\"\r\n  multiple\r\n>\r\n  <i class=\"el-icon-upload\"></i>\r\n  <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n  <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件</div>\r\n</el-upload>\r\n<div class=\"upload-actions\">\r\n  <el-button\r\n    type=\"primary\"\r\n    :loading=\"uploading\"\r\n    :disabled=\"uploadFileList.length === 0\"\r\n    @click=\"uploadFiles\"\r\n  >\r\n    {{ uploading ? '上传中...' : '开始上传' }}\r\n  </el-button>\r\n  <el-button @click=\"clearUploadFiles\">清空文件</el-button>\r\n</div>\r\n</div>\r\n\r\n<div class=\"file-selection-container\">\r\n<!-- 文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择需要检测的文件</h3>\r\n<p class=\"section-desc\">从数据库中选择要进行异常检测的文件</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"fileTable\"\r\n:data=\"availableFiles\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"200\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"300\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-document\" />\r\n<span style=\"margin-left: 8px;\">{{ row.fileName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template>\r\n<el-tag type=\"success\" size=\"small\">可用</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择文件显示 -->\r\n<div v-if=\"selectedFiles.length > 0\" class=\"selected-files-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedFiles.length }} 个文件</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n<div class=\"selected-files-list\">\r\n<el-tag\r\nv-for=\"file in selectedFiles\"\r\n:key=\"file.id\"\r\nclosable\r\nstyle=\"margin: 4px;\"\r\n@close=\"removeSelectedFile(file)\"\r\n>\r\n{{ file.fileName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n<!-- 操作按钮区域 -->\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"processSelectedFiles\"\r\n>\r\n{{ processing ? '检测中...' : '开始异常检测' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedFiles.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"processing\" class=\"progress-section\">\r\n<el-progress\r\n:percentage=\"processProgress\"\r\n:status=\"processProgress === 100 ? 'success' : ''\"\r\n:stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<!-- 异常检测结果展示 -->\r\n<div v-if=\"exceptionResults && Object.keys(exceptionResults).length > 0\" class=\"results-container\">\r\n<el-card class=\"box-card\">\r\n<div slot=\"header\" class=\"clearfix\">\r\n<span>异常检测结果</span>\r\n<span class=\"result-summary\">共发现 {{ getTotalExceptions() }} 条异常记录</span>\r\n</div>\r\n\r\n<!-- 异常类型标签页 -->\r\n<el-tabs v-model=\"activeTab\" type=\"card\">\r\n<el-tab-pane\r\n  v-for=\"(data, type) in exceptionResults\"\r\n  :key=\"type\"\r\n  :label=\"`${type} (${data.length})`\"\r\n  :name=\"type\"\r\n>\r\n<div class=\"scroll-container\">\r\n<div class=\"custom-scrollbar\">\r\n<el-table\r\n:data=\"data\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nmax-height=\"400\"\r\n>\r\n<el-table-column prop=\"订单号\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"支付人姓名\" label=\"支付人姓名\" width=\"120\" />\r\n<el-table-column prop=\"支付人身份证号\" label=\"支付人身份证号\" width=\"200\" />\r\n<el-table-column prop=\"物流单号\" label=\"物流单号\" width=\"180\" />\r\n</el-table>\r\n</div>\r\n</div>\r\n</el-tab-pane>\r\n</el-tabs>\r\n</el-card>\r\n</div>\r\n\r\n<!-- 空状态提示 -->\r\n<div v-else class=\"empty-state\">\r\n<div class=\"empty-content\">\r\n<i class=\"el-icon-document-remove empty-icon\"></i>\r\n<p class=\"empty-text\">请选择文件并进行异常检测</p>\r\n<el-button type=\"primary\" @click=\"loadAvailableFiles\">刷新文件列表</el-button>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n\r\n      // 文件选择相关\r\n      availableFiles: [],\r\n      selectedFiles: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常检测结果\r\n      exceptionResults: {},\r\n      activeTab: ''\r\n    }\r\n  },\r\n  mounted() {\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件')\r\n    },\r\n\r\n    async uploadFiles() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      try {\r\n        // 这里可以实现真实的文件上传逻辑\r\n        // 目前只是模拟上传过程\r\n        await new Promise(resolve => setTimeout(resolve, 2000))\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n        // 上传完成后刷新文件列表\r\n        this.loadAvailableFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.$message.error('文件上传失败')\r\n      } finally {\r\n        this.uploading = false\r\n      }\r\n    },\r\n\r\n    // 加载可用文件列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      console.log('开始加载文件列表...')\r\n\r\n      try {\r\n        console.log('发送请求到: http://127.0.0.1:8000/get_all_TrackingNum')\r\n        const response = await axios.get('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端响应:', response)\r\n        console.log('响应数据:', response.data)\r\n\r\n        const paths = response.data.paths || []\r\n        console.log('获取到的路径数组:', paths)\r\n\r\n        // 将路径转换为文件对象\r\n        this.availableFiles = paths.map((path, index) => {\r\n          const fileName = path.split('\\\\').pop() || path.split('/').pop()\r\n          return {\r\n            id: index + 1,\r\n            fileName: fileName,\r\n            fullPath: path\r\n          }\r\n        })\r\n\r\n        console.log('转换后的文件列表:', this.availableFiles)\r\n        this.$message.success(`加载完成，共找到 ${this.availableFiles.length} 个文件`)\r\n      } catch (error) {\r\n        console.error('加载文件列表失败:', error)\r\n        console.error('错误详情:', error.response)\r\n        if (error.response) {\r\n          this.$message.error(`请求失败: ${error.response.status} - ${error.response.statusText}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络错误：无法连接到后端服务，请检查后端是否运行在 http://127.0.0.1:8000')\r\n        } else {\r\n          this.$message.error(`请求配置错误: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n      console.log('已选择文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的文件\r\n    removeSelectedFile(file) {\r\n      const index = this.selectedFiles.findIndex(f => f.id === file.id)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.toggleRowSelection(file, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedFiles = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const table = this.$refs.fileTable\r\n        if (table) {\r\n          table.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空文件选择')\r\n    },\r\n\r\n    // 处理选中的文件\r\n    async processSelectedFiles() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要处理的文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始异常检测...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 90) {\r\n            this.processProgress += Math.random() * 15\r\n            const currentStep = Math.floor(this.processProgress / 30)\r\n            const steps = ['正在读取文件...', '正在合并数据...', '正在检测异常...']\r\n            this.progressText = steps[currentStep] || '检测中...'\r\n          }\r\n        }, 300)\r\n\r\n        // 调用后端API进行异常检测\r\n        const filePaths = this.selectedFiles.map(f => f.fullPath)\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '异常检测完成！'\r\n\r\n        // 处理后端返回的异常结果\r\n        this.exceptionResults = response.data || {}\r\n\r\n        // 设置默认激活的标签页\r\n        const resultKeys = Object.keys(this.exceptionResults)\r\n        if (resultKeys.length > 0) {\r\n          this.activeTab = resultKeys[0]\r\n        }\r\n\r\n        const totalExceptions = this.getTotalExceptions()\r\n        this.$message.success(`成功检测 ${this.selectedFiles.length} 个文件，发现 ${totalExceptions} 条异常记录`)\r\n      } catch (error) {\r\n        console.error('检测失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n        this.$message.error(`异常检测失败: ${error.message || '请检查后端服务是否正常'}`)\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 计算异常总数\r\n    getTotalExceptions() {\r\n      let total = 0\r\n      for (const key in this.exceptionResults) {\r\n        if (Array.isArray(this.exceptionResults[key])) {\r\n          total += this.exceptionResults[key].length\r\n        }\r\n      }\r\n      return total\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 文件上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f0f9ff;\r\n  border-radius: 8px;\r\n  border: 1px solid #b3d8ff;\r\n}\r\n\r\n.upload-demo {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 文件选择容器样式 */\r\n.file-selection-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 20px;\r\n  height:250px;\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n  height:-10px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择文件区域 */\r\n.selected-files-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.selected-files-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 12px 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.progress-text {\r\n  margin: 10px 0 0 0;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n}\r\n\r\n/* 结果展示区域样式 */\r\n.results-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.result-summary {\r\n  float: right;\r\n  color: #409eff;\r\n  font-weight: 600;\r\n}\r\n\r\n.empty-state {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n  padding: 40px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.empty-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 64px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  margin: 0 0 20px 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.scroll-container {\r\n  height: 600px;\r\n  position: relative;\r\n}\r\n\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-buttons .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}