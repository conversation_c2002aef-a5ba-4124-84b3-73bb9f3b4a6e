{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgaXNFeHRlcm5hbCBhcyBfaXNFeHRlcm5hbCB9IGZyb20gJ0AvdXRpbHMvdmFsaWRhdGUnOwpleHBvcnQgZGVmYXVsdCB7CiAgcHJvcHM6IHsKICAgIHRvOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsOiBmdW5jdGlvbiBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gX2lzRXh0ZXJuYWwodGhpcy50byk7CiAgICB9LAogICAgdHlwZTogZnVuY3Rpb24gdHlwZSgpIHsKICAgICAgaWYgKHRoaXMuaXNFeHRlcm5hbCkgewogICAgICAgIHJldHVybiAnYSc7CiAgICAgIH0KICAgICAgcmV0dXJuICdyb3V0ZXItbGluayc7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBsaW5rUHJvcHM6IGZ1bmN0aW9uIGxpbmtQcm9wcyh0bykgewogICAgICBpZiAodGhpcy5pc0V4dGVybmFsKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGhyZWY6IHRvLAogICAgICAgICAgdGFyZ2V0OiAnX2JsYW5rJywKICAgICAgICAgIHJlbDogJ25vb3BlbmVyJwogICAgICAgIH07CiAgICAgIH0KICAgICAgcmV0dXJuIHsKICAgICAgICB0bzogdG8KICAgICAgfTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["isExternal", "props", "to", "type", "String", "required", "computed", "methods", "linkProps", "href", "target", "rel"], "sources": ["src/layout/components/Sidebar/Link.vue"], "sourcesContent": ["<template>\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\n    <slot />\n  </component>\n</template>\n\n<script>\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  props: {\n    to: {\n      type: String,\n      required: true\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.to)\n    },\n    type() {\n      if (this.isExternal) {\n        return 'a'\n      }\n      return 'router-link'\n    }\n  },\n  methods: {\n    linkProps(to) {\n      if (this.isExternal) {\n        return {\n          href: to,\n          target: '_blank',\n          rel: 'noopener'\n        }\n      }\n      return {\n        to: to\n      }\n    }\n  }\n}\n</script>\n"], "mappings": "AAOA,SAAAA,UAAA,IAAAA,WAAA;AAEA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAN,UAAA,WAAAA,WAAA;MACA,OAAAA,WAAA,MAAAE,EAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA,SAAAH,UAAA;QACA;MACA;MACA;IACA;EACA;EACAO,OAAA;IACAC,SAAA,WAAAA,UAAAN,EAAA;MACA,SAAAF,UAAA;QACA;UACAS,IAAA,EAAAP,EAAA;UACAQ,MAAA;UACAC,GAAA;QACA;MACA;MACA;QACAT,EAAA,EAAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}