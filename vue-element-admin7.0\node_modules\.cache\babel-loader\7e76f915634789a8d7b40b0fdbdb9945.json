{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\permission.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\permission.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["router", "store", "Message", "NProgress", "getToken", "getPageTitle", "configure", "showSpinner", "whiteList", "beforeEach", "_ref", "_asyncToGenerator", "_regenerator", "m", "_callee", "to", "from", "next", "hasToken", "hasRoles", "_yield$store$dispatch", "roles", "accessRoutes", "_t", "w", "_context", "n", "start", "document", "title", "meta", "path", "done", "getters", "length", "p", "dispatch", "v", "addRoutes", "_objectSpread", "replace", "error", "concat", "indexOf", "a", "_x", "_x2", "_x3", "apply", "arguments", "after<PERSON>ach"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/permission.js"], "sourcesContent": ["import router from './router'\nimport store from './store'\nimport { Message } from 'element-ui'\nimport NProgress from 'nprogress' // progress bar\nimport 'nprogress/nprogress.css' // progress bar style\nimport { getToken } from '@/utils/auth' // get token from cookie\nimport getPageTitle from '@/utils/get-page-title'\n\nNProgress.configure({ showSpinner: false }) // NProgress Configuration\n\nconst whiteList = ['/login', '/auth-redirect'] // no redirect whitelist\n\nrouter.beforeEach(async(to, from, next) => {\n  // start progress bar\n  NProgress.start()\n\n  // set page title\n  document.title = getPageTitle(to.meta.title)\n\n  // determine whether the user has logged in\n  const hasToken = getToken()\n\n  if (hasToken) {\n    if (to.path === '/login') {\n      // if is logged in, redirect to the home page\n      next({ path: '/' })\n      NProgress.done() // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939\n    } else {\n      // determine whether the user has obtained his permission roles through getInfo\n      const hasRoles = store.getters.roles && store.getters.roles.length > 0\n      if (hasRoles) {\n        next()\n      } else {\n        try {\n          // get user info\n          // note: roles must be a object array! such as: ['admin'] or ,['developer','editor']\n          const { roles } = await store.dispatch('user/getInfo')\n\n          // generate accessible routes map based on roles\n          const accessRoutes = await store.dispatch('permission/generateRoutes', roles)\n\n          // dynamically add accessible routes\n          router.addRoutes(accessRoutes)\n\n          // hack method to ensure that addRoutes is complete\n          // set the replace: true, so the navigation will not leave a history record\n          next({ ...to, replace: true })\n        } catch (error) {\n          // remove token and go to login page to re-login\n          await store.dispatch('user/resetToken')\n          Message.error(error || 'Has Error')\n          next(`/login?redirect=${to.path}`)\n          NProgress.done()\n        }\n      }\n    }\n  } else {\n    /* has no token*/\n\n    if (whiteList.indexOf(to.path) !== -1) {\n      // in the free login whitelist, go directly\n      next()\n    } else {\n      // other pages that do not have permission to access are redirected to the login page.\n      next(`/login?redirect=${to.path}`)\n      NProgress.done()\n    }\n  }\n})\n\nrouter.afterEach(() => {\n  // finish progress bar\n  NProgress.done()\n})\n"], "mappings": ";;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,SAAS,MAAM,WAAW,EAAC;AAClC,OAAO,yBAAyB,EAAC;AACjC,SAASC,QAAQ,QAAQ,cAAc,EAAC;AACxC,OAAOC,YAAY,MAAM,wBAAwB;AAEjDF,SAAS,CAACG,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC,EAAC;;AAE5C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,EAAC;;AAE/CR,MAAM,CAACS,UAAU;EAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,CAAC,SAAAC,QAAMC,EAAE,EAAEC,IAAI,EAAEC,IAAI;IAAA,IAAAC,QAAA,EAAAC,QAAA,EAAAC,qBAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,EAAA;IAAA,OAAAX,YAAA,GAAAY,CAAA,WAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,CAAA;QAAA;UACpC;UACAvB,SAAS,CAACwB,KAAK,CAAC,CAAC;;UAEjB;UACAC,QAAQ,CAACC,KAAK,GAAGxB,YAAY,CAACU,EAAE,CAACe,IAAI,CAACD,KAAK,CAAC;;UAE5C;UACMX,QAAQ,GAAGd,QAAQ,CAAC,CAAC;UAAA,KAEvBc,QAAQ;YAAAO,QAAA,CAAAC,CAAA;YAAA;UAAA;UAAA,MACNX,EAAE,CAACgB,IAAI,KAAK,QAAQ;YAAAN,QAAA,CAAAC,CAAA;YAAA;UAAA;UACtB;UACAT,IAAI,CAAC;YAAEc,IAAI,EAAE;UAAI,CAAC,CAAC;UACnB5B,SAAS,CAAC6B,IAAI,CAAC,CAAC,EAAC;UAAAP,QAAA,CAAAC,CAAA;UAAA;QAAA;UAEjB;UACMP,QAAQ,GAAGlB,KAAK,CAACgC,OAAO,CAACZ,KAAK,IAAIpB,KAAK,CAACgC,OAAO,CAACZ,KAAK,CAACa,MAAM,GAAG,CAAC;UAAA,KAClEf,QAAQ;YAAAM,QAAA,CAAAC,CAAA;YAAA;UAAA;UACVT,IAAI,CAAC,CAAC;UAAAQ,QAAA,CAAAC,CAAA;UAAA;QAAA;UAAAD,QAAA,CAAAU,CAAA;UAAAV,QAAA,CAAAC,CAAA;UAAA,OAKoBzB,KAAK,CAACmC,QAAQ,CAAC,cAAc,CAAC;QAAA;UAAAhB,qBAAA,GAAAK,QAAA,CAAAY,CAAA;UAA9ChB,KAAK,GAAAD,qBAAA,CAALC,KAAK;UAAAI,QAAA,CAAAC,CAAA;UAAA,OAGczB,KAAK,CAACmC,QAAQ,CAAC,2BAA2B,EAAEf,KAAK,CAAC;QAAA;UAAvEC,YAAY,GAAAG,QAAA,CAAAY,CAAA;UAElB;UACArC,MAAM,CAACsC,SAAS,CAAChB,YAAY,CAAC;;UAE9B;UACA;UACAL,IAAI,CAAAsB,aAAA,CAAAA,aAAA,KAAMxB,EAAE;YAAEyB,OAAO,EAAE;UAAI,EAAE,CAAC;UAAAf,QAAA,CAAAC,CAAA;UAAA;QAAA;UAAAD,QAAA,CAAAU,CAAA;UAAAZ,EAAA,GAAAE,QAAA,CAAAY,CAAA;UAAAZ,QAAA,CAAAC,CAAA;UAAA,OAGxBzB,KAAK,CAACmC,QAAQ,CAAC,iBAAiB,CAAC;QAAA;UACvClC,OAAO,CAACuC,KAAK,CAAClB,EAAA,IAAS,WAAW,CAAC;UACnCN,IAAI,oBAAAyB,MAAA,CAAoB3B,EAAE,CAACgB,IAAI,CAAE,CAAC;UAClC5B,SAAS,CAAC6B,IAAI,CAAC,CAAC;QAAA;UAAAP,QAAA,CAAAC,CAAA;UAAA;QAAA;UAKtB;;UAEA,IAAIlB,SAAS,CAACmC,OAAO,CAAC5B,EAAE,CAACgB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACrC;YACAd,IAAI,CAAC,CAAC;UACR,CAAC,MAAM;YACL;YACAA,IAAI,oBAAAyB,MAAA,CAAoB3B,EAAE,CAACgB,IAAI,CAAE,CAAC;YAClC5B,SAAS,CAAC6B,IAAI,CAAC,CAAC;UAClB;QAAC;UAAA,OAAAP,QAAA,CAAAmB,CAAA;MAAA;IAAA,GAAA9B,OAAA;EAAA,CAEJ;EAAA,iBAAA+B,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAArC,IAAA,CAAAsC,KAAA,OAAAC,SAAA;EAAA;AAAA,IAAC;AAEFjD,MAAM,CAACkD,SAAS,CAAC,YAAM;EACrB;EACA/C,SAAS,CAAC6B,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}