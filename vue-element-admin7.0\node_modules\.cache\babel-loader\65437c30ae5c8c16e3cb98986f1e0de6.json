{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Navbar.vue", "mtime": 1747748935263}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "Breadcrumb", "<PERSON><PERSON>", "ErrorLog", "Screenfull", "SizeSelect", "Search", "components", "computed", "_objectSpread", "methods", "toggleSideBar", "$store", "dispatch", "logout", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "w", "_context", "n", "$router", "push", "concat", "$route", "fullPath", "a"], "sources": ["src/layout/components/Navbar.vue"], "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" />\n\n    <div class=\"right-menu\">\n      <template v-if=\"device!=='mobile'\">\n        <search id=\"header-search\" class=\"right-menu-item\" />\n\n        <error-log class=\"errLog-container right-menu-item hover-effect\" />\n\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\n\n        <el-tooltip content=\"Global Size\" effect=\"dark\" placement=\"bottom\">\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\n        </el-tooltip>\n\n      </template>\n\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"require('@/assets/used_images/钱.png')\" class=\"user-avatar\" alt=\"跳舞的小人\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <!-- <router-link to=\"/profile/index\">\n            <el-dropdown-item>档案</el-dropdown-item>\n          </router-link> -->\n          <router-link to=\"/\">\n            <el-dropdown-item>主界面</el-dropdown-item>\n          </router-link>\n          <!-- <a target=\"_blank\" href=\"https://github.com/PanJiaChen/vue-element-admin/\">\n            <el-dropdown-item>Github</el-dropdown-item>\n          </a> -->\n          <!-- <a target=\"_blank\" href=\"https://panjiachen.github.io/vue-element-admin-site/#/\">\n            <el-dropdown-item>Docs</el-dropdown-item>\n          </a> -->\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span style=\"display:block;\">登出</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport Hamburger from '@/components/Hamburger'\nimport ErrorLog from '@/components/ErrorLog'\nimport Screenfull from '@/components/Screenfull'\nimport SizeSelect from '@/components/SizeSelect'\nimport Search from '@/components/HeaderSearch'\n\nexport default {\n  components: {\n    Breadcrumb,\n    Hamburger,\n    ErrorLog,\n    Screenfull,\n    SizeSelect,\n    Search\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar',\n      'device'\n    ])\n  },\n  methods: {\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      await this.$store.dispatch('user/logout')\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 50px;\n  overflow: hidden;\n  position: relative;\n  background: #fff;\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\n\n  .hamburger-container {\n    line-height: 46px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .errLog-container {\n    display: inline-block;\n    vertical-align: top;\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 50px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background .3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, .025)\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 30px;\n\n      .avatar-wrapper {\n        margin-top: 5px;\n        position: relative;\n\n        .user-avatar {\n          cursor: pointer;\n          width: 40px;\n          height: 40px;\n          border-radius: 10px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -20px;\n          top: 25px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;AAgDA,SAAAA,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,QAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,MAAA;AAEA;EACAC,UAAA;IACAN,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,QAAA,EAAAA,QAAA;IACAC,UAAA,EAAAA,UAAA;IACAC,UAAA,EAAAA,UAAA;IACAC,MAAA,EAAAA;EACA;EACAE,QAAA,EAAAC,aAAA,KACAT,UAAA,EACA,WACA,UACA,SACA,EACA;EACAU,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,OAAAF,YAAA,GAAAG,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACAP,KAAA,CAAAH,MAAA,CAAAC,QAAA;YAAA;cACAE,KAAA,CAAAQ,OAAA,CAAAC,IAAA,oBAAAC,MAAA,CAAAV,KAAA,CAAAW,MAAA,CAAAC,QAAA;YAAA;cAAA,OAAAN,QAAA,CAAAO,CAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}