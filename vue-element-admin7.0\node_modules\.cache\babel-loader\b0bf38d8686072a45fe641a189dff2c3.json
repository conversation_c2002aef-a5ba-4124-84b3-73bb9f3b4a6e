{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\filters\\index.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\filters\\index.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parseTime", "formatTime", "pluralize", "time", "label", "timeAgo", "between", "Date", "now", "Number", "numberF<PERSON>atter", "num", "digits", "si", "value", "symbol", "i", "length", "toFixed", "replace", "toString", "toThousandFilter", "m", "uppercaseFirst", "string", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/filters/index.js"], "sourcesContent": ["// import parseTime, formatTime and set to filter\nexport { parseTime, formatTime } from '@/utils'\n\n/**\n * Show plural label if time is plural number\n * @param {number} time\n * @param {string} label\n * @return {string}\n */\nfunction pluralize(time, label) {\n  if (time === 1) {\n    return time + label\n  }\n  return time + label + 's'\n}\n\n/**\n * @param {number} time\n */\nexport function timeAgo(time) {\n  const between = Date.now() / 1000 - Number(time)\n  if (between < 3600) {\n    return pluralize(~~(between / 60), ' minute')\n  } else if (between < 86400) {\n    return pluralize(~~(between / 3600), ' hour')\n  } else {\n    return pluralize(~~(between / 86400), ' day')\n  }\n}\n\n/**\n * Number formatting\n * like 10000 => 10k\n * @param {number} num\n * @param {number} digits\n */\nexport function numberFormatter(num, digits) {\n  const si = [\n    { value: 1E18, symbol: 'E' },\n    { value: 1E15, symbol: 'P' },\n    { value: 1E12, symbol: 'T' },\n    { value: 1E9, symbol: 'G' },\n    { value: 1E6, symbol: 'M' },\n    { value: 1E3, symbol: 'k' }\n  ]\n  for (let i = 0; i < si.length; i++) {\n    if (num >= si[i].value) {\n      return (num / si[i].value).toFixed(digits).replace(/\\.0+$|(\\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol\n    }\n  }\n  return num.toString()\n}\n\n/**\n * 10000 => \"10,000\"\n * @param {number} num\n */\nexport function toThousandFilter(num) {\n  return (+num || 0).toString().replace(/^-?\\d+/g, m => m.replace(/(?=(?!\\b)(\\d{3})+$)/g, ','))\n}\n\n/**\n * Upper case first char\n * @param {String} string\n */\nexport function uppercaseFirst(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1)\n}\n"], "mappings": ";;;;;;;AAAA;AACA,SAASA,SAAS,EAAEC,UAAU,QAAQ,SAAS;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC9B,IAAID,IAAI,KAAK,CAAC,EAAE;IACd,OAAOA,IAAI,GAAGC,KAAK;EACrB;EACA,OAAOD,IAAI,GAAGC,KAAK,GAAG,GAAG;AAC3B;;AAEA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACF,IAAI,EAAE;EAC5B,IAAMG,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAGC,MAAM,CAACN,IAAI,CAAC;EAChD,IAAIG,OAAO,GAAG,IAAI,EAAE;IAClB,OAAOJ,SAAS,CAAC,CAAC,EAAEI,OAAO,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC;EAC/C,CAAC,MAAM,IAAIA,OAAO,GAAG,KAAK,EAAE;IAC1B,OAAOJ,SAAS,CAAC,CAAC,EAAEI,OAAO,GAAG,IAAI,CAAC,EAAE,OAAO,CAAC;EAC/C,CAAC,MAAM;IACL,OAAOJ,SAAS,CAAC,CAAC,EAAEI,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC;EAC/C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,eAAeA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC3C,IAAMC,EAAE,GAAG,CACT;IAAEC,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC5B;IAAED,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC3B;IAAED,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC3B;IAAED,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,CAC5B;EACD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,IAAIL,GAAG,IAAIE,EAAE,CAACG,CAAC,CAAC,CAACF,KAAK,EAAE;MACtB,OAAO,CAACH,GAAG,GAAGE,EAAE,CAACG,CAAC,CAAC,CAACF,KAAK,EAAEI,OAAO,CAACN,MAAM,CAAC,CAACO,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,GAAGN,EAAE,CAACG,CAAC,CAAC,CAACD,MAAM;IACrG;EACF;EACA,OAAOJ,GAAG,CAACS,QAAQ,CAAC,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACV,GAAG,EAAE;EACpC,OAAO,CAAC,CAACA,GAAG,IAAI,CAAC,EAAES,QAAQ,CAAC,CAAC,CAACD,OAAO,CAAC,SAAS,EAAE,UAAAG,CAAC;IAAA,OAAIA,CAAC,CAACH,OAAO,CAAC,sBAAsB,EAAE,GAAG,CAAC;EAAA,EAAC;AAC/F;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,cAAcA,CAACC,MAAM,EAAE;EACrC,OAAOA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;AACzD", "ignoreList": []}]}