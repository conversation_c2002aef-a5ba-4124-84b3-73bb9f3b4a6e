{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBSaWdodFBhbmVsIGZyb20gJ0AvY29tcG9uZW50cy9SaWdodFBhbmVsJwppbXBvcnQgeyBBcHBNYWluLCBOYXZiYXIsIFNldHRpbmdzLCBTaWRlYmFyLCBUYWdzVmlldyB9IGZyb20gJy4vY29tcG9uZW50cycKaW1wb3J0IFJlc2l6ZU1peGluIGZyb20gJy4vbWl4aW4vUmVzaXplSGFuZGxlcicKaW1wb3J0IHsgbWFwU3RhdGUgfSBmcm9tICd2dWV4JwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdMYXlvdXQnLAogIGNvbXBvbmVudHM6IHsKICAgIEFwcE1haW4sCiAgICBOYXZiYXIsCiAgICBSaWdodFBhbmVsLAogICAgU2V0dGluZ3MsCiAgICBTaWRlYmFyLAogICAgVGFnc1ZpZXcKICB9LAogIG1peGluczogW1Jlc2l6ZU1peGluXSwKICBjb21wdXRlZDogewogICAgLi4ubWFwU3RhdGUoewogICAgICBzaWRlYmFyOiBzdGF0ZSA9PiBzdGF0ZS5hcHAuc2lkZWJhciwKICAgICAgZGV2aWNlOiBzdGF0ZSA9PiBzdGF0ZS5hcHAuZGV2aWNlLAogICAgICBzaG93U2V0dGluZ3M6IHN0YXRlID0+IHN0YXRlLnNldHRpbmdzLnNob3dTZXR0aW5ncywKICAgICAgbmVlZFRhZ3NWaWV3OiBzdGF0ZSA9PiBzdGF0ZS5zZXR0aW5ncy50YWdzVmlldywKICAgICAgZml4ZWRIZWFkZXI6IHN0YXRlID0+IHN0YXRlLnNldHRpbmdzLmZpeGVkSGVhZGVyCiAgICB9KSwKICAgIGNsYXNzT2JqKCkgewogICAgICByZXR1cm4gewogICAgICAgIGhpZGVTaWRlYmFyOiAhdGhpcy5zaWRlYmFyLm9wZW5lZCwKICAgICAgICBvcGVuU2lkZWJhcjogdGhpcy5zaWRlYmFyLm9wZW5lZCwKICAgICAgICB3aXRob3V0QW5pbWF0aW9uOiB0aGlzLnNpZGViYXIud2l0aG91dEFuaW1hdGlvbiwKICAgICAgICBtb2JpbGU6IHRoaXMuZGV2aWNlID09PSAnbW9iaWxlJwogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVDbGlja091dHNpZGUoKSB7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhcHAvY2xvc2VTaWRlQmFyJywgeyB3aXRob3V0QW5pbWF0aW9uOiBmYWxzZSB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\n    <sidebar class=\"sidebar-container\" />\n    <div :class=\"{hasTagsView:needTagsView}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar />\n        <tags-view v-if=\"needTagsView\" />\n      </div>\n      <app-main />\n      <right-panel v-if=\"showSettings\">\n        <settings />\n      </right-panel>\n    </div>\n  </div>\n</template>\n\n<script>\nimport RightPanel from '@/components/RightPanel'\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    <PERSON>Panel,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      showSettings: state => state.settings.showSettings,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/styles/mixin.scss\";\n  @import \"~@/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$sideBarWidth});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px)\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"]}]}