{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\permission.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\permission.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLnNvbWUuanMiOwppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7CgovKioKICogQHBhcmFtIHtBcnJheX0gdmFsdWUKICogQHJldHVybnMge0Jvb2xlYW59CiAqIEBleGFtcGxlIHNlZSBAL3ZpZXdzL3Blcm1pc3Npb24vZGlyZWN0aXZlLnZ1ZQogKi8KZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY2hlY2tQZXJtaXNzaW9uKHZhbHVlKSB7CiAgaWYgKHZhbHVlICYmIHZhbHVlIGluc3RhbmNlb2YgQXJyYXkgJiYgdmFsdWUubGVuZ3RoID4gMCkgewogICAgdmFyIHJvbGVzID0gc3RvcmUuZ2V0dGVycyAmJiBzdG9yZS5nZXR0ZXJzLnJvbGVzOwogICAgdmFyIHBlcm1pc3Npb25Sb2xlcyA9IHZhbHVlOwogICAgdmFyIGhhc1Blcm1pc3Npb24gPSByb2xlcy5zb21lKGZ1bmN0aW9uIChyb2xlKSB7CiAgICAgIHJldHVybiBwZXJtaXNzaW9uUm9sZXMuaW5jbHVkZXMocm9sZSk7CiAgICB9KTsKICAgIHJldHVybiBoYXNQZXJtaXNzaW9uOwogIH0gZWxzZSB7CiAgICBjb25zb2xlLmVycm9yKCJuZWVkIHJvbGVzISBMaWtlIHYtcGVybWlzc2lvbj1cIlsnYWRtaW4nLCdlZGl0b3InXVwiIik7CiAgICByZXR1cm4gZmFsc2U7CiAgfQp9"}, {"version": 3, "names": ["store", "checkPermission", "value", "Array", "length", "roles", "getters", "permissionRoles", "hasPermission", "some", "role", "includes", "console", "error"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/utils/permission.js"], "sourcesContent": ["import store from '@/store'\n\n/**\n * @param {Array} value\n * @returns {Boolean}\n * @example see @/views/permission/directive.vue\n */\nexport default function checkPermission(value) {\n  if (value && value instanceof Array && value.length > 0) {\n    const roles = store.getters && store.getters.roles\n    const permissionRoles = value\n\n    const hasPermission = roles.some(role => {\n      return permissionRoles.includes(role)\n    })\n    return hasPermission\n  } else {\n    console.error(`need roles! Like v-permission=\"['admin','editor']\"`)\n    return false\n  }\n}\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,SAAS;;AAE3B;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC7C,IAAIA,KAAK,IAAIA,KAAK,YAAYC,KAAK,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMC,KAAK,GAAGL,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACM,OAAO,CAACD,KAAK;IAClD,IAAME,eAAe,GAAGL,KAAK;IAE7B,IAAMM,aAAa,GAAGH,KAAK,CAACI,IAAI,CAAC,UAAAC,IAAI,EAAI;MACvC,OAAOH,eAAe,CAACI,QAAQ,CAACD,IAAI,CAAC;IACvC,CAAC,CAAC;IACF,OAAOF,aAAa;EACtB,CAAC,MAAM;IACLI,OAAO,CAACC,KAAK,uDAAqD,CAAC;IACnE,OAAO,KAAK;EACd;AACF", "ignoreList": []}]}