{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=style&index=0&id=31ea41b3&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zdWItZWwtaWNvbiB7CiAgY29sb3I6IGN1cnJlbnRDb2xvcjsKICB3aWR0aDogMWVtOwogIGhlaWdodDogMWVtOwp9Cg=="}, {"version": 3, "sources": ["Item.vue"], "names": [], "mappings": ";AAmCA;AACA;AACA;AACA;AACA", "file": "Item.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<script>\nexport default {\n  name: 'MenuItem',\n  functional: true,\n  props: {\n    icon: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  render(h, context) {\n    const { icon, title } = context.props\n    const vnodes = []\n\n    if (icon) {\n      if (icon.includes('el-icon')) {\n        vnodes.push(<i class={[icon, 'sub-el-icon']} />)\n      } else {\n        vnodes.push(<svg-icon icon-class={icon}/>)\n      }\n    }\n\n    if (title) {\n      vnodes.push(<span slot='title'>{(title)}</span>)\n    }\n    return vnodes\n  }\n}\n</script>\n\n<style scoped>\n.sub-el-icon {\n  color: currentColor;\n  width: 1em;\n  height: 1em;\n}\n</style>\n"]}]}