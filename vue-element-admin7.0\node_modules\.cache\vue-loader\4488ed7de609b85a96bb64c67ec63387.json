{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue", "mtime": 1749177859018}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgUmVsYXRpb25HcmFwaCBmcm9tICdyZWxhdGlvbi1ncmFwaCcNCmltcG9ydCBheGlvcyBmcm9tICdheGlvcycNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0RlbW8nLA0KICBjb21wb25lbnRzOiB7IFJlbGF0aW9uR3JhcGggfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgb3B0aW9uczogW10sDQogICAgICB2YWx1ZTogJycsIC8vIOmAieS4reeahOaVsOaNruihqA0KICAgICAgbWluQW1vdW50OiBudWxsLA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIHVzZXJuYW1lOiBudWxsLA0KICAgICAgbm9kZXM6IFtdLA0KICAgICAgbGluZXM6IFtdLA0KICAgICAgaXNTaG93OiBmYWxzZSwNCiAgICAgIGlzU2hvd0NvZGVQYW5lbDogZmFsc2UsDQogICAgICBncmFwaE9wdGlvbnM6IHsNCiAgICAgICAgZGVidWc6IHRydWUsDQogICAgICAgIC8vIGRlZmF1bHROb2RlQm9yZGVyV2lkdGg6IDAsDQogICAgICAgIGRlZmF1bHROb2RlQ29sb3I6ICdyZ2JhKDY0LCAxNTgsIDI1NSwgMC44KScsIC8vIOS4u+iTneiJsuiKgueCuQ0KICAgICAgICBkZWZhdWx0Tm9kZUJvcmRlcldpZHRoOiAyLA0KICAgICAgICBkZWZhdWx0Tm9kZUJvcmRlckNvbG9yOiAncmdiYSg2NCwgMTU4LCAyNTUsIDEpJywNCiAgICAgICAgLy8g57q/5p2h5Lmf5L2/55So6JOd6Imy57O777yM5L2G5rex5bqm5LiN5ZCM77yM6YG/5YWN57u/6ImyDQogICAgICAgIGRlZmF1bHRMaW5lQ29sb3I6ICdyZ2JhKDk2LCAxNzUsIDI1NSwgMC42KScsIC8vIOa1heiTneiJsue6v+adoQ0KICAgICAgICAvLyDmloflrZfpopzoibINCiAgICAgICAgZGVmYXVsdE5vZGVGb250Q29sb3I6ICcjZmZmZmZmJywgLy8g55m96Imy5paH5a2XDQogICAgICAgIGRlZmF1bHRMaW5lRm9udENvbG9yOiAnIzMwMzEzMycsIC8vIOa3seeBsOiJsuaWh+Wtl++8jOabtOaYk+ivuw0KICAgICAgICBoaWRlTm9kZUNvbnRlbnRCeVpvb206IHRydWUgLy8g5qC55o2u57yp5pS+5q+U5L6L6ZqQ6JeP6IqC54K55YaF5a65DQogICAgICB9DQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIGF3YWl0IHRoaXMuc2hvd0dyYXBoKCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmhhbmRsZVNlYXJjaCgpIC8vIOWIneWni+WMluWKoOi9veihqOWQjQ0KICAvLyDlpoLmnpzpnIDpu5jorqTliqDovb3mlbDmja7vvIzlj6/lnKjmraTosIPnlKggdGhpcy5zaG93R3JhcGgoKTsNCiAgfSwNCiAgLy8gbW91bnRlZCgpIHsNCiAgLy8gICB0aGlzLnNob3dHcmFwaCgpOw0KICAvLyB9LA0KICBtZXRob2RzOiB7DQogICAgLy8gZmV0Y2hEZWZhdWx0RGF0YSgpew0KICAgIC8vICAgYWxlcnQoJ2RlZmF1bHQnKTsNCiAgICAvLyAgIGF4aW9zLmdldCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2FsbF9yZWxhdGlvbicpDQogICAgLy8gICAudGhlbihyZXNwb25zZSA9PiB7DQogICAgLy8gICAgIGlmIChyZXNwb25zZS5kYXRhLnN0YXRlID09PSAyMDApIHsNCiAgICAvLyAgICAgICAvLyDkv53lrZggbm9kZXMg5ZKMIGxpbmVzIOaVsOaNrg0KICAgIC8vICAgICAgIHRoaXMubm9kZXMgPSByZXNwb25zZS5kYXRhLmRhdGEubm9kZXM7DQogICAgLy8gICAgICAgdGhpcy5saW5lcyA9IHJlc3BvbnNlLmRhdGEuZGF0YS5saW5lczsNCiAgICAvLyAgICAgICBjb25zb2xlLmxvZygndGhpcy5ub2RlcycsIHRoaXMubm9kZXMpOw0KICAgIC8vICAgICAgIGNvbnNvbGUubG9nKCd0aGlzLmxpbmVzJywgdGhpcy5saW5lcyk7DQogICAgLy8gICAgIH0gZWxzZSB7DQogICAgLy8gICAgICAgY29uc29sZS5lcnJvcign5p+l6K+i5aSx6LSlOiAnLCByZXNwb25zZS5kYXRhLnN0YXRlKTsNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfSkNCiAgICAvLyAgIC5jYXRjaChlcnJvciA9PiB7DQogICAgLy8gICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yOicsIGVycm9yKTsNCiAgICAvLyAgIH0pOw0KICAgIC8vIH0sDQogICAgLy8gc2hvd0RlZmF1bHRHcmFwaCgpew0KICAgIC8vICAgZmV0Y2hEZWZhdWx0RGF0YSgpOw0KICAgIC8vICAgdGhpcy5pc1Nob3cgPSB0cnVlOw0KICAgIC8vICAgY29uc3QgX19ncmFwaF9qc29uX2RhdGEgPSB7DQogICAgLy8gICAgIHJvb3RJZDogJzAnLA0KICAgIC8vICAgICBub2RlczogdGhpcy5ub2RlcywNCiAgICAvLyAgICAgbGluZXM6IHRoaXMubGluZXMNCiAgICAvLyAgIH07DQogICAgLy8gICAvL+eUqOadpeaYvuekuuWbvuW9ou+8jOW/hemhu+WtmOWcqA0KICAgIC8vICAgdGhpcy4kcmVmcy5ncmFwaFJlZi5zZXRKc29uRGF0YShfX2dyYXBoX2pzb25fZGF0YSwgKGdyYXBoSW5zdGFuY2UpID0+IHsNCiAgICAvLyAgICAgICBncmFwaEluc3RhbmNlLnJlbmRlcigpOyAvLyDph43mlrDmuLLmn5Plm77lvaINCiAgICAvLyAgIH0pOw0KICAgIC8vIH0sDQogICAgaGFuZGxlU2VsZWN0Q2hhbmdlKHZhbHVlKSB7DQogICAgICBjb25zb2xlLmxvZygn6YCJ5Lit55qE5pWw5o2u6KGoOicsIHZhbHVlKQ0KICAgICAgdGhpcy5zaG93R3JhcGgoKSAvLyDpgInmi6nlj5jljJbml7bph43mlrDliqDovb3lm77ooagNCiAgICB9LA0KICAgIGFzeW5jIGZldGNoRGF0YSgpIHsNCiAgICAgIGNvbnN0IHBheWxvYWQgPSB7DQogICAgICAgIHRhYmxlTmFtZTogdGhpcy52YWx1ZSB8fCBudWxsLA0KICAgICAgICB1c2VybmFtZTogdGhpcy51c2VybmFtZSB8fCBudWxsLA0KICAgICAgICBtaW5BbW91bnQ6IE51bWJlcih0aGlzLm1pbkFtb3VudCkgfHwgMCwNCiAgICAgICAgZGF0ZVJhbmdlOiB0aGlzLmRhdGVSYW5nZSB8fCBbXQ0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coJ3BheWxvYWTmmK86JywgcGF5bG9hZCkNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2ZpbmRfcmVsYXRpb24nLCBwYXlsb2FkKQ0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zdGF0ZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5ub2RlcyA9IHJlc3BvbnNlLmRhdGEuZGF0YS5ub2Rlcw0KICAgICAgICAgIHRoaXMubGluZXMgPSByZXNwb25zZS5kYXRhLmRhdGEubGluZXMNCiAgICAgICAgICBjb25zb2xlLmxvZygn5o6l5pS25Yiw55qE5pWw5o2uOicsIHsgbm9kZXM6IHRoaXMubm9kZXMsIGxpbmVzOiB0aGlzLmxpbmVzIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+a1i+ivleWksei0pScpDQogICAgICAgICAgY29uc29sZS5lcnJvcign5pyN5Yqh5Zmo6L+U5Zue5byC5bi4OicsIHJlc3BvbnNlLmRhdGEpDQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGDmn6Xor6LlpLHotKXvvIzmnI3liqHlmajov5Tlm57nirbmgIHnoIE6ICR7cmVzcG9uc2UuZGF0YS5zdGF0ZX1gKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmxvZygn6L+Z6YeM55qE6Zeu6aKYJykNCiAgICAgICAgY29uc29sZS5lcnJvcign6K+35rGC5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aHJvdyBlcnJvciAvLyDmipvlh7rplJnor6/vvIzorqnosIPnlKjogIXnn6XpgZPor7fmsYLlpLHotKUNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHNob3dHcmFwaCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfml6XmnJ/ojIPlm7Tlj4LmlbA6JywgdGhpcy5kYXRlUmFuZ2UpDQogICAgICAgIC8vIOetieW+hSBmZXRjaERhdGEg5a6M5oiQDQogICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZQ0KICAgICAgICBhd2FpdCB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgIGlmICh0aGlzLm5vZGVzLmxlbmd0aCA9PT0gMCB8fCB0aGlzLmxpbmVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5pWw5o2u5Y+v5pi+56S6JykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICAvLyDnoa7kv53mlbDmja7liqDovb3lrozmiJDlkI7vvIzlho3miafooYzlkI7nu63pgLvovpENCg0KICAgICAgICBjb25zdCBfX2dyYXBoX2pzb25fZGF0YSA9IHsNCiAgICAgICAgICByb290SWQ6ICcwJywNCiAgICAgICAgICBub2RlczogdGhpcy5ub2RlcywNCiAgICAgICAgICBsaW5lczogdGhpcy5saW5lcw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g55u05o6l6LCD55SocmVuZGVy5pa55rOV77yM5peg6ZyA5Zue6LCDDQogICAgICAgIHRoaXMuJHJlZnMuZ3JhcGhSZWYuc2V0SnNvbkRhdGEoX19ncmFwaF9qc29uX2RhdGEsIChncmFwaEluc3RhbmNlKSA9PiB7DQogICAgICAgICAgLy8g5Y+v5Zyo5q2k5Zue6LCD5Lit5re75Yqg5biD5bGA6YWN572u77yI5aaC5p6c6ZyA6KaB77yJDQogICAgICAgICAgZ3JhcGhJbnN0YW5jZS5zZXRPcHRpb25zKHsgbGF5b3V0OiAnZm9yY2UnIH0pDQogICAgICAgIH0pDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAvLyDmjZXojrflubblpITnkIbplJnor68NCiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIGRhdGEgb3IgcmVuZGVyIGdyYXBoOicsIGVycm9yKQ0KICAgICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlIC8vIOWmguaenOWksei0pe+8jOWPr+S7peWwhiBpc1Nob3cg6K6+572u5Li6IGZhbHNlIOaIluWFtuS7lumAu+i+kQ0KICAgICAgfQ0KICAgIH0sDQogICAgb25Ob2RlQ2xpY2sobm9kZU9iamVjdCwgJGV2ZW50KSB7DQogICAgICB0aGlzLiRtZXNzYWdlKGDkurrniak6ICR7bm9kZU9iamVjdC50ZXh0fWApDQogICAgfSwNCiAgICBvbkxpbmVDbGljayhsaW5lT2JqZWN0LCBsaW5rT2JqZWN0LCAkZXZlbnQpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2UoYOS6pOaYk+minTogJHtsaW5lT2JqZWN0LnRleHR9YCkNCiAgICB9LA0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCiAgICAgIC8vIOWPkemAgeS6pOaYk+aVsOaNruWIsOWQjuerrw0KICAgICAgYXhpb3MNCiAgICAgICAgLmdldCgnaHR0cDovLzEyNy4wLjAuMTo4MDAwL2FsbF90YWJsZXMnKQ0KICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZS5kYXRhKQ0KICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhLmFsbF90YWJsZXMNCiAgICAgICAgICB0aGlzLm9wdGlvbnMgPSBkYXRhLm1hcCgoaXRlbSkgPT4gKHsNCiAgICAgICAgICAgIGxhYmVsOiBpdGVtLCAvLyDkvb/nlKjlrZfnrKbkuLLkvZzkuLogbGFiZWwNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtIC8vIOS9v+eUqOebuOWQjOeahOWtl+espuS4suS9nOS4uiB2YWx1ZQ0KICAgICAgICAgIH0pKQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["KnowledgeGraph.vue"], "names": [], "mappings": ";AAwEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "KnowledgeGraph.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-date-picker\r\n      v-model=\"dateRange\"\r\n      type=\"datetimerange\"\r\n      range-separator=\"至\"\r\n      start-placeholder=\"起始日期时间\"\r\n      end-placeholder=\"结束日期时间\"\r\n      format=\"yyyy-MM-dd HH:mm:ss\"\r\n      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n      style=\"width: 350px;margin-top: 15px;margin-right: 15px;margin-left: 15px;\"\r\n    />\r\n\r\n    <el-input\r\n      v-model=\"minAmount\"\r\n      placeholder=\"请输入查询的最低额度\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" @click=\"showGraph\">确认</el-button>\r\n    <!-- <el-button\r\n      type=\"info\"\r\n      style=\"margin-left: 10px\"\r\n      @click=\"toggleIndirectTransactions\"\r\n    >\r\n    展示间接交易\r\n\r\n    </el-button> -->\r\n\r\n    <div v-if=\"isShow\" style=\"height: calc(100vh)\">\r\n      <RelationGraph\r\n        ref=\"graphRef\"\r\n        :options=\"graphOptions\"\r\n        :on-node-click=\"onNodeClick\"\r\n        :on-line-click=\"onLineClick\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RelationGraph from 'relation-graph'\r\nimport axios from 'axios'\r\nexport default {\r\n  name: 'Demo',\r\n  components: { RelationGraph },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      value: '', // 选中的数据表\r\n      minAmount: null,\r\n      dateRange: [],\r\n      username: null,\r\n      nodes: [],\r\n      lines: [],\r\n      isShow: false,\r\n      isShowCodePanel: false,\r\n      graphOptions: {\r\n        debug: true,\r\n        // defaultNodeBorderWidth: 0,\r\n        defaultNodeColor: 'rgba(64, 158, 255, 0.8)', // 主蓝色节点\r\n        defaultNodeBorderWidth: 2,\r\n        defaultNodeBorderColor: 'rgba(64, 158, 255, 1)',\r\n        // 线条也使用蓝色系，但深度不同，避免绿色\r\n        defaultLineColor: 'rgba(96, 175, 255, 0.6)', // 浅蓝色线条\r\n        // 文字颜色\r\n        defaultNodeFontColor: '#ffffff', // 白色文字\r\n        defaultLineFontColor: '#303133', // 深灰色文字，更易读\r\n        hideNodeContentByZoom: true // 根据缩放比例隐藏节点内容\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.showGraph()\r\n  },\r\n  mounted() {\r\n    this.handleSearch() // 初始化加载表名\r\n  // 如果需默认加载数据，可在此调用 this.showGraph();\r\n  },\r\n  // mounted() {\r\n  //   this.showGraph();\r\n  // },\r\n  methods: {\r\n    // fetchDefaultData(){\r\n    //   alert('default');\r\n    //   axios.get('http://127.0.0.1:8000/all_relation')\r\n    //   .then(response => {\r\n    //     if (response.data.state === 200) {\r\n    //       // 保存 nodes 和 lines 数据\r\n    //       this.nodes = response.data.data.nodes;\r\n    //       this.lines = response.data.data.lines;\r\n    //       console.log('this.nodes', this.nodes);\r\n    //       console.log('this.lines', this.lines);\r\n    //     } else {\r\n    //       console.error('查询失败: ', response.data.state);\r\n    //     }\r\n    //   })\r\n    //   .catch(error => {\r\n    //     console.error('Error:', error);\r\n    //   });\r\n    // },\r\n    // showDefaultGraph(){\r\n    //   fetchDefaultData();\r\n    //   this.isShow = true;\r\n    //   const __graph_json_data = {\r\n    //     rootId: '0',\r\n    //     nodes: this.nodes,\r\n    //     lines: this.lines\r\n    //   };\r\n    //   //用来显示图形，必须存在\r\n    //   this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n    //       graphInstance.render(); // 重新渲染图形\r\n    //   });\r\n    // },\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      this.showGraph() // 选择变化时重新加载图表\r\n    },\r\n    async fetchData() {\r\n      const payload = {\r\n        tableName: this.value || null,\r\n        username: this.username || null,\r\n        minAmount: Number(this.minAmount) || 0,\r\n        dateRange: this.dateRange || []\r\n      }\r\n      console.log('payload是:', payload)\r\n      try {\r\n        const response = await axios.post('http://127.0.0.1:8000/find_relation', payload)\r\n        if (response.data.state === 200) {\r\n          this.nodes = response.data.data.nodes\r\n          this.lines = response.data.data.lines\r\n          console.log('接收到的数据:', { nodes: this.nodes, lines: this.lines })\r\n        } else {\r\n          console.log('测试失败')\r\n          console.error('服务器返回异常:', response.data)\r\n          throw new Error(`查询失败，服务器返回状态码: ${response.data.state}`)\r\n        }\r\n      } catch (error) {\r\n        console.log('这里的问题')\r\n        console.error('请求失败:', error)\r\n        throw error // 抛出错误，让调用者知道请求失败\r\n      }\r\n    },\r\n    async showGraph() {\r\n      try {\r\n        console.log('日期范围参数:', this.dateRange)\r\n        // 等待 fetchData 完成\r\n        this.isShow = true\r\n        await this.fetchData()\r\n        if (this.nodes.length === 0 || this.lines.length === 0) {\r\n          this.$message.warning('没有数据可显示')\r\n          return\r\n        }\r\n        // 确保数据加载完成后，再执行后续逻辑\r\n\r\n        const __graph_json_data = {\r\n          rootId: '0',\r\n          nodes: this.nodes,\r\n          lines: this.lines\r\n        }\r\n\r\n        // 直接调用render方法，无需回调\r\n        this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n          // 可在此回调中添加布局配置（如果需要）\r\n          graphInstance.setOptions({ layout: 'force' })\r\n        })\r\n      } catch (error) {\r\n        // 捕获并处理错误\r\n        console.error('Failed to fetch data or render graph:', error)\r\n        this.isShow = false // 如果失败，可以将 isShow 设置为 false 或其他逻辑\r\n      }\r\n    },\r\n    onNodeClick(nodeObject, $event) {\r\n      this.$message(`人物: ${nodeObject.text}`)\r\n    },\r\n    onLineClick(lineObject, linkObject, $event) {\r\n      this.$message(`交易额: ${lineObject.text}`)\r\n    },\r\n    handleSearch() {\r\n      // 发送交易数据到后端\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then((response) => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map((item) => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch((error) => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}