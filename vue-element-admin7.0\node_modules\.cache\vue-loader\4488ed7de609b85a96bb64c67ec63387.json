{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\KnowledgeGraph.vue", "mtime": 1749151747451}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "sources": ["KnowledgeGraph.vue"], "names": [], "mappings": ";AAuEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "KnowledgeGraph.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-select\r\n      v-model=\"value\"\r\n      placeholder=\"数据表\"\r\n      no-data-text=\"已经没有数据表了\"\r\n      style=\"margin-left: 20px\"\r\n      @focus=\"handleSearch\"\r\n      @change=\"handleSelectChange\"\r\n    >\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      />\r\n    </el-select>\r\n    <el-input\r\n      v-model=\"username\"\r\n      placeholder=\"请输入查询的用户名\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-date-picker\r\n      v-model=\"dateRange\"\r\n      type=\"datetimerange\"\r\n      range-separator=\"至\"\r\n      start-placeholder=\"起始日期时间\"\r\n      end-placeholder=\"结束日期时间\"\r\n      format=\"yyyy-MM-dd HH:mm:ss\"\r\n      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n      style=\"width: 350px;margin-top: 15px;margin-right: 15px;margin-left: 15px\"\r\n    />\r\n\r\n    <el-input\r\n      v-model=\"minAmount\"\r\n      placeholder=\"请输入查询的最低额度\"\r\n      style=\"\r\n        width: 200px;\r\n        margin-top: 15px;\r\n        margin-right: 15px;\r\n        margin-left: 15px;\r\n      \"\r\n    />\r\n\r\n    <el-button type=\"primary\" @click=\"showGraph\">确认</el-button>\r\n    <el-button\r\n      type=\"info\"\r\n      style=\"margin-left: 10px\"\r\n      @click=\"toggleIndirectTransactions\"\r\n    >\r\n      展示间接交易\r\n    </el-button>\r\n\r\n    <div v-if=\"isShow\" style=\"height: calc(100vh)\">\r\n      <RelationGraph\r\n        ref=\"graphRef\"\r\n        :options=\"graphOptions\"\r\n        :on-node-click=\"onNodeClick\"\r\n        :on-line-click=\"onLineClick\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import RelationGraph from 'relation-graph'\r\nimport axios from 'axios'\r\nexport default {\r\n  name: 'Demo',\r\n  // components: { RelationGraph },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      value: '', // 选中的数据表\r\n      minAmount: null,\r\n      dateRange: [],\r\n      username: null,\r\n      nodes: [],\r\n      lines: [],\r\n      isShow: false,\r\n      isShowCodePanel: false,\r\n      graphOptions: {\r\n        debug: true,\r\n        // defaultNodeBorderWidth: 0,\r\n        defaultNodeColor: 'rgba(64, 158, 255, 0.8)', // 主蓝色节点\r\n        defaultNodeBorderWidth: 2,\r\n        defaultNodeBorderColor: 'rgba(64, 158, 255, 1)',\r\n        // 线条也使用蓝色系，但深度不同，避免绿色\r\n        defaultLineColor: 'rgba(96, 175, 255, 0.6)', // 浅蓝色线条\r\n        // 文字颜色\r\n        defaultNodeFontColor: '#ffffff', // 白色文字\r\n        defaultLineFontColor: '#303133', // 深灰色文字，更易读\r\n        hideNodeContentByZoom: true // 根据缩放比例隐藏节点内容\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.showGraph()\r\n  },\r\n  mounted() {\r\n    this.handleSearch() // 初始化加载表名\r\n    // 如果需默认加载数据，可在此调用 this.showGraph()\r\n  },\r\n  // mounted() {\r\n  //   this.showGraph();\r\n  // },\r\n  methods: {\r\n    // fetchDefaultData(){\r\n    //   alert('default');\r\n    //   axios.get('http://127.0.0.1:8000/all_relation')\r\n    //   .then(response => {\r\n    //     if (response.data.state === 200) {\r\n    //       // 保存 nodes 和 lines 数据\r\n    //       this.nodes = response.data.data.nodes;\r\n    //       this.lines = response.data.data.lines;\r\n    //       console.log('this.nodes', this.nodes);\r\n    //       console.log('this.lines', this.lines);\r\n    //     } else {\r\n    //       console.error('查询失败: ', response.data.state);\r\n    //     }\r\n    //   })\r\n    //   .catch(error => {\r\n    //     console.error('Error:', error);\r\n    //   });\r\n    // },\r\n    // showDefaultGraph(){\r\n    //   fetchDefaultData();\r\n    //   this.isShow = true;\r\n    //   const __graph_json_data = {\r\n    //     rootId: '0',\r\n    //     nodes: this.nodes,\r\n    //     lines: this.lines\r\n    //   };\r\n    //   //用来显示图形，必须存在\r\n    //   this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n    //       graphInstance.render(); // 重新渲染图形\r\n    //   });\r\n    // },\r\n    handleSelectChange(value) {\r\n      console.log('选中的数据表:', value)\r\n      this.showGraph() // 选择变化时重新加载图表\r\n    },\r\n    async fetchData() {\r\n      const payload = {\r\n        tableName: this.value || null,\r\n        username: this.username || null,\r\n        minAmount: Number(this.minAmount) || 0,\r\n        dateRange: this.dateRange || []\r\n      }\r\n      console.log('payload是:', payload)\r\n      try {\r\n        const response = await axios.post('http://127.0.0.1:8000/find_relation', payload)\r\n        if (response.data.state === 200) {\r\n          this.nodes = response.data.data.nodes\r\n          this.lines = response.data.data.lines\r\n          console.log('接收到的数据:', { nodes: this.nodes, lines: this.lines })\r\n        } else {\r\n          console.log('测试失败')\r\n          console.error('服务器返回异常:', response.data)\r\n          throw new Error(`查询失败，服务器返回状态码: ${response.data.state}`)\r\n        }\r\n      } catch (error) {\r\n        console.log('这里的问题')\r\n        console.error('请求失败:', error)\r\n        throw error // 抛出错误，让调用者知道请求失败\r\n      }\r\n    },\r\n    async showGraph() {\r\n      try {\r\n        console.log('日期范围参数:', this.dateRange)\r\n        // 等待 fetchData 完成\r\n        this.isShow = true\r\n        await this.fetchData()\r\n        if (this.nodes.length === 0 || this.lines.length === 0) {\r\n          this.$message.warning('没有数据可显示')\r\n          return\r\n        }\r\n        // 确保数据加载完成后，再执行后续逻辑\r\n\r\n        const __graph_json_data = {\r\n          rootId: '0',\r\n          nodes: this.nodes,\r\n          lines: this.lines\r\n        }\r\n\r\n        // 直接调用render方法，无需回调\r\n        this.$refs.graphRef.setJsonData(__graph_json_data, (graphInstance) => {\r\n          // 可在此回调中添加布局配置（如果需要）\r\n          graphInstance.setOptions({ layout: 'force' })\r\n        })\r\n      } catch (error) {\r\n        // 捕获并处理错误\r\n        console.error('Failed to fetch data or render graph:', error)\r\n        this.isShow = false // 如果失败，可以将 isShow 设置为 false 或其他逻辑\r\n      }\r\n    },\r\n    onNodeClick(nodeObject, $event) {\r\n      this.$message(`人物: ${nodeObject.text}`)\r\n    },\r\n    onLineClick(lineObject, linkObject, $event) {\r\n      this.$message(`交易额: ${lineObject.text}`)\r\n    },\r\n    handleSearch() {\r\n      // 发送交易数据到后端\r\n      axios\r\n        .get('http://127.0.0.1:8000/all_tables')\r\n        .then((response) => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map((item) => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch((error) => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}