{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\index.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\index.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parseTime", "time", "cFormat", "arguments", "length", "format", "date", "_typeof", "test", "parseInt", "replace", "RegExp", "toString", "Date", "formatObj", "y", "getFullYear", "m", "getMonth", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "a", "getDay", "time_str", "result", "key", "value", "padStart", "formatTime", "option", "now", "diff", "Math", "ceil", "getQueryObject", "url", "window", "location", "href", "search", "substring", "lastIndexOf", "obj", "reg", "rs", "$1", "$2", "name", "decodeURIComponent", "val", "String", "byteLength", "str", "code", "charCodeAt", "cleanArray", "actual", "newArray", "push", "param", "json", "Object", "keys", "map", "undefined", "encodeURIComponent", "join", "param2Obj", "split", "searchArr", "for<PERSON>ach", "v", "index", "indexOf", "html2Text", "div", "document", "createElement", "innerHTML", "textContent", "innerText", "objectMerge", "target", "source", "Array", "isArray", "slice", "property", "sourceProperty", "toggleClass", "element", "className", "classString", "nameIndex", "substr", "getTime", "type", "toDateString", "debounce", "func", "wait", "immediate", "timeout", "args", "context", "timestamp", "later", "last", "setTimeout", "apply", "_len", "_key", "callNow", "deepClone", "Error", "targetObj", "constructor", "uniqueArr", "arr", "from", "Set", "createUniqueString", "randomNum", "random", "hasClass", "ele", "cls", "match", "addClass", "removeClass"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/utils/index.js"], "sourcesContent": ["/**\n * Created by PanJia<PERSON>hen on 16/11/18.\n */\n\n/**\n * Parse the time to string\n * @param {(Object|string|number)} time\n * @param {string} cFormat\n * @returns {string | null}\n */\nexport function parseTime(time, cFormat) {\n  if (arguments.length === 0 || !time) {\n    return null\n  }\n  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'\n  let date\n  if (typeof time === 'object') {\n    date = time\n  } else {\n    if ((typeof time === 'string')) {\n      if ((/^[0-9]+$/.test(time))) {\n        // support \"1548221490638\"\n        time = parseInt(time)\n      } else {\n        // support safari\n        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\n        time = time.replace(new RegExp(/-/gm), '/')\n      }\n    }\n\n    if ((typeof time === 'number') && (time.toString().length === 10)) {\n      time = time * 1000\n    }\n    date = new Date(time)\n  }\n  const formatObj = {\n    y: date.getFullYear(),\n    m: date.getMonth() + 1,\n    d: date.getDate(),\n    h: date.getHours(),\n    i: date.getMinutes(),\n    s: date.getSeconds(),\n    a: date.getDay()\n  }\n  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {\n    const value = formatObj[key]\n    // Note: getDay() returns 0 on Sunday\n    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value ] }\n    return value.toString().padStart(2, '0')\n  })\n  return time_str\n}\n\n/**\n * @param {number} time\n * @param {string} option\n * @returns {string}\n */\nexport function formatTime(time, option) {\n  if (('' + time).length === 10) {\n    time = parseInt(time) * 1000\n  } else {\n    time = +time\n  }\n  const d = new Date(time)\n  const now = Date.now()\n\n  const diff = (now - d) / 1000\n\n  if (diff < 30) {\n    return '刚刚'\n  } else if (diff < 3600) {\n    // less 1 hour\n    return Math.ceil(diff / 60) + '分钟前'\n  } else if (diff < 3600 * 24) {\n    return Math.ceil(diff / 3600) + '小时前'\n  } else if (diff < 3600 * 24 * 2) {\n    return '1天前'\n  }\n  if (option) {\n    return parseTime(time, option)\n  } else {\n    return (\n      d.getMonth() +\n      1 +\n      '月' +\n      d.getDate() +\n      '日' +\n      d.getHours() +\n      '时' +\n      d.getMinutes() +\n      '分'\n    )\n  }\n}\n\n/**\n * @param {string} url\n * @returns {Object}\n */\nexport function getQueryObject(url) {\n  url = url == null ? window.location.href : url\n  const search = url.substring(url.lastIndexOf('?') + 1)\n  const obj = {}\n  const reg = /([^?&=]+)=([^?&=]*)/g\n  search.replace(reg, (rs, $1, $2) => {\n    const name = decodeURIComponent($1)\n    let val = decodeURIComponent($2)\n    val = String(val)\n    obj[name] = val\n    return rs\n  })\n  return obj\n}\n\n/**\n * @param {string} input value\n * @returns {number} output value\n */\nexport function byteLength(str) {\n  // returns the byte length of an utf8 string\n  let s = str.length\n  for (var i = str.length - 1; i >= 0; i--) {\n    const code = str.charCodeAt(i)\n    if (code > 0x7f && code <= 0x7ff) s++\n    else if (code > 0x7ff && code <= 0xffff) s += 2\n    if (code >= 0xDC00 && code <= 0xDFFF) i--\n  }\n  return s\n}\n\n/**\n * @param {Array} actual\n * @returns {Array}\n */\nexport function cleanArray(actual) {\n  const newArray = []\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i]) {\n      newArray.push(actual[i])\n    }\n  }\n  return newArray\n}\n\n/**\n * @param {Object} json\n * @returns {Array}\n */\nexport function param(json) {\n  if (!json) return ''\n  return cleanArray(\n    Object.keys(json).map(key => {\n      if (json[key] === undefined) return ''\n      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])\n    })\n  ).join('&')\n}\n\n/**\n * @param {string} url\n * @returns {Object}\n */\nexport function param2Obj(url) {\n  const search = decodeURIComponent(url.split('?')[1]).replace(/\\+/g, ' ')\n  if (!search) {\n    return {}\n  }\n  const obj = {}\n  const searchArr = search.split('&')\n  searchArr.forEach(v => {\n    const index = v.indexOf('=')\n    if (index !== -1) {\n      const name = v.substring(0, index)\n      const val = v.substring(index + 1, v.length)\n      obj[name] = val\n    }\n  })\n  return obj\n}\n\n/**\n * @param {string} val\n * @returns {string}\n */\nexport function html2Text(val) {\n  const div = document.createElement('div')\n  div.innerHTML = val\n  return div.textContent || div.innerText\n}\n\n/**\n * Merges two objects, giving the last one precedence\n * @param {Object} target\n * @param {(Object|Array)} source\n * @returns {Object}\n */\nexport function objectMerge(target, source) {\n  if (typeof target !== 'object') {\n    target = {}\n  }\n  if (Array.isArray(source)) {\n    return source.slice()\n  }\n  Object.keys(source).forEach(property => {\n    const sourceProperty = source[property]\n    if (typeof sourceProperty === 'object') {\n      target[property] = objectMerge(target[property], sourceProperty)\n    } else {\n      target[property] = sourceProperty\n    }\n  })\n  return target\n}\n\n/**\n * @param {HTMLElement} element\n * @param {string} className\n */\nexport function toggleClass(element, className) {\n  if (!element || !className) {\n    return\n  }\n  let classString = element.className\n  const nameIndex = classString.indexOf(className)\n  if (nameIndex === -1) {\n    classString += '' + className\n  } else {\n    classString =\n      classString.substr(0, nameIndex) +\n      classString.substr(nameIndex + className.length)\n  }\n  element.className = classString\n}\n\n/**\n * @param {string} type\n * @returns {Date}\n */\nexport function getTime(type) {\n  if (type === 'start') {\n    return new Date().getTime() - 3600 * 1000 * 24 * 90\n  } else {\n    return new Date(new Date().toDateString())\n  }\n}\n\n/**\n * @param {Function} func\n * @param {number} wait\n * @param {boolean} immediate\n * @return {*}\n */\nexport function debounce(func, wait, immediate) {\n  let timeout, args, context, timestamp, result\n\n  const later = function() {\n    // 据上一次触发时间间隔\n    const last = +new Date() - timestamp\n\n    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait\n    if (last < wait && last > 0) {\n      timeout = setTimeout(later, wait - last)\n    } else {\n      timeout = null\n      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用\n      if (!immediate) {\n        result = func.apply(context, args)\n        if (!timeout) context = args = null\n      }\n    }\n  }\n\n  return function(...args) {\n    context = this\n    timestamp = +new Date()\n    const callNow = immediate && !timeout\n    // 如果延时不存在，重新设定延时\n    if (!timeout) timeout = setTimeout(later, wait)\n    if (callNow) {\n      result = func.apply(context, args)\n      context = args = null\n    }\n\n    return result\n  }\n}\n\n/**\n * This is just a simple version of deep copy\n * Has a lot of edge cases bug\n * If you want to use a perfect deep copy, use lodash's _.cloneDeep\n * @param {Object} source\n * @returns {Object}\n */\nexport function deepClone(source) {\n  if (!source && typeof source !== 'object') {\n    throw new Error('error arguments', 'deepClone')\n  }\n  const targetObj = source.constructor === Array ? [] : {}\n  Object.keys(source).forEach(keys => {\n    if (source[keys] && typeof source[keys] === 'object') {\n      targetObj[keys] = deepClone(source[keys])\n    } else {\n      targetObj[keys] = source[keys]\n    }\n  })\n  return targetObj\n}\n\n/**\n * @param {Array} arr\n * @returns {Array}\n */\nexport function uniqueArr(arr) {\n  return Array.from(new Set(arr))\n}\n\n/**\n * @returns {string}\n */\nexport function createUniqueString() {\n  const timestamp = +new Date() + ''\n  const randomNum = parseInt((1 + Math.random()) * 65536) + ''\n  return (+(randomNum + timestamp)).toString(32)\n}\n\n/**\n * Check if an element has a class\n * @param {HTMLElement} elm\n * @param {string} cls\n * @returns {boolean}\n */\nexport function hasClass(ele, cls) {\n  return !!ele.className.match(new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)'))\n}\n\n/**\n * Add class to element\n * @param {HTMLElement} elm\n * @param {string} cls\n */\nexport function addClass(ele, cls) {\n  if (!hasClass(ele, cls)) ele.className += ' ' + cls\n}\n\n/**\n * Remove class from element\n * @param {HTMLElement} elm\n * @param {string} cls\n */\nexport function removeClass(ele, cls) {\n  if (hasClass(ele, cls)) {\n    const reg = new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)')\n    ele.className = ele.className.replace(reg, ' ')\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,IAAI,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAMI,MAAM,GAAGH,OAAO,IAAI,yBAAyB;EACnD,IAAII,IAAI;EACR,IAAIC,OAAA,CAAON,IAAI,MAAK,QAAQ,EAAE;IAC5BK,IAAI,GAAGL,IAAI;EACb,CAAC,MAAM;IACL,IAAK,OAAOA,IAAI,KAAK,QAAQ,EAAG;MAC9B,IAAK,UAAU,CAACO,IAAI,CAACP,IAAI,CAAC,EAAG;QAC3B;QACAA,IAAI,GAAGQ,QAAQ,CAACR,IAAI,CAAC;MACvB,CAAC,MAAM;QACL;QACA;QACAA,IAAI,GAAGA,IAAI,CAACS,OAAO,CAAC,IAAIC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;MAC7C;IACF;IAEA,IAAK,OAAOV,IAAI,KAAK,QAAQ,IAAMA,IAAI,CAACW,QAAQ,CAAC,CAAC,CAACR,MAAM,KAAK,EAAG,EAAE;MACjEH,IAAI,GAAGA,IAAI,GAAG,IAAI;IACpB;IACAK,IAAI,GAAG,IAAIO,IAAI,CAACZ,IAAI,CAAC;EACvB;EACA,IAAMa,SAAS,GAAG;IAChBC,CAAC,EAAET,IAAI,CAACU,WAAW,CAAC,CAAC;IACrBC,CAAC,EAAEX,IAAI,CAACY,QAAQ,CAAC,CAAC,GAAG,CAAC;IACtBC,CAAC,EAAEb,IAAI,CAACc,OAAO,CAAC,CAAC;IACjBC,CAAC,EAAEf,IAAI,CAACgB,QAAQ,CAAC,CAAC;IAClBC,CAAC,EAAEjB,IAAI,CAACkB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEnB,IAAI,CAACoB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAErB,IAAI,CAACsB,MAAM,CAAC;EACjB,CAAC;EACD,IAAMC,QAAQ,GAAGxB,MAAM,CAACK,OAAO,CAAC,iBAAiB,EAAE,UAACoB,MAAM,EAAEC,GAAG,EAAK;IAClE,IAAMC,KAAK,GAAGlB,SAAS,CAACiB,GAAG,CAAC;IAC5B;IACA,IAAIA,GAAG,KAAK,GAAG,EAAE;MAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,KAAK,CAAE;IAAC;IACtE,OAAOA,KAAK,CAACpB,QAAQ,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1C,CAAC,CAAC;EACF,OAAOJ,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,UAAUA,CAACjC,IAAI,EAAEkC,MAAM,EAAE;EACvC,IAAI,CAAC,EAAE,GAAGlC,IAAI,EAAEG,MAAM,KAAK,EAAE,EAAE;IAC7BH,IAAI,GAAGQ,QAAQ,CAACR,IAAI,CAAC,GAAG,IAAI;EAC9B,CAAC,MAAM;IACLA,IAAI,GAAG,CAACA,IAAI;EACd;EACA,IAAMkB,CAAC,GAAG,IAAIN,IAAI,CAACZ,IAAI,CAAC;EACxB,IAAMmC,GAAG,GAAGvB,IAAI,CAACuB,GAAG,CAAC,CAAC;EAEtB,IAAMC,IAAI,GAAG,CAACD,GAAG,GAAGjB,CAAC,IAAI,IAAI;EAE7B,IAAIkB,IAAI,GAAG,EAAE,EAAE;IACb,OAAO,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,EAAE;IACtB;IACA,OAAOC,IAAI,CAACC,IAAI,CAACF,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK;EACrC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE;IAC3B,OAAOC,IAAI,CAACC,IAAI,CAACF,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK;EACvC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE;IAC/B,OAAO,KAAK;EACd;EACA,IAAIF,MAAM,EAAE;IACV,OAAOnC,SAAS,CAACC,IAAI,EAAEkC,MAAM,CAAC;EAChC,CAAC,MAAM;IACL,OACEhB,CAAC,CAACD,QAAQ,CAAC,CAAC,GACZ,CAAC,GACD,GAAG,GACHC,CAAC,CAACC,OAAO,CAAC,CAAC,GACX,GAAG,GACHD,CAAC,CAACG,QAAQ,CAAC,CAAC,GACZ,GAAG,GACHH,CAAC,CAACK,UAAU,CAAC,CAAC,GACd,GAAG;EAEP;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASgB,cAAcA,CAACC,GAAG,EAAE;EAClCA,GAAG,GAAGA,GAAG,IAAI,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGH,GAAG;EAC9C,IAAMI,MAAM,GAAGJ,GAAG,CAACK,SAAS,CAACL,GAAG,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtD,IAAMC,GAAG,GAAG,CAAC,CAAC;EACd,IAAMC,GAAG,GAAG,sBAAsB;EAClCJ,MAAM,CAACnC,OAAO,CAACuC,GAAG,EAAE,UAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAK;IAClC,IAAMC,IAAI,GAAGC,kBAAkB,CAACH,EAAE,CAAC;IACnC,IAAII,GAAG,GAAGD,kBAAkB,CAACF,EAAE,CAAC;IAChCG,GAAG,GAAGC,MAAM,CAACD,GAAG,CAAC;IACjBP,GAAG,CAACK,IAAI,CAAC,GAAGE,GAAG;IACf,OAAOL,EAAE;EACX,CAAC,CAAC;EACF,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASS,UAAUA,CAACC,GAAG,EAAE;EAC9B;EACA,IAAIjC,CAAC,GAAGiC,GAAG,CAACtD,MAAM;EAClB,KAAK,IAAImB,CAAC,GAAGmC,GAAG,CAACtD,MAAM,GAAG,CAAC,EAAEmB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxC,IAAMoC,IAAI,GAAGD,GAAG,CAACE,UAAU,CAACrC,CAAC,CAAC;IAC9B,IAAIoC,IAAI,GAAG,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAElC,CAAC,EAAE,MAChC,IAAIkC,IAAI,GAAG,KAAK,IAAIA,IAAI,IAAI,MAAM,EAAElC,CAAC,IAAI,CAAC;IAC/C,IAAIkC,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAEpC,CAAC,EAAE;EAC3C;EACA,OAAOE,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASoC,UAAUA,CAACC,MAAM,EAAE;EACjC,IAAMC,QAAQ,GAAG,EAAE;EACnB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,MAAM,CAAC1D,MAAM,EAAEmB,CAAC,EAAE,EAAE;IACtC,IAAIuC,MAAM,CAACvC,CAAC,CAAC,EAAE;MACbwC,QAAQ,CAACC,IAAI,CAACF,MAAM,CAACvC,CAAC,CAAC,CAAC;IAC1B;EACF;EACA,OAAOwC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,OAAOL,UAAU,CACfM,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,UAAAtC,GAAG,EAAI;IAC3B,IAAImC,IAAI,CAACnC,GAAG,CAAC,KAAKuC,SAAS,EAAE,OAAO,EAAE;IACtC,OAAOC,kBAAkB,CAACxC,GAAG,CAAC,GAAG,GAAG,GAAGwC,kBAAkB,CAACL,IAAI,CAACnC,GAAG,CAAC,CAAC;EACtE,CAAC,CACH,CAAC,CAACyC,IAAI,CAAC,GAAG,CAAC;AACb;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAChC,GAAG,EAAE;EAC7B,IAAMI,MAAM,GAAGS,kBAAkB,CAACb,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAChE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACxE,IAAI,CAACmC,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,IAAMG,GAAG,GAAG,CAAC,CAAC;EACd,IAAM2B,SAAS,GAAG9B,MAAM,CAAC6B,KAAK,CAAC,GAAG,CAAC;EACnCC,SAAS,CAACC,OAAO,CAAC,UAAAC,CAAC,EAAI;IACrB,IAAMC,KAAK,GAAGD,CAAC,CAACE,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAMzB,IAAI,GAAGwB,CAAC,CAAC/B,SAAS,CAAC,CAAC,EAAEgC,KAAK,CAAC;MAClC,IAAMvB,GAAG,GAAGsB,CAAC,CAAC/B,SAAS,CAACgC,KAAK,GAAG,CAAC,EAAED,CAAC,CAACzE,MAAM,CAAC;MAC5C4C,GAAG,CAACK,IAAI,CAAC,GAAGE,GAAG;IACjB;EACF,CAAC,CAAC;EACF,OAAOP,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASgC,SAASA,CAACzB,GAAG,EAAE;EAC7B,IAAM0B,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCF,GAAG,CAACG,SAAS,GAAG7B,GAAG;EACnB,OAAO0B,GAAG,CAACI,WAAW,IAAIJ,GAAG,CAACK,SAAS;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC1C,IAAIlF,OAAA,CAAOiF,MAAM,MAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAG,CAAC,CAAC;EACb;EACA,IAAIE,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,OAAOA,MAAM,CAACG,KAAK,CAAC,CAAC;EACvB;EACAzB,MAAM,CAACC,IAAI,CAACqB,MAAM,CAAC,CAACb,OAAO,CAAC,UAAAiB,QAAQ,EAAI;IACtC,IAAMC,cAAc,GAAGL,MAAM,CAACI,QAAQ,CAAC;IACvC,IAAItF,OAAA,CAAOuF,cAAc,MAAK,QAAQ,EAAE;MACtCN,MAAM,CAACK,QAAQ,CAAC,GAAGN,WAAW,CAACC,MAAM,CAACK,QAAQ,CAAC,EAAEC,cAAc,CAAC;IAClE,CAAC,MAAM;MACLN,MAAM,CAACK,QAAQ,CAAC,GAAGC,cAAc;IACnC;EACF,CAAC,CAAC;EACF,OAAON,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAC9C,IAAI,CAACD,OAAO,IAAI,CAACC,SAAS,EAAE;IAC1B;EACF;EACA,IAAIC,WAAW,GAAGF,OAAO,CAACC,SAAS;EACnC,IAAME,SAAS,GAAGD,WAAW,CAACnB,OAAO,CAACkB,SAAS,CAAC;EAChD,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBD,WAAW,IAAI,EAAE,GAAGD,SAAS;EAC/B,CAAC,MAAM;IACLC,WAAW,GACTA,WAAW,CAACE,MAAM,CAAC,CAAC,EAAED,SAAS,CAAC,GAChCD,WAAW,CAACE,MAAM,CAACD,SAAS,GAAGF,SAAS,CAAC7F,MAAM,CAAC;EACpD;EACA4F,OAAO,CAACC,SAAS,GAAGC,WAAW;AACjC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAACC,IAAI,EAAE;EAC5B,IAAIA,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO,IAAIzF,IAAI,CAAC,CAAC,CAACwF,OAAO,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;EACrD,CAAC,MAAM;IACL,OAAO,IAAIxF,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC0F,YAAY,CAAC,CAAC,CAAC;EAC5C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC9C,IAAIC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEjF,MAAM;EAE7C,IAAMkF,MAAK,GAAG,SAARA,KAAKA,CAAA,EAAc;IACvB;IACA,IAAMC,IAAI,GAAG,CAAC,IAAIpG,IAAI,CAAC,CAAC,GAAGkG,SAAS;;IAEpC;IACA,IAAIE,IAAI,GAAGP,IAAI,IAAIO,IAAI,GAAG,CAAC,EAAE;MAC3BL,OAAO,GAAGM,UAAU,CAACF,MAAK,EAAEN,IAAI,GAAGO,IAAI,CAAC;IAC1C,CAAC,MAAM;MACLL,OAAO,GAAG,IAAI;MACd;MACA,IAAI,CAACD,SAAS,EAAE;QACd7E,MAAM,GAAG2E,IAAI,CAACU,KAAK,CAACL,OAAO,EAAED,IAAI,CAAC;QAClC,IAAI,CAACD,OAAO,EAAEE,OAAO,GAAGD,IAAI,GAAG,IAAI;MACrC;IACF;EACF,CAAC;EAED,OAAO,YAAkB;IAAA,SAAAO,IAAA,GAAAjH,SAAA,CAAAC,MAAA,EAANyG,IAAI,OAAAnB,KAAA,CAAA0B,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJR,IAAI,CAAAQ,IAAA,IAAAlH,SAAA,CAAAkH,IAAA;IAAA;IACrBP,OAAO,GAAG,IAAI;IACdC,SAAS,GAAG,CAAC,IAAIlG,IAAI,CAAC,CAAC;IACvB,IAAMyG,OAAO,GAAGX,SAAS,IAAI,CAACC,OAAO;IACrC;IACA,IAAI,CAACA,OAAO,EAAEA,OAAO,GAAGM,UAAU,CAACF,MAAK,EAAEN,IAAI,CAAC;IAC/C,IAAIY,OAAO,EAAE;MACXxF,MAAM,GAAG2E,IAAI,CAACU,KAAK,CAACL,OAAO,EAAED,IAAI,CAAC;MAClCC,OAAO,GAAGD,IAAI,GAAG,IAAI;IACvB;IAEA,OAAO/E,MAAM;EACf,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyF,SAASA,CAAC9B,MAAM,EAAE;EAChC,IAAI,CAACA,MAAM,IAAIlF,OAAA,CAAOkF,MAAM,MAAK,QAAQ,EAAE;IACzC,MAAM,IAAI+B,KAAK,CAAC,iBAAiB,EAAE,WAAW,CAAC;EACjD;EACA,IAAMC,SAAS,GAAGhC,MAAM,CAACiC,WAAW,KAAKhC,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;EACxDvB,MAAM,CAACC,IAAI,CAACqB,MAAM,CAAC,CAACb,OAAO,CAAC,UAAAR,IAAI,EAAI;IAClC,IAAIqB,MAAM,CAACrB,IAAI,CAAC,IAAI7D,OAAA,CAAOkF,MAAM,CAACrB,IAAI,CAAC,MAAK,QAAQ,EAAE;MACpDqD,SAAS,CAACrD,IAAI,CAAC,GAAGmD,SAAS,CAAC9B,MAAM,CAACrB,IAAI,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLqD,SAAS,CAACrD,IAAI,CAAC,GAAGqB,MAAM,CAACrB,IAAI,CAAC;IAChC;EACF,CAAC,CAAC;EACF,OAAOqD,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,SAASA,CAACC,GAAG,EAAE;EAC7B,OAAOlC,KAAK,CAACmC,IAAI,CAAC,IAAIC,GAAG,CAACF,GAAG,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA,OAAO,SAASG,kBAAkBA,CAAA,EAAG;EACnC,IAAMhB,SAAS,GAAG,CAAC,IAAIlG,IAAI,CAAC,CAAC,GAAG,EAAE;EAClC,IAAMmH,SAAS,GAAGvH,QAAQ,CAAC,CAAC,CAAC,GAAG6B,IAAI,CAAC2F,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;EAC5D,OAAO,CAAC,EAAED,SAAS,GAAGjB,SAAS,CAAC,EAAEnG,QAAQ,CAAC,EAAE,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsH,QAAQA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACjC,OAAO,CAAC,CAACD,GAAG,CAAClC,SAAS,CAACoC,KAAK,CAAC,IAAI1H,MAAM,CAAC,SAAS,GAAGyH,GAAG,GAAG,SAAS,CAAC,CAAC;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAACH,GAAG,EAAEC,GAAG,EAAE;EACjC,IAAI,CAACF,QAAQ,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAED,GAAG,CAAClC,SAAS,IAAI,GAAG,GAAGmC,GAAG;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,WAAWA,CAACJ,GAAG,EAAEC,GAAG,EAAE;EACpC,IAAIF,QAAQ,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE;IACtB,IAAMnF,GAAG,GAAG,IAAItC,MAAM,CAAC,SAAS,GAAGyH,GAAG,GAAG,SAAS,CAAC;IACnDD,GAAG,CAAClC,SAAS,GAAGkC,GAAG,CAAClC,SAAS,CAACvF,OAAO,CAACuC,GAAG,EAAE,GAAG,CAAC;EACjD;AACF", "ignoreList": []}]}