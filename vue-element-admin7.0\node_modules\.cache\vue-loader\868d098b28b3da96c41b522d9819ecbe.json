{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\RightPanel\\index.vue?vue&type=style&index=1&id=1e488bfb&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\RightPanel\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yaWdodFBhbmVsLWJhY2tncm91bmQgewogIHBvc2l0aW9uOiBmaXhlZDsKICB0b3A6IDA7CiAgbGVmdDogMDsKICBvcGFjaXR5OiAwOwogIHRyYW5zaXRpb246IG9wYWNpdHkgLjNzIGN1YmljLWJlemllciguNywgLjMsIC4xLCAxKTsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIC4yKTsKICB6LWluZGV4OiAtMTsKfQoKLnJpZ2h0UGFuZWwgewogIHdpZHRoOiAxMDAlOwogIG1heC13aWR0aDogMjYwcHg7CiAgaGVpZ2h0OiAxMDB2aDsKICBwb3NpdGlvbjogZml4ZWQ7CiAgdG9wOiAwOwogIHJpZ2h0OiAwOwogIGJveC1zaGFkb3c6IDBweCAwcHggMTVweCAwcHggcmdiYSgwLCAwLCAwLCAuMDUpOwogIHRyYW5zaXRpb246IGFsbCAuMjVzIGN1YmljLWJlemllciguNywgLjMsIC4xLCAxKTsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgxMDAlKTsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIHotaW5kZXg6IDQwMDAwOwp9Cgouc2hvdyB7CiAgdHJhbnNpdGlvbjogYWxsIC4zcyBjdWJpYy1iZXppZXIoLjcsIC4zLCAuMSwgMSk7CgogIC5yaWdodFBhbmVsLWJhY2tncm91bmQgewogICAgei1pbmRleDogMjAwMDA7CiAgICBvcGFjaXR5OiAxOwogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDEwMCU7CiAgfQoKICAucmlnaHRQYW5lbCB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgwKTsKICB9Cn0KCi5oYW5kbGUtYnV0dG9uIHsKICB3aWR0aDogNDhweDsKICBoZWlnaHQ6IDQ4cHg7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGxlZnQ6IC00OHB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBmb250LXNpemU6IDI0cHg7CiAgYm9yZGVyLXJhZGl1czogNnB4IDAgMCA2cHggIWltcG9ydGFudDsKICB6LWluZGV4OiAwOwogIHBvaW50ZXItZXZlbnRzOiBhdXRvOwogIGN1cnNvcjogcG9pbnRlcjsKICBjb2xvcjogI2ZmZjsKICBsaW5lLWhlaWdodDogNDhweDsKICBpIHsKICAgIGZvbnQtc2l6ZTogMjRweDsKICAgIGxpbmUtaGVpZ2h0OiA0OHB4OwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightPanel", "sourcesContent": ["<template>\n  <div ref=\"rightPanel\" :class=\"{show:show}\" class=\"rightPanel-container\">\n    <div class=\"rightPanel-background\" />\n    <div class=\"rightPanel\">\n      <div class=\"handle-button\" :style=\"{'top':buttonTop+'px','background-color':theme}\" @click=\"show=!show\">\n        <i :class=\"show?'el-icon-close':'el-icon-setting'\" />\n      </div>\n      <div class=\"rightPanel-items\">\n        <slot />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { addClass, removeClass } from '@/utils'\n\nexport default {\n  name: 'RightPanel',\n  props: {\n    clickNotClose: {\n      default: false,\n      type: Boolean\n    },\n    buttonTop: {\n      default: 250,\n      type: Number\n    }\n  },\n  data() {\n    return {\n      show: false\n    }\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme\n    }\n  },\n  watch: {\n    show(value) {\n      if (value && !this.clickNotClose) {\n        this.addEventClick()\n      }\n      if (value) {\n        addClass(document.body, 'showRightPanel')\n      } else {\n        removeClass(document.body, 'showRightPanel')\n      }\n    }\n  },\n  mounted() {\n    this.insertToBody()\n  },\n  beforeDestroy() {\n    const elx = this.$refs.rightPanel\n    elx.remove()\n  },\n  methods: {\n    addEventClick() {\n      window.addEventListener('click', this.closeSidebar)\n    },\n    closeSidebar(evt) {\n      const parent = evt.target.closest('.rightPanel')\n      if (!parent) {\n        this.show = false\n        window.removeEventListener('click', this.closeSidebar)\n      }\n    },\n    insertToBody() {\n      const elx = this.$refs.rightPanel\n      const body = document.querySelector('body')\n      body.insertBefore(elx, body.firstChild)\n    }\n  }\n}\n</script>\n\n<style>\n.showRightPanel {\n  overflow: hidden;\n  position: relative;\n  width: calc(100% - 15px);\n}\n</style>\n\n<style lang=\"scss\" scoped>\n.rightPanel-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  opacity: 0;\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\n  background: rgba(0, 0, 0, .2);\n  z-index: -1;\n}\n\n.rightPanel {\n  width: 100%;\n  max-width: 260px;\n  height: 100vh;\n  position: fixed;\n  top: 0;\n  right: 0;\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\n  transform: translate(100%);\n  background: #fff;\n  z-index: 40000;\n}\n\n.show {\n  transition: all .3s cubic-bezier(.7, .3, .1, 1);\n\n  .rightPanel-background {\n    z-index: 20000;\n    opacity: 1;\n    width: 100%;\n    height: 100%;\n  }\n\n  .rightPanel {\n    transform: translate(0);\n  }\n}\n\n.handle-button {\n  width: 48px;\n  height: 48px;\n  position: absolute;\n  left: -48px;\n  text-align: center;\n  font-size: 24px;\n  border-radius: 6px 0 0 6px !important;\n  z-index: 0;\n  pointer-events: auto;\n  cursor: pointer;\n  color: #fff;\n  line-height: 48px;\n  i {\n    font-size: 24px;\n    line-height: 48px;\n  }\n}\n</style>\n"]}]}