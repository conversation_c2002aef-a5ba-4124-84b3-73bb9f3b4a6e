{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Screenfull\\index.vue", "mtime": 1731739938000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHNjcmVlbmZ1bGwgZnJvbSAnc2NyZWVuZnVsbCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU2NyZWVuZnVsbCcsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzRnVsbHNjcmVlbjogZmFsc2UKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pbml0KCk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgdGhpcy5kZXN0cm95KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgIGlmICghc2NyZWVuZnVsbC5lbmFibGVkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAneW91IGJyb3dzZXIgY2FuIG5vdCB3b3JrJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBzY3JlZW5mdWxsLnRvZ2dsZSgpOwogICAgfSwKICAgIGNoYW5nZTogZnVuY3Rpb24gY2hhbmdlKCkgewogICAgICB0aGlzLmlzRnVsbHNjcmVlbiA9IHNjcmVlbmZ1bGwuaXNGdWxsc2NyZWVuOwogICAgfSwKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIGlmIChzY3JlZW5mdWxsLmVuYWJsZWQpIHsKICAgICAgICBzY3JlZW5mdWxsLm9uKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSk7CiAgICAgIH0KICAgIH0sCiAgICBkZXN0cm95OiBmdW5jdGlvbiBkZXN0cm95KCkgewogICAgICBpZiAoc2NyZWVuZnVsbC5lbmFibGVkKSB7CiAgICAgICAgc2NyZWVuZnVsbC5vZmYoJ2NoYW5nZScsIHRoaXMuY2hhbmdlKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["screenfull", "name", "data", "isFullscreen", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "methods", "click", "enabled", "$message", "message", "type", "toggle", "change", "on", "off"], "sources": ["src/components/Screenfull/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\n  </div>\n</template>\n\n<script>\nimport screenfull from 'screenfull'\n\nexport default {\n  name: 'Screenfull',\n  data() {\n    return {\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.destroy()\n  },\n  methods: {\n    click() {\n      if (!screenfull.enabled) {\n        this.$message({\n          message: 'you browser can not work',\n          type: 'warning'\n        })\n        return false\n      }\n      screenfull.toggle()\n    },\n    change() {\n      this.isFullscreen = screenfull.isFullscreen\n    },\n    init() {\n      if (screenfull.enabled) {\n        screenfull.on('change', this.change)\n      }\n    },\n    destroy() {\n      if (screenfull.enabled) {\n        screenfull.off('change', this.change)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.screenfull-svg {\n  display: inline-block;\n  cursor: pointer;\n  fill: #5a5e66;;\n  width: 20px;\n  height: 20px;\n  vertical-align: 10px;\n}\n</style>\n"], "mappings": "AAOA,OAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAT,UAAA,CAAAU,OAAA;QACA,KAAAC,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA;MACA;MACAb,UAAA,CAAAc,MAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAZ,YAAA,GAAAH,UAAA,CAAAG,YAAA;IACA;IACAE,IAAA,WAAAA,KAAA;MACA,IAAAL,UAAA,CAAAU,OAAA;QACAV,UAAA,CAAAgB,EAAA,gBAAAD,MAAA;MACA;IACA;IACAR,OAAA,WAAAA,QAAA;MACA,IAAAP,UAAA,CAAAU,OAAA;QACAV,UAAA,CAAAiB,GAAA,gBAAAF,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}