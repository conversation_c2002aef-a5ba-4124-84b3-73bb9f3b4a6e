{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\RightPanel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\RightPanel\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IGFkZENsYXNzLCByZW1vdmVDbGFzcyB9IGZyb20gJ0AvdXRpbHMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1JpZ2h0UGFuZWwnLAogIHByb3BzOiB7CiAgICBjbGlja05vdENsb3NlOiB7CiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgICB0eXBlOiBCb29sZWFuCiAgICB9LAogICAgYnV0dG9uVG9wOiB7CiAgICAgIGRlZmF1bHQ6IDI1MCwKICAgICAgdHlwZTogTnVtYmVyCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2hvdzogZmFsc2UKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICB0aGVtZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgc2hvdyh2YWx1ZSkgewogICAgICBpZiAodmFsdWUgJiYgIXRoaXMuY2xpY2tOb3RDbG9zZSkgewogICAgICAgIHRoaXMuYWRkRXZlbnRDbGljaygpCiAgICAgIH0KICAgICAgaWYgKHZhbHVlKSB7CiAgICAgICAgYWRkQ2xhc3MoZG9jdW1lbnQuYm9keSwgJ3Nob3dSaWdodFBhbmVsJykKICAgICAgfSBlbHNlIHsKICAgICAgICByZW1vdmVDbGFzcyhkb2N1bWVudC5ib2R5LCAnc2hvd1JpZ2h0UGFuZWwnKQogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5pbnNlcnRUb0JvZHkoKQogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIGNvbnN0IGVseCA9IHRoaXMuJHJlZnMucmlnaHRQYW5lbAogICAgZWx4LnJlbW92ZSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBhZGRFdmVudENsaWNrKCkgewogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlU2lkZWJhcikKICAgIH0sCiAgICBjbG9zZVNpZGViYXIoZXZ0KSB7CiAgICAgIGNvbnN0IHBhcmVudCA9IGV2dC50YXJnZXQuY2xvc2VzdCgnLnJpZ2h0UGFuZWwnKQogICAgICBpZiAoIXBhcmVudCkgewogICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlCiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5jbG9zZVNpZGViYXIpCiAgICAgIH0KICAgIH0sCiAgICBpbnNlcnRUb0JvZHkoKSB7CiAgICAgIGNvbnN0IGVseCA9IHRoaXMuJHJlZnMucmlnaHRQYW5lbAogICAgICBjb25zdCBib2R5ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignYm9keScpCiAgICAgIGJvZHkuaW5zZXJ0QmVmb3JlKGVseCwgYm9keS5maXJzdENoaWxkKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAeA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightPanel", "sourcesContent": ["<template>\n  <div ref=\"rightPanel\" :class=\"{show:show}\" class=\"rightPanel-container\">\n    <div class=\"rightPanel-background\" />\n    <div class=\"rightPanel\">\n      <div class=\"handle-button\" :style=\"{'top':buttonTop+'px','background-color':theme}\" @click=\"show=!show\">\n        <i :class=\"show?'el-icon-close':'el-icon-setting'\" />\n      </div>\n      <div class=\"rightPanel-items\">\n        <slot />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { addClass, removeClass } from '@/utils'\n\nexport default {\n  name: 'RightPanel',\n  props: {\n    clickNotClose: {\n      default: false,\n      type: Boolean\n    },\n    buttonTop: {\n      default: 250,\n      type: Number\n    }\n  },\n  data() {\n    return {\n      show: false\n    }\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme\n    }\n  },\n  watch: {\n    show(value) {\n      if (value && !this.clickNotClose) {\n        this.addEventClick()\n      }\n      if (value) {\n        addClass(document.body, 'showRightPanel')\n      } else {\n        removeClass(document.body, 'showRightPanel')\n      }\n    }\n  },\n  mounted() {\n    this.insertToBody()\n  },\n  beforeDestroy() {\n    const elx = this.$refs.rightPanel\n    elx.remove()\n  },\n  methods: {\n    addEventClick() {\n      window.addEventListener('click', this.closeSidebar)\n    },\n    closeSidebar(evt) {\n      const parent = evt.target.closest('.rightPanel')\n      if (!parent) {\n        this.show = false\n        window.removeEventListener('click', this.closeSidebar)\n      }\n    },\n    insertToBody() {\n      const elx = this.$refs.rightPanel\n      const body = document.querySelector('body')\n      body.insertBefore(elx, body.firstChild)\n    }\n  }\n}\n</script>\n\n<style>\n.showRightPanel {\n  overflow: hidden;\n  position: relative;\n  width: calc(100% - 15px);\n}\n</style>\n\n<style lang=\"scss\" scoped>\n.rightPanel-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  opacity: 0;\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\n  background: rgba(0, 0, 0, .2);\n  z-index: -1;\n}\n\n.rightPanel {\n  width: 100%;\n  max-width: 260px;\n  height: 100vh;\n  position: fixed;\n  top: 0;\n  right: 0;\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\n  transform: translate(100%);\n  background: #fff;\n  z-index: 40000;\n}\n\n.show {\n  transition: all .3s cubic-bezier(.7, .3, .1, 1);\n\n  .rightPanel-background {\n    z-index: 20000;\n    opacity: 1;\n    width: 100%;\n    height: 100%;\n  }\n\n  .rightPanel {\n    transform: translate(0);\n  }\n}\n\n.handle-button {\n  width: 48px;\n  height: 48px;\n  position: absolute;\n  left: -48px;\n  text-align: center;\n  font-size: 24px;\n  border-radius: 6px 0 0 6px !important;\n  z-index: 0;\n  pointer-events: auto;\n  cursor: pointer;\n  color: #fff;\n  line-height: 48px;\n  i {\n    font-size: 24px;\n    line-height: 48px;\n  }\n}\n</style>\n"]}]}