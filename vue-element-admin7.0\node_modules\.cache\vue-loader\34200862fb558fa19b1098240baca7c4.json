{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\directive.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\directive.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBwZXJtaXNzaW9uIGZyb20gJ0AvZGlyZWN0aXZlL3Blcm1pc3Npb24vaW5kZXguanMnIC8vIOadg+mZkOWIpOaWreaMh+S7pAppbXBvcnQgY2hlY2tQZXJtaXNzaW9uIGZyb20gJ0AvdXRpbHMvcGVybWlzc2lvbicgLy8g5p2D6ZmQ5Yik5pat5Ye95pWwCmltcG9ydCBTd2l0Y2hSb2xlcyBmcm9tICcuL2NvbXBvbmVudHMvU3dpdGNoUm9sZXMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0RpcmVjdGl2ZVBlcm1pc3Npb24nLAogIGNvbXBvbmVudHM6IHsgU3dpdGNoUm9sZXMgfSwKICBkaXJlY3RpdmVzOiB7IHBlcm1pc3Npb24gfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAga2V5OiAxIC8vIOS4uuS6huiDveavj+asoeWIh+aNouadg+mZkOeahOaXtuWAmemHjeaWsOWIneWni+WMluaMh+S7pAogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgY2hlY2tQZXJtaXNzaW9uLAogICAgaGFuZGxlUm9sZXNDaGFuZ2UoKSB7CiAgICAgIHRoaXMua2V5KysKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["directive.vue"], "names": [], "mappings": ";AAqEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "directive.vue", "sourceRoot": "src/views/permission", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <switch-roles @change=\"handleRolesChange\" />\n    <div :key=\"key\" style=\"margin-top:30px;\">\n      <div>\n        <span v-permission=\"['admin']\" class=\"permission-alert\">\n          Only\n          <el-tag class=\"permission-tag\" size=\"small\">admin</el-tag> can see this\n        </span>\n        <el-tag v-permission=\"['admin']\" class=\"permission-sourceCode\" type=\"info\">\n          v-permission=\"['admin']\"\n        </el-tag>\n      </div>\n\n      <div>\n        <span v-permission=\"['editor']\" class=\"permission-alert\">\n          Only\n          <el-tag class=\"permission-tag\" size=\"small\">editor</el-tag> can see this\n        </span>\n        <el-tag v-permission=\"['editor']\" class=\"permission-sourceCode\" type=\"info\">\n          v-permission=\"['editor']\"\n        </el-tag>\n      </div>\n\n      <div>\n        <span v-permission=\"['admin','editor']\" class=\"permission-alert\">\n          Both\n          <el-tag class=\"permission-tag\" size=\"small\">admin</el-tag> and\n          <el-tag class=\"permission-tag\" size=\"small\">editor</el-tag> can see this\n        </span>\n        <el-tag v-permission=\"['admin','editor']\" class=\"permission-sourceCode\" type=\"info\">\n          v-permission=\"['admin','editor']\"\n        </el-tag>\n      </div>\n    </div>\n\n    <div :key=\"'checkPermission'+key\" style=\"margin-top:60px;\">\n      <aside>\n        In some cases, using v-permission will have no effect. For example: Element-UI's Tab component or el-table-column and other scenes that dynamically render dom. You can only do this with v-if.\n        <br> e.g.\n      </aside>\n\n      <el-tabs type=\"border-card\" style=\"width:550px;\">\n        <el-tab-pane v-if=\"checkPermission(['admin'])\" label=\"Admin\">\n          Admin can see this\n          <el-tag class=\"permission-sourceCode\" type=\"info\">\n            v-if=\"checkPermission(['admin'])\"\n          </el-tag>\n        </el-tab-pane>\n\n        <el-tab-pane v-if=\"checkPermission(['editor'])\" label=\"Editor\">\n          Editor can see this\n          <el-tag class=\"permission-sourceCode\" type=\"info\">\n            v-if=\"checkPermission(['editor'])\"\n          </el-tag>\n        </el-tab-pane>\n\n        <el-tab-pane v-if=\"checkPermission(['admin','editor'])\" label=\"Admin-OR-Editor\">\n          Both admin or editor can see this\n          <el-tag class=\"permission-sourceCode\" type=\"info\">\n            v-if=\"checkPermission(['admin','editor'])\"\n          </el-tag>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n  </div>\n</template>\n\n<script>\nimport permission from '@/directive/permission/index.js' // 权限判断指令\nimport checkPermission from '@/utils/permission' // 权限判断函数\nimport SwitchRoles from './components/SwitchRoles'\n\nexport default {\n  name: 'DirectivePermission',\n  components: { SwitchRoles },\n  directives: { permission },\n  data() {\n    return {\n      key: 1 // 为了能每次切换权限的时候重新初始化指令\n    }\n  },\n  methods: {\n    checkPermission,\n    handleRolesChange() {\n      this.key++\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  ::v-deep .permission-alert {\n    width: 320px;\n    margin-top: 15px;\n    background-color: #f0f9eb;\n    color: #67c23a;\n    padding: 8px 16px;\n    border-radius: 4px;\n    display: inline-block;\n  }\n  ::v-deep .permission-sourceCode {\n    margin-left: 15px;\n  }\n  ::v-deep .permission-tag {\n    background-color: #ecf5ff;\n  }\n}\n</style>\n\n"]}]}