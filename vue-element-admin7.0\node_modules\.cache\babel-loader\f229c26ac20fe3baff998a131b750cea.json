{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\ErrorLog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\ErrorLog\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdFcnJvckxvZycsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1RhYmxlVmlzaWJsZTogZmFsc2UKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgZXJyb3JMb2dzOiBmdW5jdGlvbiBlcnJvckxvZ3MoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5nZXR0ZXJzLmVycm9yTG9nczsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGNsZWFyQWxsOiBmdW5jdGlvbiBjbGVhckFsbCgpIHsKICAgICAgdGhpcy5kaWFsb2dUYWJsZVZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2Vycm9yTG9nL2NsZWFyRXJyb3JMb2cnKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "data", "dialogTableVisible", "computed", "errorLogs", "$store", "getters", "methods", "clearAll", "dispatch"], "sources": ["src/components/ErrorLog/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"errorLogs.length>0\">\n    <el-badge :is-dot=\"true\" style=\"line-height: 25px;margin-top: -5px;\" @click.native=\"dialogTableVisible=true\">\n      <el-button style=\"padding: 8px 10px;\" size=\"small\" type=\"danger\">\n        <svg-icon icon-class=\"bug\" />\n      </el-button>\n    </el-badge>\n\n    <el-dialog :visible.sync=\"dialogTableVisible\" width=\"80%\" append-to-body>\n      <div slot=\"title\">\n        <span style=\"padding-right: 10px;\">Error Log</span>\n        <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-delete\" @click=\"clearAll\">Clear All</el-button>\n      </div>\n      <el-table :data=\"errorLogs\" border>\n        <el-table-column label=\"Message\">\n          <template slot-scope=\"{row}\">\n            <div>\n              <span class=\"message-title\">Msg:</span>\n              <el-tag type=\"danger\">\n                {{ row.err.message }}\n              </el-tag>\n            </div>\n            <br>\n            <div>\n              <span class=\"message-title\" style=\"padding-right: 10px;\">Info: </span>\n              <el-tag type=\"warning\">\n                {{ row.vm.$vnode.tag }} error in {{ row.info }}\n              </el-tag>\n            </div>\n            <br>\n            <div>\n              <span class=\"message-title\" style=\"padding-right: 16px;\">Url: </span>\n              <el-tag type=\"success\">\n                {{ row.url }}\n              </el-tag>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"Stack\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.err.stack }}\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ErrorLog',\n  data() {\n    return {\n      dialogTableVisible: false\n    }\n  },\n  computed: {\n    errorLogs() {\n      return this.$store.getters.errorLogs\n    }\n  },\n  methods: {\n    clearAll() {\n      this.dialogTableVisible = false\n      this.$store.dispatch('errorLog/clearErrorLog')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.message-title {\n  font-size: 16px;\n  color: #333;\n  font-weight: bold;\n  padding-right: 8px;\n}\n</style>\n"], "mappings": "AAiDA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAAC,MAAA,CAAAC,OAAA,CAAAF,SAAA;IACA;EACA;EACAG,OAAA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAN,kBAAA;MACA,KAAAG,MAAA,CAAAI,QAAA;IACA;EACA;AACA", "ignoreList": []}]}