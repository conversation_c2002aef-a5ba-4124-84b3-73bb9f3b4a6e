{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\getters.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\getters.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7CnZhciBnZXR0ZXJzID0gewogIHNpZGViYXI6IGZ1bmN0aW9uIHNpZGViYXIoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuc2lkZWJhcjsKICB9LAogIHNpemU6IGZ1bmN0aW9uIHNpemUoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuc2l6ZTsKICB9LAogIGRldmljZTogZnVuY3Rpb24gZGV2aWNlKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUuYXBwLmRldmljZTsKICB9LAogIHZpc2l0ZWRWaWV3czogZnVuY3Rpb24gdmlzaXRlZFZpZXdzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudGFnc1ZpZXcudmlzaXRlZFZpZXdzOwogIH0sCiAgY2FjaGVkVmlld3M6IGZ1bmN0aW9uIGNhY2hlZFZpZXdzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudGFnc1ZpZXcuY2FjaGVkVmlld3M7CiAgfSwKICB0b2tlbjogZnVuY3Rpb24gdG9rZW4oc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLnRva2VuOwogIH0sCiAgYXZhdGFyOiBmdW5jdGlvbiBhdmF0YXIoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLmF2YXRhcjsKICB9LAogIG5hbWU6IGZ1bmN0aW9uIG5hbWUoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLm5hbWU7CiAgfSwKICBpbnRyb2R1Y3Rpb246IGZ1bmN0aW9uIGludHJvZHVjdGlvbihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIuaW50cm9kdWN0aW9uOwogIH0sCiAgcm9sZXM6IGZ1bmN0aW9uIHJvbGVzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5yb2xlczsKICB9LAogIHBlcm1pc3Npb25fcm91dGVzOiBmdW5jdGlvbiBwZXJtaXNzaW9uX3JvdXRlcyhzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnBlcm1pc3Npb24ucm91dGVzOwogIH0sCiAgZXJyb3JMb2dzOiBmdW5jdGlvbiBlcnJvckxvZ3Moc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5lcnJvckxvZy5sb2dzOwogIH0KfTsKZXhwb3J0IGRlZmF1bHQgZ2V0dGVyczs="}, {"version": 3, "names": ["getters", "sidebar", "state", "app", "size", "device", "visitedViews", "tagsView", "cachedViews", "token", "user", "avatar", "name", "introduction", "roles", "permission_routes", "permission", "routes", "errorLogs", "errorLog", "logs"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/store/getters.js"], "sourcesContent": ["const getters = {\n  sidebar: state => state.app.sidebar,\n  size: state => state.app.size,\n  device: state => state.app.device,\n  visitedViews: state => state.tagsView.visitedViews,\n  cachedViews: state => state.tagsView.cachedViews,\n  token: state => state.user.token,\n  avatar: state => state.user.avatar,\n  name: state => state.user.name,\n  introduction: state => state.user.introduction,\n  roles: state => state.user.roles,\n  permission_routes: state => state.permission.routes,\n  errorLogs: state => state.errorLog.logs\n}\nexport default getters\n"], "mappings": ";AAAA,IAAMA,OAAO,GAAG;EACdC,OAAO,EAAE,SAATA,OAAOA,CAAEC,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACF,OAAO;EAAA;EACnCG,IAAI,EAAE,SAANA,IAAIA,CAAEF,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACC,IAAI;EAAA;EAC7BC,MAAM,EAAE,SAARA,MAAMA,CAAEH,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACE,MAAM;EAAA;EACjCC,YAAY,EAAE,SAAdA,YAAYA,CAAEJ,KAAK;IAAA,OAAIA,KAAK,CAACK,QAAQ,CAACD,YAAY;EAAA;EAClDE,WAAW,EAAE,SAAbA,WAAWA,CAAEN,KAAK;IAAA,OAAIA,KAAK,CAACK,QAAQ,CAACC,WAAW;EAAA;EAChDC,KAAK,EAAE,SAAPA,KAAKA,CAAEP,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACD,KAAK;EAAA;EAChCE,MAAM,EAAE,SAARA,MAAMA,CAAET,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACC,MAAM;EAAA;EAClCC,IAAI,EAAE,SAANA,IAAIA,CAAEV,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACE,IAAI;EAAA;EAC9BC,YAAY,EAAE,SAAdA,YAAYA,CAAEX,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACG,YAAY;EAAA;EAC9CC,KAAK,EAAE,SAAPA,KAAKA,CAAEZ,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACI,KAAK;EAAA;EAChCC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAEb,KAAK;IAAA,OAAIA,KAAK,CAACc,UAAU,CAACC,MAAM;EAAA;EACnDC,SAAS,EAAE,SAAXA,SAASA,CAAEhB,KAAK;IAAA,OAAIA,KAAK,CAACiB,QAAQ,CAACC,IAAI;EAAA;AACzC,CAAC;AACD,eAAepB,OAAO", "ignoreList": []}]}