{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["RightPanel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Settings", "Sidebar", "TagsView", "ResizeMixin", "mapState", "name", "components", "mixins", "computed", "_objectSpread", "sidebar", "state", "app", "device", "showSettings", "settings", "needTagsView", "tagsView", "fixedHeader", "classObj", "hideSidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "methods", "handleClickOutside", "$store", "dispatch"], "sources": ["src/layout/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\n    <sidebar class=\"sidebar-container\" />\n    <div :class=\"{hasTagsView:needTagsView}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar />\n        <tags-view v-if=\"needTagsView\" />\n      </div>\n      <app-main />\n      <right-panel v-if=\"showSettings\">\n        <settings />\n      </right-panel>\n    </div>\n  </div>\n</template>\n\n<script>\nimport RightPanel from '@/components/RightPanel'\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    <PERSON>Panel,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      showSettings: state => state.settings.showSettings,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/styles/mixin.scss\";\n  @import \"~@/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$sideBarWidth});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px)\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"], "mappings": ";AAkBA,OAAAA,UAAA;AACA,SAAAC,OAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,QAAA;AACA,OAAAC,WAAA;AACA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAR,OAAA,EAAAA,OAAA;IACAC,MAAA,EAAAA,MAAA;IACAF,UAAA,EAAAA,UAAA;IACAG,QAAA,EAAAA,QAAA;IACAC,OAAA,EAAAA,OAAA;IACAC,QAAA,EAAAA;EACA;EACAK,MAAA,GAAAJ,WAAA;EACAK,QAAA,EAAAC,aAAA,CAAAA,aAAA,KACAL,QAAA;IACAM,OAAA,WAAAA,QAAAC,KAAA;MAAA,OAAAA,KAAA,CAAAC,GAAA,CAAAF,OAAA;IAAA;IACAG,MAAA,WAAAA,OAAAF,KAAA;MAAA,OAAAA,KAAA,CAAAC,GAAA,CAAAC,MAAA;IAAA;IACAC,YAAA,WAAAA,aAAAH,KAAA;MAAA,OAAAA,KAAA,CAAAI,QAAA,CAAAD,YAAA;IAAA;IACAE,YAAA,WAAAA,aAAAL,KAAA;MAAA,OAAAA,KAAA,CAAAI,QAAA,CAAAE,QAAA;IAAA;IACAC,WAAA,WAAAA,YAAAP,KAAA;MAAA,OAAAA,KAAA,CAAAI,QAAA,CAAAG,WAAA;IAAA;EACA;IACAC,QAAA,WAAAA,SAAA;MACA;QACAC,WAAA,QAAAV,OAAA,CAAAW,MAAA;QACAC,WAAA,OAAAZ,OAAA,CAAAW,MAAA;QACAE,gBAAA,OAAAb,OAAA,CAAAa,gBAAA;QACAC,MAAA,OAAAX,MAAA;MACA;IACA;EAAA,EACA;EACAY,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;QAAAL,gBAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}