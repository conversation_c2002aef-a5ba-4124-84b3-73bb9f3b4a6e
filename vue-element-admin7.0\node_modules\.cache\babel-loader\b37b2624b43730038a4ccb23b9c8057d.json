{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\components\\SwitchRoles.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\components\\SwitchRoles.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGNvbXB1dGVkOiB7CiAgICByb2xlczogZnVuY3Rpb24gcm9sZXMoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOwogICAgfSwKICAgIHN3aXRjaFJvbGVzOiB7CiAgICAgIGdldDogZnVuY3Rpb24gZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLnJvbGVzWzBdOwogICAgICB9LAogICAgICBzZXQ6IGZ1bmN0aW9uIHNldCh2YWwpIHsKICAgICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCd1c2VyL2NoYW5nZVJvbGVzJywgdmFsKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzLiRlbWl0KCdjaGFuZ2UnKTsKICAgICAgICB9KTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["computed", "roles", "$store", "getters", "switchRoles", "get", "set", "val", "_this", "dispatch", "then", "$emit"], "sources": ["src/views/permission/components/SwitchRoles.vue"], "sourcesContent": ["<template>\n  <div>\n    <div style=\"margin-bottom:15px;\">\n      Your roles: {{ roles }}\n    </div>\n    Switch roles:\n    <el-radio-group v-model=\"switchRoles\">\n      <el-radio-button label=\"editor\" />\n      <el-radio-button label=\"admin\" />\n    </el-radio-group>\n  </div>\n</template>\n\n<script>\nexport default {\n  computed: {\n    roles() {\n      return this.$store.getters.roles\n    },\n    switchRoles: {\n      get() {\n        return this.roles[0]\n      },\n      set(val) {\n        this.$store.dispatch('user/changeRoles', val).then(() => {\n          this.$emit('change')\n        })\n      }\n    }\n  }\n}\n</script>\n"], "mappings": "AAcA;EACAA,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,MAAA,CAAAC,OAAA,CAAAF,KAAA;IACA;IACAG,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAJ,KAAA;MACA;MACAK,GAAA,WAAAA,IAAAC,GAAA;QAAA,IAAAC,KAAA;QACA,KAAAN,MAAA,CAAAO,QAAA,qBAAAF,GAAA,EAAAG,IAAA;UACAF,KAAA,CAAAG,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}