{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\validate.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\validate.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isExternal", "path", "test", "validUsername", "str", "valid_map", "indexOf", "trim", "validURL", "url", "reg", "validLowerCase", "validUpperCase", "validAlphabets", "validEmail", "email", "isString", "String", "isArray", "arg", "Array", "Object", "prototype", "toString", "call"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/utils/validate.js"], "sourcesContent": ["/**\n * Created by PanJiaChen on 16/11/18.\n */\n\n/**\n * @param {string} path\n * @returns {Boolean}\n */\nexport function isExternal(path) {\n  return /^(https?:|mailto:|tel:)/.test(path)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUsername(str) {\n  const valid_map = ['admin', 'editor']\n  return valid_map.indexOf(str.trim()) >= 0\n}\n\n/**\n * @param {string} url\n * @returns {Boolean}\n */\nexport function validURL(url) {\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\n  return reg.test(url)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validLowerCase(str) {\n  const reg = /^[a-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUpperCase(str) {\n  const reg = /^[A-Z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validAlphabets(str) {\n  const reg = /^[A-Za-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} email\n * @returns {Boolean}\n */\nexport function validEmail(email) {\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\n  return reg.test(email)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function isString(str) {\n  if (typeof str === 'string' || str instanceof String) {\n    return true\n  }\n  return false\n}\n\n/**\n * @param {Array} arg\n * @returns {Boolean}\n */\nexport function isArray(arg) {\n  if (typeof Array.isArray === 'undefined') {\n    return Object.prototype.toString.call(arg) === '[object Array]'\n  }\n  return Array.isArray(arg)\n}\n"], "mappings": ";;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACC,IAAI,CAACD,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACC,OAAO,CAACF,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAMC,GAAG,GAAG,4TAA4T;EACxU,OAAOA,GAAG,CAACR,IAAI,CAACO,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,cAAcA,CAACP,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,cAAcA,CAACR,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASS,cAAcA,CAACT,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,aAAa;EACzB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASU,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAML,GAAG,GAAG,yJAAyJ;EACrK,OAAOA,GAAG,CAACR,IAAI,CAACa,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACZ,GAAG,EAAE;EAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYa,MAAM,EAAE;IACpD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;IACxC,OAAOG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;EACjE;EACA,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B", "ignoreList": []}]}