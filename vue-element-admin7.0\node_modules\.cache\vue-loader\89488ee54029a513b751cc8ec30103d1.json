{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\table\\complex-table.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\table\\complex-table.vue", "mtime": 1747749393579}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["complex-table.vue"], "names": [], "mappings": ";AAwNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "complex-table.vue", "sourceRoot": "src/views/table", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"filter-container\">\n      <el-select\n        v-model=\"tableName\"\n        placeholder=\"数据表\"\n        no-data-text=\"已经没有数据表了\"\n        style=\"margin-left: 20px\"\n        @focus=\"handleSearch\"\n        @change=\"handleSelectChange\"\n      >\n        <el-option\n          v-for=\"item in options\"\n          :key=\"item.value\"\n          :label=\"item.label\"\n          :value=\"item.value\"\n        />\n      </el-select>\n      <el-input\n        v-model=\"username\"\n        placeholder=\"用户名\"\n        style=\"width: 200px; margin-top: 7px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-input\n        v-model=\"opponent\"\n        placeholder=\"对手户名\"\n        style=\"width: 200px; margin-top: 7px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-input\n        v-model=\"cardNumber\"\n        placeholder=\"银行卡号\"\n        style=\"width: 200px; margin-top: 7px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-input\n        v-model=\"remark\"\n        placeholder=\"备注\"\n        style=\"width: 200px; margin-top: 7px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-button\n        class=\"filter-item search_submit\"\n        type=\"primary\"\n        icon=\"el-icon-search\"\n        @click=\"handleFilter\"\n      >\n        搜索\n      </el-button>\n    </div>\n\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%\"\n      @sort-change=\"sortChange\"\n    >\n      <!-- <el-table-column label=\"ID\" prop=\"id\" sortable=\"custom\" align=\"center\" width=\"80\" :class-name=\"getSortClass('id')\">\n        <template slot-scope=\"{row}\">\n          <span>{{ row.[0].value }}</span>\n        </template>\n      </el-table-column> -->\n      <el-table-column\n        label=\"交易户名\"\n        prop=\"account\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.account }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"交易卡号\" prop=\"cardNumber\" min-width=\"150px\">\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.cardNumber }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"交易账号\" prop=\"accountNumber\" min-width=\"150px\">\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.accountNumber }}</span>\n          <!-- <span class=\"link-cardNumber\" @click=\"handleUpdate(row)\">{{ row.title }}</span>\n          <el-tag>{{ row.type | typeFilter }}</el-tag> -->\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"交易时间\"\n        prop=\"time\"\n        width=\"160px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.time }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"收付标志\"\n        prop=\"signal\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.signal }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"交易金额\"\n        prop=\"tractionMoney\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.tractionMoney }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"交易余额\"\n        prop=\"transactionBalance\"\n        width=\"160px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.transactionBalance }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"交易币种\"\n        prop=\"currence\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.currence }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"对手账户\"\n        prop=\"counterAccountNumber\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.counterAccountNumber }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"对手户名\"\n        prop=\"counterAccount\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.counterAccount }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"交易网点名称\"\n        prop=\"outlet\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.outlet }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"对手开户银行\"\n        prop=\"counterBank\"\n        width=\"150px\"\n        align=\"center\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.counterBank }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"备注\" prop=\"remark\" width=\"150px\">\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.remark }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"摘要说明\" prop=\"statement\" width=\"150px\">\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.statement }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"ip\" prop=\"ip\" width=\"150px\">\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.ip }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"mac\" prop=\"mac\" width=\"150px\">\n        <template slot-scope=\"{ row }\">\n          <span>{{ row.mac }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.limit\"\n      @pagination=\"getList\"\n    />\n  </div>\n</template>\n\n<script>\nimport axios from 'axios'\nimport {\n// fetchList,\n// fetchPv,\n// createArticle,\n// updateArticle\n} from '@/api/article'\nimport waves from '@/directive/waves' // waves directive\n// import { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination' // secondary package based on el-pagination\n\nexport default {\n  name: 'ComplexTable',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      options: [],\n      tableName: '', // 选中的数据表\n      tableKey: 0,\n      list: null,\n      rawData: null,\n      total: 0,\n      listLoading: true,\n      username: '',\n      opponent: '',\n      cardNumber: '',\n      remark: '',\n      listQuery: {\n        tableName: null,\n        page: 1,\n        limit: 10,\n        name: '金吉子',\n        opponent: null,\n        account: null,\n        remark: null // 新增备注输入框\n      },\n      showReviewer: false,\n      downloadLoading: false\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      axios\n        .post('http://127.0.0.1:8000/all_transaction_history', this.listQuery) // 使用 POST 请求\n        .then((response) => {\n          this.rawData = response.data.items\n          this.total = response.data.total\n          this.list = this.rawData.map((item) => {\n            return {\n              account: item[0] === 'None' ? '空' : item[0],\n              cardNumber: item[1] === 'None' ? '空' : item[1],\n              accountNumber: item[2] === 'None' ? '空' : item[2],\n              time: item[3] === 'None' ? '空' : item[3],\n              signal: item[4] === 'None' ? '空' : item[4],\n              tractionMoney: item[5] === 'None' ? '空' : Number(item[5]), // 转换为数字\n              transactionBalance: item[6] === 'None' ? '空' : Number(item[6]),\n              currence: item[7] === 'None' ? '空' : item[7],\n              counterAccountNumber: item[8] === 'None' ? '空' : item[8],\n              counterAccount: item[9] === 'None' ? '空' : item[9],\n              outlet: item[10] === 'None' ? '空' : item[10],\n              counterBank: item[11] === 'None' ? '空' : item[11],\n              remark: item[12] === 'None' ? '空' : item[12],\n              statement: item[13] === 'None' ? '空' : item[13],\n              ip: item[14] === 'None' ? '空' : item[14],\n              mac: item[15] === 'None' ? '空' : item[15]\n            }\n          })\n        })\n        .catch((error) => {\n          console.error('Error fetching the list:', error)\n        })\n        .finally(() => {\n          // 模拟请求时间\n          setTimeout(() => {\n            this.listLoading = false\n          }, 1500)\n        })\n    },\n    handleFilter() {\n      console.log('handleFilter called')\n      this.listQuery.page = 1\n      this.listQuery.name = this.username || null\n      this.listQuery.account = this.cardNumber || null\n      this.listQuery.remark = this.remark || null\n      this.listQuery.opponent = this.opponent || null\n      this.listQuery.tableName = this.tableName\n      console.log(this.listQuery)\n      this.getList()\n    },\n    handleModifyStatus(row, status) {\n      this.$message({\n        message: '操作Success',\n        type: 'success'\n      })\n      row.status = status\n    },\n    sortChange(data) {\n      const { prop, order } = data\n      if (prop === 'id') {\n        this.sortByID(order)\n      }\n    },\n    sortByID(order) {\n      if (order === 'ascending') {\n        this.listQuery.sort = '+id'\n      } else {\n        this.listQuery.sort = '-id'\n      }\n      this.handleFilter()\n    },\n    getSortClass: function(key) {\n      const sort = this.listQuery.sort\n      return sort === `+${key}` ? 'ascending' : 'descending'\n    },\n    handleSearch() {\n      // 发送交易数据到后端\n      axios\n        .get('http://127.0.0.1:8000/all_tables')\n        .then((response) => {\n          console.log(response.data)\n          const data = response.data.all_tables\n          this.options = data.map((item) => ({\n            label: item, // 使用字符串作为 label\n            value: item // 使用相同的字符串作为 value\n          }))\n        })\n        .catch((error) => {\n          this.$message.error('上传失败:', error)\n        })\n    }\n  }\n}\n</script>\n\n<style>\n.filter-item {\n  margin: 0 15px;\n}\n.search_submit {\n  position: relative; /* 相对定位，以便伪元素定位 */\n  overflow: hidden; /* 隐藏溢出部分 */\n  border: none; /* 去掉按钮边框 */\n  background-color: #007bffd5; /* 按钮背景颜色 */\n  color: white; /* 字体颜色 */\n  padding: 10px 20px; /* 按钮内边距 */\n  border-radius: 4px; /* 圆角 */\n  cursor: pointer; /* 鼠标指针 */\n  transition: background-color 0.3s; /* 背景颜色过渡 */\n}\n</style>\n"]}]}