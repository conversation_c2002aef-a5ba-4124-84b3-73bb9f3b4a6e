{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Navbar.vue", "mtime": 1747748935263}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5uYXZiYXIgewogIGhlaWdodDogNTBweDsKICBvdmVyZmxvdzogaGlkZGVuOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJveC1zaGFkb3c6IDAgMXB4IDRweCByZ2JhKDAsMjEsNDEsLjA4KTsKCiAgLmhhbWJ1cmdlci1jb250YWluZXIgewogICAgbGluZS1oZWlnaHQ6IDQ2cHg7CiAgICBoZWlnaHQ6IDEwMCU7CiAgICBmbG9hdDogbGVmdDsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQgLjNzOwogICAgLXdlYmtpdC10YXAtaGlnaGxpZ2h0LWNvbG9yOnRyYW5zcGFyZW50OwoKICAgICY6aG92ZXIgewogICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIC4wMjUpCiAgICB9CiAgfQoKICAuYnJlYWRjcnVtYi1jb250YWluZXIgewogICAgZmxvYXQ6IGxlZnQ7CiAgfQoKICAuZXJyTG9nLWNvbnRhaW5lciB7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICB2ZXJ0aWNhbC1hbGlnbjogdG9wOwogIH0KCiAgLnJpZ2h0LW1lbnUgewogICAgZmxvYXQ6IHJpZ2h0OwogICAgaGVpZ2h0OiAxMDAlOwogICAgbGluZS1oZWlnaHQ6IDUwcHg7CgogICAgJjpmb2N1cyB7CiAgICAgIG91dGxpbmU6IG5vbmU7CiAgICB9CgogICAgLnJpZ2h0LW1lbnUtaXRlbSB7CiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgcGFkZGluZzogMCA4cHg7CiAgICAgIGhlaWdodDogMTAwJTsKICAgICAgZm9udC1zaXplOiAxOHB4OwogICAgICBjb2xvcjogIzVhNWU2NjsKICAgICAgdmVydGljYWwtYWxpZ246IHRleHQtYm90dG9tOwoKICAgICAgJi5ob3Zlci1lZmZlY3QgewogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIC4zczsKCiAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIC4wMjUpCiAgICAgICAgfQogICAgICB9CiAgICB9CgogICAgLmF2YXRhci1jb250YWluZXIgewogICAgICBtYXJnaW4tcmlnaHQ6IDMwcHg7CgogICAgICAuYXZhdGFyLXdyYXBwZXIgewogICAgICAgIG1hcmdpbi10b3A6IDVweDsKICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CgogICAgICAgIC51c2VyLWF2YXRhciB7CiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgICB3aWR0aDogNDBweDsKICAgICAgICAgIGhlaWdodDogNDBweDsKICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7CiAgICAgICAgfQoKICAgICAgICAuZWwtaWNvbi1jYXJldC1ib3R0b20gewogICAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgcmlnaHQ6IC0yMHB4OwogICAgICAgICAgdG9wOiAyNXB4OwogICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";AAqFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" />\n\n    <div class=\"right-menu\">\n      <template v-if=\"device!=='mobile'\">\n        <search id=\"header-search\" class=\"right-menu-item\" />\n\n        <error-log class=\"errLog-container right-menu-item hover-effect\" />\n\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\n\n        <el-tooltip content=\"Global Size\" effect=\"dark\" placement=\"bottom\">\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\n        </el-tooltip>\n\n      </template>\n\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"require('@/assets/used_images/钱.png')\" class=\"user-avatar\" alt=\"跳舞的小人\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <!-- <router-link to=\"/profile/index\">\n            <el-dropdown-item>档案</el-dropdown-item>\n          </router-link> -->\n          <router-link to=\"/\">\n            <el-dropdown-item>主界面</el-dropdown-item>\n          </router-link>\n          <!-- <a target=\"_blank\" href=\"https://github.com/PanJiaChen/vue-element-admin/\">\n            <el-dropdown-item>Github</el-dropdown-item>\n          </a> -->\n          <!-- <a target=\"_blank\" href=\"https://panjiachen.github.io/vue-element-admin-site/#/\">\n            <el-dropdown-item>Docs</el-dropdown-item>\n          </a> -->\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span style=\"display:block;\">登出</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport Hamburger from '@/components/Hamburger'\nimport ErrorLog from '@/components/ErrorLog'\nimport Screenfull from '@/components/Screenfull'\nimport SizeSelect from '@/components/SizeSelect'\nimport Search from '@/components/HeaderSearch'\n\nexport default {\n  components: {\n    Breadcrumb,\n    Hamburger,\n    ErrorLog,\n    Screenfull,\n    SizeSelect,\n    Search\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar',\n      'device'\n    ])\n  },\n  methods: {\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      await this.$store.dispatch('user/logout')\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 50px;\n  overflow: hidden;\n  position: relative;\n  background: #fff;\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\n\n  .hamburger-container {\n    line-height: 46px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .errLog-container {\n    display: inline-block;\n    vertical-align: top;\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 50px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background .3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, .025)\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 30px;\n\n      .avatar-wrapper {\n        margin-top: 5px;\n        position: relative;\n\n        .user-avatar {\n          cursor: pointer;\n          width: 40px;\n          height: 40px;\n          border-radius: 10px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -20px;\n          top: 25px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}