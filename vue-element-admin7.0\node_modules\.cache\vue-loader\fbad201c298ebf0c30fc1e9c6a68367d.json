{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=style&index=0&id=09ac478a&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749173686857}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";AA4kBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"300\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<div class=\"header-actions\">\r\n<span v-if=\"selectedTables.length > 8\" class=\"scroll-tip\">可滚动查看更多</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nsize=\"small\"\r\ntype=\"info\"\r\nstyle=\"margin: 2px 4px 2px 0;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n</div>\r\n\r\n<!-- 操作按钮区域 - 移到容器外部 -->\r\n<div class=\"action-buttons-wrapper\">\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\nsize=\"medium\"\r\n>\r\n{{ processing ? '处理中...' : '🔍 异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n  :percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n  :stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<!-- 异常结果展示组件 -->\r\n<exception-results\r\n  :exception-data=\"exceptionList\"\r\n  @export-results=\"handleExportResults\"\r\n  @clear-results=\"handleClearResults\"\r\n/>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport ExceptionResults from './ExceptionResults.vue'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  components: {\r\n    ExceptionResults\r\n  },\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // Excel文件选择相关\r\n      availableTables: [], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      exceptionColumns: [], // 动态生成的表格列\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 真正调用后端API上传文件\r\n        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传\r\n        try {\r\n          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            timeout: 60000\r\n          })\r\n\r\n          // 检查上传结果\r\n          if (!response.data || !response.data.success) {\r\n            throw new Error(response.data?.message || '上传失败')\r\n          }\r\n        } catch (uploadError) {\r\n          // 如果上传接口不存在，使用模拟上传\r\n          if (uploadError.response && uploadError.response.status === 404) {\r\n            console.warn('上传接口不存在，使用模拟上传')\r\n            await new Promise(resolve => setTimeout(resolve, 2000))\r\n          } else {\r\n            throw uploadError\r\n          }\r\n        }\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 上传成功后，重新加载服务器上的Excel文件列表\r\n        await this.loadAvailableFiles()\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n        console.log('这些文件来自path_default文件夹:', response.data.paths)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('选中的表格数据:', this.selectedTables)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n        console.log('这些路径来自path_default文件夹:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          console.log('后端返回的原始数据结构:', response.data)\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            console.log(`异常类型 ${exceptionType} 的数据:`, exceptions)\r\n\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 直接使用后端返回的数据，添加异常类型\r\n                const exception = {\r\n                  异常类型: exceptionType, // 添加异常类型字段\r\n                  ...item // 展开后端返回的所有字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          // 根据detectTrade函数的返回结构，固定列配置\r\n          this.exceptionColumns = [\r\n            {\r\n              prop: '异常类型',\r\n              label: '异常类型',\r\n              width: 150,\r\n              align: 'center',\r\n              type: 'tag'\r\n            },\r\n            {\r\n              prop: '订单号',\r\n              label: '订单号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人姓名',\r\n              label: '支付人姓名',\r\n              width: 120,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人身份证号',\r\n              label: '支付人身份证号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '物流单号',\r\n              label: '物流单号',\r\n              width: 180,\r\n              align: 'center'\r\n            }\r\n          ]\r\n\r\n          this.exceptionList = exceptionList\r\n          console.log('处理后的异常数据列表:', this.exceptionList)\r\n          console.log('表格列配置:', this.exceptionColumns)\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success',\r\n        // 添加更多可能的异常类型\r\n        '重复订单': 'danger',\r\n        '异常物流': 'warning',\r\n        '身份证异常': 'danger',\r\n        '姓名异常': 'warning'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    },\r\n\r\n    // 根据列名获取列宽度\r\n    getColumnWidth(columnName) {\r\n      const widthMap = {\r\n        '订单号': 180,\r\n        '支付人姓名': 120,\r\n        '支付人身份证号': 180,\r\n        '物流单号': 180,\r\n        '异常类型': 150\r\n      }\r\n      return widthMap[columnName] || 120\r\n    },\r\n\r\n    // 根据列名获取对齐方式\r\n    getColumnAlign(columnName) {\r\n      const alignMap = {\r\n        '订单号': 'center',\r\n        '支付人姓名': 'center',\r\n        '支付人身份证号': 'center',\r\n        '物流单号': 'center',\r\n        '异常类型': 'center'\r\n      }\r\n      return alignMap[columnName] || 'left'\r\n    },\r\n\r\n    // 处理导出结果\r\n    handleExportResults(data) {\r\n      console.log('导出异常结果:', data)\r\n      // 这里可以实现具体的导出逻辑\r\n      this.$message.success('导出功能开发中...')\r\n    },\r\n\r\n    // 处理清空结果\r\n    handleClearResults() {\r\n      this.$confirm('确定要清空所有异常检测结果吗？', '确认清空', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.exceptionList = []\r\n        this.exceptionColumns = []\r\n        this.$message.success('已清空异常检测结果')\r\n      }).catch(() => {\r\n        this.$message.info('已取消清空操作')\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 20px; /* 从30px减少到20px */\r\n  padding: 15px; /* 从20px减少到15px */\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100px; /* 从180px减少到100px */\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 40px; /* 从67px减少到40px */\r\n  color: #c0c4cc;\r\n  margin: 15px 0 8px; /* 从40px 0 16px调整到15px 0 8px */\r\n  line-height: 30px; /* 从50px减少到30px */\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 13px; /* 从14px减少到13px */\r\n  text-align: center;\r\n  margin-top: -5px; /* 向上调整位置 */\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 15px; /* 从20px减少到15px */\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 15px 0; /* 从20px减少到15px */\r\n  padding: 12px; /* 从15px减少到12px */\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n  max-height: 120px; /* 从200px减少到120px */\r\n  overflow: hidden; /* 隐藏溢出内容 */\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.scroll-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  font-weight: normal;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px; /* 从8px减少到6px */\r\n  max-height: 80px; /* 从120px减少到80px */\r\n  overflow-y: auto; /* 添加垂直滚动条 */\r\n  padding-right: 8px; /* 为滚动条留出空间 */\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.selected-tables-list::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 操作按钮包装器 */\r\n.action-buttons-wrapper {\r\n  margin: 20px 0;\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #ffffff, #f8f9fa);\r\n  border: 2px solid #409eff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.action-buttons::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  background: linear-gradient(45deg, #409eff, #67c23a, #e6a23c, #f56c6c);\r\n  border-radius: 12px;\r\n  z-index: -1;\r\n  animation: borderGlow 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes borderGlow {\r\n  0%, 100% { opacity: 0.6; }\r\n  50% { opacity: 1; }\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 14px 24px;\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-buttons .el-button--success {\r\n  background: linear-gradient(135deg, #67c23a, #85ce61);\r\n  border: none;\r\n  box-shadow: 0 3px 10px rgba(103, 194, 58, 0.4);\r\n  font-size: 16px;\r\n  padding: 16px 32px;\r\n}\r\n\r\n.action-buttons .el-button--success:hover {\r\n  background: linear-gradient(135deg, #85ce61, #67c23a);\r\n  transform: translateY(-2px) scale(1.05);\r\n  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.5);\r\n}\r\n\r\n.action-buttons .el-button--primary {\r\n  background: linear-gradient(135deg, #409eff, #66b3ff);\r\n  border: none;\r\n  box-shadow: 0 3px 10px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n.action-buttons .el-button--primary:hover {\r\n  background: linear-gradient(135deg, #66b3ff, #409eff);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 15px;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);\r\n  border-radius: 10px;\r\n  border: 1px solid #b3d8ff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.progress-text {\r\n  margin: 15px 0 0 0;\r\n  font-size: 15px;\r\n  color: #409eff;\r\n  text-align: center;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n/* 异常结果组件容器 */\r\n.exception-results-wrapper {\r\n  margin-top: 30px;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n   .action-buttons {\r\n     flex-direction: column;\r\n   }\r\n   .action-buttons .el-button {\r\n     width: 100%;\r\n   }\r\n \r\n   .exception-header {\r\n     padding: 12px 15px;\r\n     margin: -15px -15px 15px -15px;\r\n   }\r\n \r\n   .header-title {\r\n     font-size: 16px;\r\n   }\r\n \r\n   .scroll-container {\r\n     height: 400px; \r\n   }\r\n \r\n   .table-summary {\r\n     padding: 12px 15px;\r\n   }\r\n\r\n   .summary-text {\r\n     font-size: 14px;\r\n   }\r\n\r\n   .exception-count {\r\n     font-size: 16px; \r\n   }\r\n  }\r\n</style>\r\n"]}]}