{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\page.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\page.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBTd2l0Y2hSb2xlcyBmcm9tICcuL2NvbXBvbmVudHMvU3dpdGNoUm9sZXMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BhZ2VQZXJtaXNzaW9uJywKICBjb21wb25lbnRzOiB7IFN3aXRjaFJvbGVzIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlUm9sZXNDaGFuZ2UoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9wZXJtaXNzaW9uL2luZGV4PycgKyArbmV3IERhdGUoKSB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["page.vue"], "names": [], "mappings": ";AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "page.vue", "sourceRoot": "src/views/permission", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <switch-roles @change=\"handleRolesChange\" />\n  </div>\n</template>\n\n<script>\nimport SwitchRoles from './components/SwitchRoles'\n\nexport default {\n  name: 'PagePermission',\n  components: { SwitchRoles },\n  methods: {\n    handleRolesChange() {\n      this.$router.push({ path: '/permission/index?' + +new Date() })\n    }\n  }\n}\n</script>\n"]}]}