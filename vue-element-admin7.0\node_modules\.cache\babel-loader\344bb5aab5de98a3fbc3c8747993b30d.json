{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\databaseManage.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\databaseManage.js", "mtime": 1747748935263}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIGZyb20gIkQ6LzIwMjVcdTU5MjdcdTUyMUJfXHU1NzMwXHU0RTBCXHU3NTMwXHU1RTg0L3Z1ZS1lbGVtZW50LWFkbWluNy4wL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgTGF5b3V0IGZyb20gJ0AvbGF5b3V0JzsKdmFyIGRhdGFiYXNlTWFuYWdlUm91dGVyID0gewogIHBhdGg6ICcvZGF0YWJhc2VNYW5hZ2UnLAogIGNvbXBvbmVudDogTGF5b3V0LAogIHJlZGlyZWN0OiAnbm9SZWRpcmVjdCcsCiAgbmFtZTogJ0RhdGFiYXNlTWFuYWdlJywKICBtZXRhOiB7CiAgICB0aXRsZTogJ+aVsOaNruW6k+aTjee6tScsCiAgICBpY29uOiAnZWRpdCcKICB9LAogIGNoaWxkcmVuOiBbewogICAgcGF0aDogJ2RhdGFiYXNlTWFuYWdlMScsCiAgICBjb21wb25lbnQ6IGZ1bmN0aW9uIGNvbXBvbmVudCgpIHsKICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCdAL3ZpZXdzL2RhdGFiYXNlTWFuYWdlL2RhdGFiYXNlTWFuYWdlMS52dWUnKSk7CiAgICAgIH0pOwogICAgfSwKICAgIG5hbWU6ICdEYXRhYmFzZU1hbmFnZScsCiAgICBtZXRhOiB7CiAgICAgIHRpdGxlOiAn5pWw5o2u5bqT5pON5L2c6aG16Z2iJwogICAgfQogIH1dCn07CmV4cG9ydCBkZWZhdWx0IGRhdGFiYXNlTWFuYWdlUm91dGVyOw=="}, {"version": 3, "names": ["Layout", "databaseManageRouter", "path", "component", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard", "require"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/router/modules/databaseManage.js"], "sourcesContent": ["import Layout from '@/layout'\r\n\r\nconst databaseManageRouter = {\r\n  path: '/databaseManage',\r\n  component: Layout,\r\n  redirect: 'noRedirect',\r\n  name: 'DatabaseManage',\r\n  meta: {\r\n    title: '数据库操纵',\r\n    icon: 'edit'\r\n  },\r\n  children: [\r\n    {\r\n      path: 'databaseManage1',\r\n      component: () => import('@/views/databaseManage/databaseManage1.vue'),\r\n      name: 'DatabaseManage',\r\n      meta: { title: '数据库操作页面' }\r\n    }\r\n  ]\r\n}\r\nexport default databaseManageRouter\n"], "mappings": ";;;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,IAAMC,oBAAoB,GAAG;EAC3BC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE;IACJC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,4CAA4C;MAAA;IAAA,CAAC;IACrET,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;EAC3B,CAAC;AAEL,CAAC;AACD,eAAeN,oBAAoB", "ignoreList": []}]}