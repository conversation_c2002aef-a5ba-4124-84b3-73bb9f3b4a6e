{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgTG9nbyBmcm9tICcuL0xvZ28nCmltcG9ydCBTaWRlYmFySXRlbSBmcm9tICcuL1NpZGViYXJJdGVtJwppbXBvcnQgdmFyaWFibGVzIGZyb20gJ0Avc3R5bGVzL3ZhcmlhYmxlcy5zY3NzJwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsgU2lkZWJhckl0ZW0sIExvZ28gfSwKICBjb21wdXRlZDogewogICAgLi4ubWFwR2V0dGVycyhbCiAgICAgICdwZXJtaXNzaW9uX3JvdXRlcycsCiAgICAgICdzaWRlYmFyJwogICAgXSksCiAgICBhY3RpdmVNZW51KCkgewogICAgICBjb25zdCByb3V0ZSA9IHRoaXMuJHJvdXRlCiAgICAgIGNvbnN0IHsgbWV0YSwgcGF0aCB9ID0gcm91dGUKICAgICAgLy8gaWYgc2V0IHBhdGgsIHRoZSBzaWRlYmFyIHdpbGwgaGlnaGxpZ2h0IHRoZSBwYXRoIHlvdSBzZXQKICAgICAgaWYgKG1ldGEuYWN0aXZlTWVudSkgewogICAgICAgIHJldHVybiBtZXRhLmFjdGl2ZU1lbnUKICAgICAgfQogICAgICByZXR1cm4gcGF0aAogICAgfSwKICAgIHNob3dMb2dvKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2lkZWJhckxvZ28KICAgIH0sCiAgICB2YXJpYWJsZXMoKSB7CiAgICAgIHJldHVybiB2YXJpYWJsZXMKICAgIH0sCiAgICBpc0NvbGxhcHNlKCkgewogICAgICByZXR1cm4gIXRoaXMuc2lkZWJhci5vcGVuZWQKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\n  <div :class=\"{'has-logo':showLogo}\">\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n    <el-scrollbar wrap-class=\"scrollbar-wrapper\">\n      <el-menu\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        :background-color=\"variables.menuBg\"\n        :text-color=\"variables.menuText\"\n        :unique-opened=\"false\"\n        :active-text-color=\"variables.menuActiveText\"\n        :collapse-transition=\"false\"\n        mode=\"vertical\"\n      >\n        <sidebar-item v-for=\"route in permission_routes\" :key=\"route.path\" :item=\"route\" :base-path=\"route.path\" />\n      </el-menu>\n    </el-scrollbar>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Logo from './Logo'\nimport SidebarItem from './SidebarItem'\nimport variables from '@/styles/variables.scss'\n\nexport default {\n  components: { SidebarItem, Logo },\n  computed: {\n    ...mapGetters([\n      'permission_routes',\n      'sidebar'\n    ]),\n    activeMenu() {\n      const route = this.$route\n      const { meta, path } = route\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        return meta.activeMenu\n      }\n      return path\n    },\n    showLogo() {\n      return this.$store.state.settings.sidebarLogo\n    },\n    variables() {\n      return variables\n    },\n    isCollapse() {\n      return !this.sidebar.opened\n    }\n  }\n}\n</script>\n"]}]}