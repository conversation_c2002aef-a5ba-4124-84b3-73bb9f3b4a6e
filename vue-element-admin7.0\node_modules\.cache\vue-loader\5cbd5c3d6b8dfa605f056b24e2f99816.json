{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\components\\PanelGroup.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\components\\PanelGroup.vue", "mtime": 1731856974000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBDb3VudFRvIGZyb20gJ3Z1ZS1jb3VudC10bycKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBDb3VudFRvCiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZXRMaW5lQ2hhcnREYXRhKHR5cGUpIHsKICAgICAgdGhpcy4kZW1pdCgnaGFuZGxlU2V0TGluZUNoYXJ0RGF0YScsIHR5cGUpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["PanelGroup.vue"], "names": [], "mappings": ";AA0DA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PanelGroup.vue", "sourceRoot": "src/views/dashboard/admin/components", "sourcesContent": ["<template>\n  <el-row :gutter=\"40\" class=\"panel-group\">\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('newVisitis')\">\n        <div class=\"card-panel-icon-wrapper icon-people\">\n          <svg-icon icon-class=\"peoples\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            用户数\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"102400\" :duration=\"2600\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('messages')\">\n        <div class=\"card-panel-icon-wrapper icon-message\">\n          <svg-icon icon-class=\"shopping\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            近七日活跃用户数\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"81212\" :duration=\"3000\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('purchases')\">\n        <div class=\"card-panel-icon-wrapper icon-money\">\n          <svg-icon icon-class=\"money\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            近七日交易流水量\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"9280\" :duration=\"3200\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('shoppings')\">\n        <div class=\"card-panel-icon-wrapper icon-shopping\">\n          <svg-icon icon-class=\"message\" class-name=\"card-panel-icon\" />\n        </div>\n        <div class=\"card-panel-description\">\n          <div class=\"card-panel-text\">\n            近七日交易记录数\n          </div>\n          <count-to :start-val=\"0\" :end-val=\"13600\" :duration=\"3600\" class=\"card-panel-num\" />\n        </div>\n      </div>\n    </el-col>\n  </el-row>\n</template>\n\n<script>\nimport CountTo from 'vue-count-to'\n\nexport default {\n  components: {\n    CountTo\n  },\n  methods: {\n    handleSetLineChartData(type) {\n      this.$emit('handleSetLineChartData', type)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.panel-group {\n  margin-top: 18px;\n\n  .card-panel-col {\n    margin-bottom: 32px;\n  }\n\n  .card-panel {\n    height: 108px;\n    cursor: pointer;\n    font-size: 12px;\n    position: relative;\n    overflow: hidden;\n    color: #666;\n    background: #fff;\n    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);\n    border-color: rgba(0, 0, 0, .05);\n\n    &:hover {\n      .card-panel-icon-wrapper {\n        color: #fff;\n      }\n\n      .icon-people {\n        background: #40c9c6;\n      }\n\n      .icon-message {\n        background: #36a3f7;\n      }\n\n      .icon-money {\n        background: #f4516c;\n      }\n\n      .icon-shopping {\n        background: #34bfa3\n      }\n    }\n\n    .icon-people {\n      color: #40c9c6;\n    }\n\n    .icon-message {\n      color: #36a3f7;\n    }\n\n    .icon-money {\n      color: #f4516c;\n    }\n\n    .icon-shopping {\n      color: #34bfa3\n    }\n\n    .card-panel-icon-wrapper {\n      float: left;\n      margin: 14px 0 0 14px;\n      padding: 16px;\n      transition: all 0.38s ease-out;\n      border-radius: 6px;\n    }\n\n    .card-panel-icon {\n      float: left;\n      font-size: 48px;\n    }\n\n    .card-panel-description {\n      float: right;\n      font-weight: bold;\n      margin: 26px;\n      margin-left: 0px;\n\n      .card-panel-text {\n        line-height: 18px;\n        color: rgba(0, 0, 0, 0.45);\n        font-size: 16px;\n        margin-bottom: 12px;\n      }\n\n      .card-panel-num {\n        font-size: 20px;\n      }\n    }\n  }\n}\n\n@media (max-width:550px) {\n  .card-panel-description {\n    display: none;\n  }\n\n  .card-panel-icon-wrapper {\n    float: none !important;\n    width: 100%;\n    height: 100%;\n    margin: 0 !important;\n\n    .svg-icon {\n      display: block;\n      margin: 14px auto !important;\n      float: none !important;\n    }\n  }\n}\n</style>\n"]}]}