{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ01lbnVJdGVtJywKICBmdW5jdGlvbmFsOiB0cnVlLAogIHByb3BzOiB7CiAgICBpY29uOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICB0aXRsZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoLCBjb250ZXh0KSB7CiAgICB2YXIgX2NvbnRleHQkcHJvcHMgPSBjb250ZXh0LnByb3BzLAogICAgICBpY29uID0gX2NvbnRleHQkcHJvcHMuaWNvbiwKICAgICAgdGl0bGUgPSBfY29udGV4dCRwcm9wcy50aXRsZTsKICAgIHZhciB2bm9kZXMgPSBbXTsKICAgIGlmIChpY29uKSB7CiAgICAgIGlmIChpY29uLmluY2x1ZGVzKCdlbC1pY29uJykpIHsKICAgICAgICB2bm9kZXMucHVzaChoKCJpIiwgewogICAgICAgICAgImNsYXNzIjogW2ljb24sICdzdWItZWwtaWNvbiddCiAgICAgICAgfSkpOwogICAgICB9IGVsc2UgewogICAgICAgIHZub2Rlcy5wdXNoKGgoInN2Zy1pY29uIiwgewogICAgICAgICAgImF0dHJzIjogewogICAgICAgICAgICAiaWNvbi1jbGFzcyI6IGljb24KICAgICAgICAgIH0KICAgICAgICB9KSk7CiAgICAgIH0KICAgIH0KICAgIGlmICh0aXRsZSkgewogICAgICB2bm9kZXMucHVzaChoKCJzcGFuIiwgewogICAgICAgICJzbG90IjogJ3RpdGxlJwogICAgICB9LCBbdGl0bGVdKSk7CiAgICB9CiAgICByZXR1cm4gdm5vZGVzOwogIH0KfTs="}, {"version": 3, "names": ["name", "functional", "props", "icon", "type", "String", "default", "title", "render", "h", "context", "_context$props", "vnodes", "includes", "push"], "sources": ["src/layout/components/Sidebar/Item.vue"], "sourcesContent": ["<script>\nexport default {\n  name: 'MenuItem',\n  functional: true,\n  props: {\n    icon: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  render(h, context) {\n    const { icon, title } = context.props\n    const vnodes = []\n\n    if (icon) {\n      if (icon.includes('el-icon')) {\n        vnodes.push(<i class={[icon, 'sub-el-icon']} />)\n      } else {\n        vnodes.push(<svg-icon icon-class={icon}/>)\n      }\n    }\n\n    if (title) {\n      vnodes.push(<span slot='title'>{(title)}</span>)\n    }\n    return vnodes\n  }\n}\n</script>\n\n<style scoped>\n.sub-el-icon {\n  color: currentColor;\n  width: 1em;\n  height: 1em;\n}\n</style>\n"], "mappings": ";;AACA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,MAAA,WAAAA,OAAAC,CAAA,EAAAC,OAAA;IACA,IAAAC,cAAA,GAAAD,OAAA,CAAAR,KAAA;MAAAC,IAAA,GAAAQ,cAAA,CAAAR,IAAA;MAAAI,KAAA,GAAAI,cAAA,CAAAJ,KAAA;IACA,IAAAK,MAAA;IAEA,IAAAT,IAAA;MACA,IAAAA,IAAA,CAAAU,QAAA;QACAD,MAAA,CAAAE,IAAA,CAAAL,CAAA;UAAA,UAAAN,IAAA;QAAA;MACA;QACAS,MAAA,CAAAE,IAAA,CAAAL,CAAA;UAAA;YAAA,cAAAN;UAAA;QAAA;MACA;IACA;IAEA,IAAAI,KAAA;MACAK,MAAA,CAAAE,IAAA,CAAAL,CAAA;QAAA;MAAA,IAAAF,KAAA;IACA;IACA,OAAAK,MAAA;EACA;AACA", "ignoreList": []}]}