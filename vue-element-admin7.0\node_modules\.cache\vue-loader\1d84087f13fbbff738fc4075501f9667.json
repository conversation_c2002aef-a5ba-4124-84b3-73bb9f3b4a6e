{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue", "mtime": 1747749632954}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIGltcG9ydCBUaGVtZVBpY2tlciBmcm9tICdAL2NvbXBvbmVudHMvVGhlbWVQaWNrZXInCgpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7fQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGZpeGVkSGVhZGVyOiB7CiAgICAgIGdldCgpIHsKICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MuZml4ZWRIZWFkZXIKICAgICAgfSwKICAgICAgc2V0KHZhbCkgewogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICAgICAga2V5OiAnZml4ZWRIZWFkZXInLAogICAgICAgICAgdmFsdWU6IHZhbAogICAgICAgIH0pCiAgICAgIH0KICAgIH0sCiAgICB0YWdzVmlldzogewogICAgICBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRhZ3NWaWV3CiAgICAgIH0sCiAgICAgIHNldCh2YWwpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICAgIGtleTogJ3RhZ3NWaWV3JywKICAgICAgICAgIHZhbHVlOiB2YWwKICAgICAgICB9KQogICAgICB9CiAgICB9LAogICAgc2lkZWJhckxvZ286IHsKICAgICAgZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5zaWRlYmFyTG9nbwogICAgICB9LAogICAgICBzZXQodmFsKSB7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAgICBrZXk6ICdzaWRlYmFyTG9nbycsCiAgICAgICAgICB2YWx1ZTogdmFsCiAgICAgICAgfSkKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgdGhlbWVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICAgIGtleTogJ3RoZW1lJywKICAgICAgICB2YWx1ZTogdmFsCiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA8BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Settings", "sourcesContent": ["<template>\n  <div class=\"drawer-container\">\n    <div>\n      <h3 class=\"drawer-title\">页面样式设置</h3>\n\n      <!-- <div class=\"drawer-item\">\n        <span>Te Cohemlor</span>\n        <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n      </div> -->\n\n      <div class=\"drawer-item\">\n        <span>开启标签页面可见</span>\n        <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>固定标签头</span>\n        <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>侧边栏的标志可见</span>\n        <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n      </div>\n\n    </div>\n  </div>\n</template>\n\n<script>\n// import ThemePicker from '@/components/ThemePicker'\n\nexport default {\n  components: {},\n  data() {\n    return {}\n  },\n  computed: {\n    fixedHeader: {\n      get() {\n        return this.$store.state.settings.fixedHeader\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        })\n      }\n    },\n    tagsView: {\n      get() {\n        return this.$store.state.settings.tagsView\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        })\n      }\n    },\n    sidebarLogo: {\n      get() {\n        return this.$store.state.settings.sidebarLogo\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        })\n      }\n    }\n  },\n  methods: {\n    themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.drawer-container {\n  padding: 24px;\n  font-size: 14px;\n  line-height: 1.5;\n  word-wrap: break-word;\n\n  .drawer-title {\n    margin-bottom: 12px;\n    color: rgba(0, 0, 0, .85);\n    font-size: 14px;\n    line-height: 22px;\n  }\n\n  .drawer-item {\n    color: rgba(0, 0, 0, .65);\n    font-size: 14px;\n    padding: 12px 0;\n  }\n\n  .drawer-switch {\n    float: right\n  }\n}\n</style>\n"]}]}