{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\log.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\log.js", "mtime": 1747748935263}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIGZyb20gIkQ6LzIwMjVcdTU5MjdcdTUyMUJfXHU1NzMwXHU0RTBCXHU3NTMwXHU1RTg0L3Z1ZS1lbGVtZW50LWFkbWluNy4wL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgTGF5b3V0IGZyb20gJ0AvbGF5b3V0JzsKdmFyIGxvZ01hbmFnZVJvdXRlciA9IHsKICBwYXRoOiAnL2xvZ01hbmFnZScsCiAgY29tcG9uZW50OiBMYXlvdXQsCiAgcmVkaXJlY3Q6ICdub1JlZGlyZWN0JywKICBuYW1lOiAnTG9nTWFuYWdlJywKICBtZXRhOiB7CiAgICB0aXRsZTogJ+aXpeW/lycsCiAgICBpY29uOiAnZWR1Y2F0aW9uJwogIH0sCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAnbG9nTWFuYWdlJywKICAgIGNvbXBvbmVudDogZnVuY3Rpb24gY29tcG9uZW50KCkgewogICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKHJlcXVpcmUoJ0Avdmlld3MvbG9nTWFuYWdlci9sb2dNYW5hZ2VyMS52dWUnKSk7CiAgICAgIH0pOwogICAgfSwKICAgIG5hbWU6ICdMb2dNYW5hZ2UnLAogICAgbWV0YTogewogICAgICB0aXRsZTogJ+afpeeci+aXpeW/lycKICAgIH0KICB9XQp9OwpleHBvcnQgZGVmYXVsdCBsb2dNYW5hZ2VSb3V0ZXI7"}, {"version": 3, "names": ["Layout", "logManageRouter", "path", "component", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard", "require"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/router/modules/log.js"], "sourcesContent": ["import Layout from '@/layout'\r\n\r\nconst logManageRouter = {\r\n  path: '/logManage',\r\n  component: Layout,\r\n  redirect: 'noRedirect',\r\n  name: 'LogManage',\r\n  meta: {\r\n    title: '日志',\r\n    icon: 'education'\r\n  },\r\n  children: [\r\n    {\r\n      path: 'logManage',\r\n      component: () => import('@/views/logManager/logManager1.vue'),\r\n      name: 'LogManage',\r\n      meta: { title: '查看日志' }\r\n    }\r\n  ]\r\n}\r\nexport default logManageRouter\n"], "mappings": ";;;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,IAAMC,eAAe,GAAG;EACtBC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;IACJC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oCAAoC;MAAA;IAAA,CAAC;IAC7DT,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC;AACD,eAAeN,eAAe", "ignoreList": []}]}