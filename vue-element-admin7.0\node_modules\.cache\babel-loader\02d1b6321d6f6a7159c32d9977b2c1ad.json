{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\logManager\\logManager1.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\logManager\\logManager1.vue", "mtime": 1747749486341}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "waves", "Pagination", "name", "components", "directives", "data", "list", "rawData", "table<PERSON><PERSON>", "total", "listLoading", "time", "username", "fileName", "databaseName", "list<PERSON>uery", "page", "pagelimit", "created", "getList", "methods", "_this", "post", "then", "response", "items", "map", "item", "catch", "error", "console", "finally", "setTimeout", "handleFilter", "log"], "sources": ["src/views/logManager/logManager1.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <div class=\"filter-container\">\r\n        <!-- <el-input\r\n          v-model=\"time\"\r\n          placeholder=\"时间\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        /> -->\r\n        <el-date-picker\r\n            v-model=\"time\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期时间\"\r\n            end-placeholder=\"结束日期时间\"\r\n            format=\"yyyy-MM-dd HH:mm:ss\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 350px; margin-top: 7px;\"\r\n        />\r\n\r\n        <el-input\r\n          v-model=\"username\"\r\n          placeholder=\"用户名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n\r\n        <el-input\r\n          v-model=\"fileName\"\r\n          placeholder=\"文件名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n        <el-input\r\n          v-model=\"databaseName\"\r\n          placeholder=\"数据库名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n        <el-button\r\n          class=\"filter-item search_submit\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          @click=\"handleFilter\"\r\n        >\r\n          搜索\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :key=\"tableKey\"\r\n        v-loading=\"listLoading\"\r\n        :data=\"list\"\r\n        border\r\n        fit\r\n        highlight-current-row\r\n        style=\"width: 100%\"\r\n        @sort-change=\"sortChange\"\r\n      >\r\n        <el-table-column\r\n          label=\"时间\"\r\n          prop=\"time\"\r\n          width=\"150px\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.time }}</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"用户名\" prop=\"username\" min-width=\"150px\">\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.username }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文件名\" prop=\"fileName\" min-width=\"150px\">\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.fileName }}</span>\r\n            <!-- <span class=\"link-cardNumber\" @click=\"handleUpdate(row)\">{{ row.title }}</span>\r\n            <el-tag>{{ row.type | typeFilter }}</el-tag> -->\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"数据库名\"\r\n          prop=\"databaseName\"\r\n          width=\"160px\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.databaseName }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"listQuery.page\"\r\n        :limit.sync=\"listQuery.pagelimit\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n  </template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport waves from '@/directive/waves' // waves directive\r\n// import { parseTime } from '@/utils'\r\nimport Pagination from '@/components/Pagination' // secondary package based on el-pagination\r\n\r\nexport default {\r\n  name: 'LogManager',\r\n  components: { Pagination },\r\n  directives: { waves },\r\n  data() {\r\n    return {\r\n      list: null,\r\n      rawData: null,\r\n      tableKey: 0,\r\n      total: 0,\r\n      listLoading: true,\r\n      time: [],\r\n      username: '',\r\n      fileName: '',\r\n      databaseName: '',\r\n      listQuery: {\r\n        page: 1,\r\n        pagelimit: 10,\r\n        time: null,\r\n        username: null,\r\n        fileName: null,\r\n        databaseName: null\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.listLoading = true\r\n      axios\r\n        .post('http://127.0.0.1:8000/all_log_history', this.listQuery) // 使用 POST 请求\r\n        .then((response) => {\r\n          this.rawData = response.data.items\r\n          this.total = response.data.total\r\n          this.list = this.rawData.map((item) => {\r\n            return {\r\n              time: item[0] === 'None' ? '空' : item[0],\r\n              username: item[1] === 'None' ? '空' : item[1],\r\n              fileName: item[2] === 'None' ? '空' : item[2],\r\n              databaseName: item[3] === 'None' ? '空' : item[3]\r\n            }\r\n          })\r\n        })\r\n        .catch((error) => {\r\n          console.error('Error fetching the list:', error)\r\n        })\r\n        .finally(() => {\r\n          // 模拟请求时间\r\n          setTimeout(() => {\r\n            this.listLoading = false\r\n          }, 1500)\r\n        })\r\n    },\r\n    handleFilter() {\r\n      console.log('handleFilter called')\r\n      this.listQuery.page = 1\r\n      this.listQuery.time = this.time || null\r\n      this.listQuery.username = this.username || null\r\n      this.listQuery.fileName = this.fileName || null\r\n      this.listQuery.databaseName = this.databaseName\r\n      console.log(this.listQuery)\r\n      this.getList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style>\r\n  .filter-item {\r\n    margin: 0 15px;\r\n  }\r\n  .search_submit {\r\n    position: relative; /* 相对定位，以便伪元素定位 */\r\n    overflow: hidden; /* 隐藏溢出部分 */\r\n    border: none; /* 去掉按钮边框 */\r\n    background-color: #007bffd5; /* 按钮背景颜色 */\r\n    color: white; /* 字体颜色 */\r\n    padding: 10px 20px; /* 按钮内边距 */\r\n    border-radius: 4px; /* 圆角 */\r\n    cursor: pointer; /* 鼠标指针 */\r\n    transition: background-color 0.3s; /* 背景颜色过渡 */\r\n  }\r\n  </style>\r\n"], "mappings": ";;;;AA6GA,OAAAA,KAAA;AACA,OAAAC,KAAA;AACA;AACA,OAAAC,UAAA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAJ,KAAA,EAAAA;EAAA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,WAAA;MACAC,IAAA;MACAC,QAAA;MACAC,QAAA;MACAC,YAAA;MACAC,SAAA;QACAC,IAAA;QACAC,SAAA;QACAN,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,YAAA;MACA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAX,WAAA;MACAX,KAAA,CACAuB,IAAA,+CAAAP,SAAA;MAAA,CACAQ,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAd,OAAA,GAAAiB,QAAA,CAAAnB,IAAA,CAAAoB,KAAA;QACAJ,KAAA,CAAAZ,KAAA,GAAAe,QAAA,CAAAnB,IAAA,CAAAI,KAAA;QACAY,KAAA,CAAAf,IAAA,GAAAe,KAAA,CAAAd,OAAA,CAAAmB,GAAA,WAAAC,IAAA;UACA;YACAhB,IAAA,EAAAgB,IAAA,uBAAAA,IAAA;YACAf,QAAA,EAAAe,IAAA,uBAAAA,IAAA;YACAd,QAAA,EAAAc,IAAA,uBAAAA,IAAA;YACAb,YAAA,EAAAa,IAAA,uBAAAA,IAAA;UACA;QACA;MACA,GACAC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,6BAAAA,KAAA;MACA,GACAE,OAAA;QACA;QACAC,UAAA;UACAX,KAAA,CAAAX,WAAA;QACA;MACA;IACA;IACAuB,YAAA,WAAAA,aAAA;MACAH,OAAA,CAAAI,GAAA;MACA,KAAAnB,SAAA,CAAAC,IAAA;MACA,KAAAD,SAAA,CAAAJ,IAAA,QAAAA,IAAA;MACA,KAAAI,SAAA,CAAAH,QAAA,QAAAA,QAAA;MACA,KAAAG,SAAA,CAAAF,QAAA,QAAAA,QAAA;MACA,KAAAE,SAAA,CAAAD,YAAA,QAAAA,YAAA;MACAgB,OAAA,CAAAI,GAAA,MAAAnB,SAAA;MACA,KAAAI,OAAA;IACA;EACA;AACA", "ignoreList": []}]}