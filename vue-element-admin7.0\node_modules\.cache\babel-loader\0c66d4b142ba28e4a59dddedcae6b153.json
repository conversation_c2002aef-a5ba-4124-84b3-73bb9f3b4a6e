{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\role.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\role.vue", "mtime": 1732096978000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["path", "deepClone", "getRoutes", "getRoles", "addRole", "deleteRole", "updateRole", "defaultRole", "key", "name", "description", "routes", "data", "role", "Object", "assign", "rolesList", "dialogVisible", "dialogType", "checkStrictly", "defaultProps", "children", "label", "computed", "routesData", "created", "methods", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "res", "w", "_context", "n", "v", "serviceRoutes", "generateRoutes", "a", "_this2", "_callee2", "_context2", "basePath", "arguments", "length", "undefined", "_iterator", "_createForOfIteratorHelper", "_step", "s", "done", "route", "value", "hidden", "onlyOneShowingChild", "alwaysShow", "resolve", "title", "meta", "push", "err", "e", "f", "generateArr", "_this3", "for<PERSON>ach", "temp", "concat", "_toConsumableArray", "handleAddRole", "$refs", "tree", "setCheckedNodes", "handleEdit", "scope", "_this4", "row", "$nextTick", "handleDelete", "_ref", "_this5", "$index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_callee3", "_context3", "splice", "$message", "message", "catch", "error", "console", "log", "generateTree", "checked<PERSON>eys", "_iterator2", "_step2", "routePath", "includes", "confirmRole", "_this6", "_callee4", "isEdit", "index", "_yield$addRole", "_this6$role", "_context4", "getChe<PERSON><PERSON>eys", "$notify", "dangerouslyUseHTMLString", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "item", "_objectSpread", "noShowingChildren"], "sources": ["src/views/permission/role.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-button type=\"primary\" @click=\"handleAddRole\">New Role</el-button>\n\n    <el-table :data=\"rolesList\" style=\"width: 100%;margin-top:30px;\" border>\n      <el-table-column align=\"center\" label=\"Role Key\" width=\"220\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.key }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"center\" label=\"Role Name\" width=\"220\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.name }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"header-center\" label=\"Description\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.description }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"center\" label=\"Operations\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"primary\" size=\"small\" @click=\"handleEdit(scope)\">Edit</el-button>\n          <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope)\">Delete</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <el-dialog :visible.sync=\"dialogVisible\" :title=\"dialogType==='edit'?'Edit Role':'New Role'\">\n      <el-form :model=\"role\" label-width=\"80px\" label-position=\"left\">\n        <el-form-item label=\"Name\">\n          <el-input v-model=\"role.name\" placeholder=\"Role Name\" />\n        </el-form-item>\n        <el-form-item label=\"Desc\">\n          <el-input\n            v-model=\"role.description\"\n            :autosize=\"{ minRows: 2, maxRows: 4}\"\n            type=\"textarea\"\n            placeholder=\"Role Description\"\n          />\n        </el-form-item>\n        <el-form-item label=\"Menus\">\n          <el-tree\n            ref=\"tree\"\n            :check-strictly=\"checkStrictly\"\n            :data=\"routesData\"\n            :props=\"defaultProps\"\n            show-checkbox\n            node-key=\"path\"\n            class=\"permission-tree\"\n          />\n        </el-form-item>\n      </el-form>\n      <div style=\"text-align:right;\">\n        <el-button type=\"danger\" @click=\"dialogVisible=false\">Cancel</el-button>\n        <el-button type=\"primary\" @click=\"confirmRole\">Confirm</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport path from 'path'\nimport { deepClone } from '@/utils'\nimport { getRoutes, getRoles, addRole, deleteRole, updateRole } from '@/api/role'\n\nconst defaultRole = {\n  key: '',\n  name: '',\n  description: '',\n  routes: []\n}\n\nexport default {\n  data() {\n    return {\n      role: Object.assign({}, defaultRole),\n      routes: [],\n      rolesList: [],\n      dialogVisible: false,\n      dialogType: 'new',\n      checkStrictly: false,\n      defaultProps: {\n        children: 'children',\n        label: 'title'\n      }\n    }\n  },\n  computed: {\n    routesData() {\n      return this.routes\n    }\n  },\n  created() {\n    // Mock: get all routes and roles list from server\n    this.getRoutes()\n    this.getRoles()\n  },\n  methods: {\n    async getRoutes() {\n      const res = await getRoutes()\n      this.serviceRoutes = res.data\n      this.routes = this.generateRoutes(res.data)\n    },\n    async getRoles() {\n      const res = await getRoles()\n      this.rolesList = res.data\n    },\n\n    // Reshape the routes structure so that it looks the same as the sidebar\n    generateRoutes(routes, basePath = '/') {\n      const res = []\n\n      for (let route of routes) {\n        // skip some route\n        if (route.hidden) { continue }\n\n        const onlyOneShowingChild = this.onlyOneShowingChild(route.children, route)\n\n        if (route.children && onlyOneShowingChild && !route.alwaysShow) {\n          route = onlyOneShowingChild\n        }\n\n        const data = {\n          path: path.resolve(basePath, route.path),\n          title: route.meta && route.meta.title\n\n        }\n\n        // recursive child routes\n        if (route.children) {\n          data.children = this.generateRoutes(route.children, data.path)\n        }\n        res.push(data)\n      }\n      return res\n    },\n    generateArr(routes) {\n      let data = []\n      routes.forEach(route => {\n        data.push(route)\n        if (route.children) {\n          const temp = this.generateArr(route.children)\n          if (temp.length > 0) {\n            data = [...data, ...temp]\n          }\n        }\n      })\n      return data\n    },\n    handleAddRole() {\n      this.role = Object.assign({}, defaultRole)\n      if (this.$refs.tree) {\n        this.$refs.tree.setCheckedNodes([])\n      }\n      this.dialogType = 'new'\n      this.dialogVisible = true\n    },\n    handleEdit(scope) {\n      this.dialogType = 'edit'\n      this.dialogVisible = true\n      this.checkStrictly = true\n      this.role = deepClone(scope.row)\n      this.$nextTick(() => {\n        const routes = this.generateRoutes(this.role.routes)\n        this.$refs.tree.setCheckedNodes(this.generateArr(routes))\n        // set checked state of a node not affects its father and child nodes\n        this.checkStrictly = false\n      })\n    },\n    handleDelete({ $index, row }) {\n      this.$confirm('Confirm to remove the role?', 'Warning', {\n        confirmButtonText: 'Confirm',\n        cancelButtonText: 'Cancel',\n        type: 'warning'\n      })\n        .then(async() => {\n          await deleteRole(row.key)\n          this.rolesList.splice($index, 1)\n          this.$message({\n            type: 'success',\n            message: 'Delete succed!'\n          })\n        })\n        .catch(error => { console.log(error) })\n    },\n    generateTree(routes, basePath = '/', checkedKeys) {\n      const res = []\n\n      for (const route of routes) {\n        const routePath = path.resolve(basePath, route.path)\n\n        // recursive child routes\n        if (route.children) {\n          route.children = this.generateTree(route.children, routePath, checkedKeys)\n        }\n\n        if (checkedKeys.includes(routePath) || (route.children && route.children.length >= 1)) {\n          res.push(route)\n        }\n      }\n      return res\n    },\n    async confirmRole() {\n      const isEdit = this.dialogType === 'edit'\n\n      const checkedKeys = this.$refs.tree.getCheckedKeys()\n      this.role.routes = this.generateTree(deepClone(this.serviceRoutes), '/', checkedKeys)\n\n      if (isEdit) {\n        await updateRole(this.role.key, this.role)\n        for (let index = 0; index < this.rolesList.length; index++) {\n          if (this.rolesList[index].key === this.role.key) {\n            this.rolesList.splice(index, 1, Object.assign({}, this.role))\n            break\n          }\n        }\n      } else {\n        const { data } = await addRole(this.role)\n        this.role.key = data.key\n        this.rolesList.push(this.role)\n      }\n\n      const { description, key, name } = this.role\n      this.dialogVisible = false\n      this.$notify({\n        title: 'Success',\n        dangerouslyUseHTMLString: true,\n        message: `\n            <div>Role Key: ${key}</div>\n            <div>Role Name: ${name}</div>\n            <div>Description: ${description}</div>\n          `,\n        type: 'success'\n      })\n    },\n    // reference: src/view/layout/components/Sidebar/SidebarItem.vue\n    onlyOneShowingChild(children = [], parent) {\n      let onlyOneChild = null\n      const showingChildren = children.filter(item => !item.hidden)\n\n      // When there is only one child route, the child route is displayed by default\n      if (showingChildren.length === 1) {\n        onlyOneChild = showingChildren[0]\n        onlyOneChild.path = path.resolve(parent.path, onlyOneChild.path)\n        return onlyOneChild\n      }\n\n      // Show parent if there are no child route to display\n      if (showingChildren.length === 0) {\n        onlyOneChild = { ... parent, path: '', noShowingChildren: true }\n        return onlyOneChild\n      }\n\n      return false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .roles-table {\n    margin-top: 30px;\n  }\n  .permission-tree {\n    margin-bottom: 30px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA8DA,OAAAA,IAAA;AACA,SAAAC,SAAA;AACA,SAAAC,SAAA,IAAAA,UAAA,EAAAC,QAAA,IAAAA,SAAA,EAAAC,OAAA,EAAAC,UAAA,EAAAC,UAAA;AAEA,IAAAC,WAAA;EACAC,GAAA;EACAC,IAAA;EACAC,WAAA;EACAC,MAAA;AACA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,EAAAC,MAAA,CAAAC,MAAA,KAAAR,WAAA;MACAI,MAAA;MACAK,SAAA;MACAC,aAAA;MACAC,UAAA;MACAC,aAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAb,MAAA;IACA;EACA;EACAc,OAAA,WAAAA,QAAA;IACA;IACA,KAAAvB,SAAA;IACA,KAAAC,QAAA;EACA;EACAuB,OAAA;IACAxB,SAAA,WAAAA,UAAA;MAAA,IAAAyB,KAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,YAAA,GAAAI,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACAjC,UAAA;YAAA;cAAA8B,GAAA,GAAAE,QAAA,CAAAE,CAAA;cACAT,KAAA,CAAAU,aAAA,GAAAL,GAAA,CAAApB,IAAA;cACAe,KAAA,CAAAhB,MAAA,GAAAgB,KAAA,CAAAW,cAAA,CAAAN,GAAA,CAAApB,IAAA;YAAA;cAAA,OAAAsB,QAAA,CAAAK,CAAA;UAAA;QAAA,GAAAR,OAAA;MAAA;IACA;IACA5B,QAAA,WAAAA,SAAA;MAAA,IAAAqC,MAAA;MAAA,OAAAZ,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAW,SAAA;QAAA,IAAAT,GAAA;QAAA,OAAAH,YAAA,GAAAI,CAAA,WAAAS,SAAA;UAAA,kBAAAA,SAAA,CAAAP,CAAA;YAAA;cAAAO,SAAA,CAAAP,CAAA;cAAA,OACAhC,SAAA;YAAA;cAAA6B,GAAA,GAAAU,SAAA,CAAAN,CAAA;cACAI,MAAA,CAAAxB,SAAA,GAAAgB,GAAA,CAAApB,IAAA;YAAA;cAAA,OAAA8B,SAAA,CAAAH,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEA;IACAH,cAAA,WAAAA,eAAA3B,MAAA;MAAA,IAAAgC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAZ,GAAA;MAAA,IAAAe,SAAA,GAAAC,0BAAA,CAEArC,MAAA;QAAAsC,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAZ,CAAA,IAAAgB,IAAA;UAAA,IAAAC,KAAA,GAAAH,KAAA,CAAAI,KAAA;UACA;UACA,IAAAD,KAAA,CAAAE,MAAA;YAAA;UAAA;UAEA,IAAAC,mBAAA,QAAAA,mBAAA,CAAAH,KAAA,CAAA/B,QAAA,EAAA+B,KAAA;UAEA,IAAAA,KAAA,CAAA/B,QAAA,IAAAkC,mBAAA,KAAAH,KAAA,CAAAI,UAAA;YACAJ,KAAA,GAAAG,mBAAA;UACA;UAEA,IAAA3C,IAAA;YACAZ,IAAA,EAAAA,IAAA,CAAAyD,OAAA,CAAAd,QAAA,EAAAS,KAAA,CAAApD,IAAA;YACA0D,KAAA,EAAAN,KAAA,CAAAO,IAAA,IAAAP,KAAA,CAAAO,IAAA,CAAAD;UAEA;;UAEA;UACA,IAAAN,KAAA,CAAA/B,QAAA;YACAT,IAAA,CAAAS,QAAA,QAAAiB,cAAA,CAAAc,KAAA,CAAA/B,QAAA,EAAAT,IAAA,CAAAZ,IAAA;UACA;UACAgC,GAAA,CAAA4B,IAAA,CAAAhD,IAAA;QACA;MAAA,SAAAiD,GAAA;QAAAd,SAAA,CAAAe,CAAA,CAAAD,GAAA;MAAA;QAAAd,SAAA,CAAAgB,CAAA;MAAA;MACA,OAAA/B,GAAA;IACA;IACAgC,WAAA,WAAAA,YAAArD,MAAA;MAAA,IAAAsD,MAAA;MACA,IAAArD,IAAA;MACAD,MAAA,CAAAuD,OAAA,WAAAd,KAAA;QACAxC,IAAA,CAAAgD,IAAA,CAAAR,KAAA;QACA,IAAAA,KAAA,CAAA/B,QAAA;UACA,IAAA8C,IAAA,GAAAF,MAAA,CAAAD,WAAA,CAAAZ,KAAA,CAAA/B,QAAA;UACA,IAAA8C,IAAA,CAAAtB,MAAA;YACAjC,IAAA,MAAAwD,MAAA,CAAAC,kBAAA,CAAAzD,IAAA,GAAAyD,kBAAA,CAAAF,IAAA;UACA;QACA;MACA;MACA,OAAAvD,IAAA;IACA;IACA0D,aAAA,WAAAA,cAAA;MACA,KAAAzD,IAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAR,WAAA;MACA,SAAAgE,KAAA,CAAAC,IAAA;QACA,KAAAD,KAAA,CAAAC,IAAA,CAAAC,eAAA;MACA;MACA,KAAAvD,UAAA;MACA,KAAAD,aAAA;IACA;IACAyD,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,KAAA1D,UAAA;MACA,KAAAD,aAAA;MACA,KAAAE,aAAA;MACA,KAAAN,IAAA,GAAAZ,SAAA,CAAA0E,KAAA,CAAAE,GAAA;MACA,KAAAC,SAAA;QACA,IAAAnE,MAAA,GAAAiE,MAAA,CAAAtC,cAAA,CAAAsC,MAAA,CAAA/D,IAAA,CAAAF,MAAA;QACAiE,MAAA,CAAAL,KAAA,CAAAC,IAAA,CAAAC,eAAA,CAAAG,MAAA,CAAAZ,WAAA,CAAArD,MAAA;QACA;QACAiE,MAAA,CAAAzD,aAAA;MACA;IACA;IACA4D,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,MAAA,GAAAF,IAAA,CAAAE,MAAA;QAAAL,GAAA,GAAAG,IAAA,CAAAH,GAAA;MACA,KAAAM,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAC,IAAA,cAAA3D,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA0D,SAAA;QAAA,OAAA3D,YAAA,GAAAI,CAAA,WAAAwD,SAAA;UAAA,kBAAAA,SAAA,CAAAtD,CAAA;YAAA;cAAAsD,SAAA,CAAAtD,CAAA;cAAA,OACA9B,UAAA,CAAAwE,GAAA,CAAArE,GAAA;YAAA;cACAyE,MAAA,CAAAjE,SAAA,CAAA0E,MAAA,CAAAR,MAAA;cACAD,MAAA,CAAAU,QAAA;gBACAL,IAAA;gBACAM,OAAA;cACA;YAAA;cAAA,OAAAH,SAAA,CAAAlD,CAAA;UAAA;QAAA,GAAAiD,QAAA;MAAA,CACA,IACAK,KAAA,WAAAC,KAAA;QAAAC,OAAA,CAAAC,GAAA,CAAAF,KAAA;MAAA;IACA;IACAG,YAAA,WAAAA,aAAAtF,MAAA;MAAA,IAAAgC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAsD,WAAA,GAAAtD,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACA,IAAAd,GAAA;MAAA,IAAAmE,UAAA,GAAAnD,0BAAA,CAEArC,MAAA;QAAAyF,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAjD,CAAA,MAAAkD,MAAA,GAAAD,UAAA,CAAAhE,CAAA,IAAAgB,IAAA;UAAA,IAAAC,KAAA,GAAAgD,MAAA,CAAA/C,KAAA;UACA,IAAAgD,SAAA,GAAArG,IAAA,CAAAyD,OAAA,CAAAd,QAAA,EAAAS,KAAA,CAAApD,IAAA;;UAEA;UACA,IAAAoD,KAAA,CAAA/B,QAAA;YACA+B,KAAA,CAAA/B,QAAA,QAAA4E,YAAA,CAAA7C,KAAA,CAAA/B,QAAA,EAAAgF,SAAA,EAAAH,WAAA;UACA;UAEA,IAAAA,WAAA,CAAAI,QAAA,CAAAD,SAAA,KAAAjD,KAAA,CAAA/B,QAAA,IAAA+B,KAAA,CAAA/B,QAAA,CAAAwB,MAAA;YACAb,GAAA,CAAA4B,IAAA,CAAAR,KAAA;UACA;QACA;MAAA,SAAAS,GAAA;QAAAsC,UAAA,CAAArC,CAAA,CAAAD,GAAA;MAAA;QAAAsC,UAAA,CAAApC,CAAA;MAAA;MACA,OAAA/B,GAAA;IACA;IACAuE,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAA5E,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA2E,SAAA;QAAA,IAAAC,MAAA,EAAAR,WAAA,EAAAS,KAAA,EAAAC,cAAA,EAAAhG,IAAA,EAAAiG,WAAA,EAAAnG,WAAA,EAAAF,GAAA,EAAAC,IAAA;QAAA,OAAAoB,YAAA,GAAAI,CAAA,WAAA6E,SAAA;UAAA,kBAAAA,SAAA,CAAA3E,CAAA;YAAA;cACAuE,MAAA,GAAAF,MAAA,CAAAtF,UAAA;cAEAgF,WAAA,GAAAM,MAAA,CAAAjC,KAAA,CAAAC,IAAA,CAAAuC,cAAA;cACAP,MAAA,CAAA3F,IAAA,CAAAF,MAAA,GAAA6F,MAAA,CAAAP,YAAA,CAAAhG,SAAA,CAAAuG,MAAA,CAAAnE,aAAA,QAAA6D,WAAA;cAAA,KAEAQ,MAAA;gBAAAI,SAAA,CAAA3E,CAAA;gBAAA;cAAA;cAAA2E,SAAA,CAAA3E,CAAA;cAAA,OACA7B,UAAA,CAAAkG,MAAA,CAAA3F,IAAA,CAAAL,GAAA,EAAAgG,MAAA,CAAA3F,IAAA;YAAA;cACA8F,KAAA;YAAA;cAAA,MAAAA,KAAA,GAAAH,MAAA,CAAAxF,SAAA,CAAA6B,MAAA;gBAAAiE,SAAA,CAAA3E,CAAA;gBAAA;cAAA;cAAA,MACAqE,MAAA,CAAAxF,SAAA,CAAA2F,KAAA,EAAAnG,GAAA,KAAAgG,MAAA,CAAA3F,IAAA,CAAAL,GAAA;gBAAAsG,SAAA,CAAA3E,CAAA;gBAAA;cAAA;cACAqE,MAAA,CAAAxF,SAAA,CAAA0E,MAAA,CAAAiB,KAAA,KAAA7F,MAAA,CAAAC,MAAA,KAAAyF,MAAA,CAAA3F,IAAA;cAAA,OAAAiG,SAAA,CAAAvE,CAAA;YAAA;cAFAoE,KAAA;cAAAG,SAAA,CAAA3E,CAAA;cAAA;YAAA;cAAA2E,SAAA,CAAA3E,CAAA;cAAA;YAAA;cAAA2E,SAAA,CAAA3E,CAAA;cAAA,OAOA/B,OAAA,CAAAoG,MAAA,CAAA3F,IAAA;YAAA;cAAA+F,cAAA,GAAAE,SAAA,CAAA1E,CAAA;cAAAxB,IAAA,GAAAgG,cAAA,CAAAhG,IAAA;cACA4F,MAAA,CAAA3F,IAAA,CAAAL,GAAA,GAAAI,IAAA,CAAAJ,GAAA;cACAgG,MAAA,CAAAxF,SAAA,CAAA4C,IAAA,CAAA4C,MAAA,CAAA3F,IAAA;YAAA;cAAAgG,WAAA,GAGAL,MAAA,CAAA3F,IAAA,EAAAH,WAAA,GAAAmG,WAAA,CAAAnG,WAAA,EAAAF,GAAA,GAAAqG,WAAA,CAAArG,GAAA,EAAAC,IAAA,GAAAoG,WAAA,CAAApG,IAAA;cACA+F,MAAA,CAAAvF,aAAA;cACAuF,MAAA,CAAAQ,OAAA;gBACAtD,KAAA;gBACAuD,wBAAA;gBACArB,OAAA,kCAAAxB,MAAA,CACA5D,GAAA,0CAAA4D,MAAA,CACA3D,IAAA,4CAAA2D,MAAA,CACA1D,WAAA,uBACA;gBACA4E,IAAA;cACA;YAAA;cAAA,OAAAwB,SAAA,CAAAvE,CAAA;UAAA;QAAA,GAAAkE,QAAA;MAAA;IACA;IACA;IACAlD,mBAAA,WAAAA,oBAAA;MAAA,IAAAlC,QAAA,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAsE,MAAA,GAAAtE,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACA,IAAAqE,YAAA;MACA,IAAAC,eAAA,GAAA/F,QAAA,CAAAgG,MAAA,WAAAC,IAAA;QAAA,QAAAA,IAAA,CAAAhE,MAAA;MAAA;;MAEA;MACA,IAAA8D,eAAA,CAAAvE,MAAA;QACAsE,YAAA,GAAAC,eAAA;QACAD,YAAA,CAAAnH,IAAA,GAAAA,IAAA,CAAAyD,OAAA,CAAAyD,MAAA,CAAAlH,IAAA,EAAAmH,YAAA,CAAAnH,IAAA;QACA,OAAAmH,YAAA;MACA;;MAEA;MACA,IAAAC,eAAA,CAAAvE,MAAA;QACAsE,YAAA,GAAAI,aAAA,CAAAA,aAAA,KAAAL,MAAA;UAAAlH,IAAA;UAAAwH,iBAAA;QAAA;QACA,OAAAL,YAAA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}