{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749148885234}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiAhX3ZtLml0ZW0uaGlkZGVuID8gX2MoImRpdiIsIFtfdm0uaGFzT25lU2hvd2luZ0NoaWxkKF92bS5pdGVtLmNoaWxkcmVuLCBfdm0uaXRlbSkgJiYgKCFfdm0ub25seU9uZUNoaWxkLmNoaWxkcmVuIHx8IF92bS5vbmx5T25lQ2hpbGQubm9TaG93aW5nQ2hpbGRyZW4pICYmICFfdm0uaXRlbS5hbHdheXNTaG93ID8gW192bS5vbmx5T25lQ2hpbGQubWV0YSA/IF9jKCJhcHAtbGluayIsIHsKICAgIGF0dHJzOiB7CiAgICAgIHRvOiBfdm0ucmVzb2x2ZVBhdGgoX3ZtLm9ubHlPbmVDaGlsZC5wYXRoKQogICAgfQogIH0sIFtfYygiZWwtbWVudS1pdGVtIiwgewogICAgY2xhc3M6IHsKICAgICAgInN1Ym1lbnUtdGl0bGUtbm9Ecm9wZG93biI6ICFfdm0uaXNOZXN0CiAgICB9LAogICAgYXR0cnM6IHsKICAgICAgaW5kZXg6IF92bS5yZXNvbHZlUGF0aChfdm0ub25seU9uZUNoaWxkLnBhdGgpCiAgICB9CiAgfSwgW19jKCJpdGVtIiwgewogICAgYXR0cnM6IHsKICAgICAgaWNvbjogX3ZtLm9ubHlPbmVDaGlsZC5tZXRhLmljb24gfHwgX3ZtLml0ZW0ubWV0YSAmJiBfdm0uaXRlbS5tZXRhLmljb24sCiAgICAgIHRpdGxlOiBfdm0ub25seU9uZUNoaWxkLm1ldGEudGl0bGUKICAgIH0KICB9KV0sIDEpXSwgMSkgOiBfdm0uX2UoKV0gOiBfYygiZWwtc3VibWVudSIsIHsKICAgIHJlZjogInN1Yk1lbnUiLAogICAgYXR0cnM6IHsKICAgICAgaW5kZXg6IF92bS5yZXNvbHZlUGF0aChfdm0uaXRlbS5wYXRoKSwKICAgICAgInBvcHBlci1hcHBlbmQtdG8tYm9keSI6ICIiCiAgICB9CiAgfSwgW19jKCJ0ZW1wbGF0ZSIsIHsKICAgIHNsb3Q6ICJ0aXRsZSIKICB9LCBbX3ZtLml0ZW0ubWV0YSA/IF9jKCJpdGVtIiwgewogICAgYXR0cnM6IHsKICAgICAgaWNvbjogX3ZtLml0ZW0ubWV0YSAmJiBfdm0uaXRlbS5tZXRhLmljb24sCiAgICAgIHRpdGxlOiBfdm0uaXRlbS5tZXRhLnRpdGxlCiAgICB9CiAgfSkgOiBfdm0uX2UoKV0sIDEpLCBfdm0uX2woX3ZtLml0ZW0uY2hpbGRyZW4sIGZ1bmN0aW9uIChjaGlsZCkgewogICAgcmV0dXJuIF9jKCJzaWRlYmFyLWl0ZW0iLCB7CiAgICAgIGtleTogY2hpbGQucGF0aCwKICAgICAgc3RhdGljQ2xhc3M6ICJuZXN0LW1lbnUiLAogICAgICBhdHRyczogewogICAgICAgICJpcy1uZXN0IjogdHJ1ZSwKICAgICAgICBpdGVtOiBjaGlsZCwKICAgICAgICAiYmFzZS1wYXRoIjogX3ZtLnJlc29sdmVQYXRoKGNoaWxkLnBhdGgpCiAgICAgIH0KICAgIH0pOwogIH0pXSwgMildLCAyKSA6IF92bS5fZSgpOwp9Owp2YXIgc3RhdGljUmVuZGVyRm5zID0gW107CnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZTsKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfTs="}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "item", "hidden", "hasOneShowingChild", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noShowingChildren", "alwaysShow", "meta", "attrs", "to", "<PERSON><PERSON><PERSON>", "path", "class", "isNest", "index", "icon", "title", "_e", "ref", "slot", "_l", "child", "key", "staticClass", "staticRenderFns", "_withStripped"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/layout/components/Sidebar/SidebarItem.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return !_vm.item.hidden\n    ? _c(\n        \"div\",\n        [\n          _vm.hasOneShowingChild(_vm.item.children, _vm.item) &&\n          (!_vm.onlyOneChild.children || _vm.onlyOneChild.noShowingChildren) &&\n          !_vm.item.alwaysShow\n            ? [\n                _vm.onlyOneChild.meta\n                  ? _c(\n                      \"app-link\",\n                      { attrs: { to: _vm.resolvePath(_vm.onlyOneChild.path) } },\n                      [\n                        _c(\n                          \"el-menu-item\",\n                          {\n                            class: { \"submenu-title-noDropdown\": !_vm.isNest },\n                            attrs: {\n                              index: _vm.resolvePath(_vm.onlyOneChild.path),\n                            },\n                          },\n                          [\n                            _c(\"item\", {\n                              attrs: {\n                                icon:\n                                  _vm.onlyOneChild.meta.icon ||\n                                  (_vm.item.meta && _vm.item.meta.icon),\n                                title: _vm.onlyOneChild.meta.title,\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    )\n                  : _vm._e(),\n              ]\n            : _c(\n                \"el-submenu\",\n                {\n                  ref: \"subMenu\",\n                  attrs: {\n                    index: _vm.resolvePath(_vm.item.path),\n                    \"popper-append-to-body\": \"\",\n                  },\n                },\n                [\n                  _c(\n                    \"template\",\n                    { slot: \"title\" },\n                    [\n                      _vm.item.meta\n                        ? _c(\"item\", {\n                            attrs: {\n                              icon: _vm.item.meta && _vm.item.meta.icon,\n                              title: _vm.item.meta.title,\n                            },\n                          })\n                        : _vm._e(),\n                    ],\n                    1\n                  ),\n                  _vm._l(_vm.item.children, function (child) {\n                    return _c(\"sidebar-item\", {\n                      key: child.path,\n                      staticClass: \"nest-menu\",\n                      attrs: {\n                        \"is-nest\": true,\n                        item: child,\n                        \"base-path\": _vm.resolvePath(child.path),\n                      },\n                    })\n                  }),\n                ],\n                2\n              ),\n        ],\n        2\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAO,CAACD,GAAG,CAACG,IAAI,CAACC,MAAM,GACnBH,EAAE,CACA,KAAK,EACL,CACED,GAAG,CAACK,kBAAkB,CAACL,GAAG,CAACG,IAAI,CAACG,QAAQ,EAAEN,GAAG,CAACG,IAAI,CAAC,KAClD,CAACH,GAAG,CAACO,YAAY,CAACD,QAAQ,IAAIN,GAAG,CAACO,YAAY,CAACC,iBAAiB,CAAC,IAClE,CAACR,GAAG,CAACG,IAAI,CAACM,UAAU,GAChB,CACET,GAAG,CAACO,YAAY,CAACG,IAAI,GACjBT,EAAE,CACA,UAAU,EACV;IAAEU,KAAK,EAAE;MAAEC,EAAE,EAAEZ,GAAG,CAACa,WAAW,CAACb,GAAG,CAACO,YAAY,CAACO,IAAI;IAAE;EAAE,CAAC,EACzD,CACEb,EAAE,CACA,cAAc,EACd;IACEc,KAAK,EAAE;MAAE,0BAA0B,EAAE,CAACf,GAAG,CAACgB;IAAO,CAAC;IAClDL,KAAK,EAAE;MACLM,KAAK,EAAEjB,GAAG,CAACa,WAAW,CAACb,GAAG,CAACO,YAAY,CAACO,IAAI;IAC9C;EACF,CAAC,EACD,CACEb,EAAE,CAAC,MAAM,EAAE;IACTU,KAAK,EAAE;MACLO,IAAI,EACFlB,GAAG,CAACO,YAAY,CAACG,IAAI,CAACQ,IAAI,IACzBlB,GAAG,CAACG,IAAI,CAACO,IAAI,IAAIV,GAAG,CAACG,IAAI,CAACO,IAAI,CAACQ,IAAK;MACvCC,KAAK,EAAEnB,GAAG,CAACO,YAAY,CAACG,IAAI,CAACS;IAC/B;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDnB,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,GACDnB,EAAE,CACA,YAAY,EACZ;IACEoB,GAAG,EAAE,SAAS;IACdV,KAAK,EAAE;MACLM,KAAK,EAAEjB,GAAG,CAACa,WAAW,CAACb,GAAG,CAACG,IAAI,CAACW,IAAI,CAAC;MACrC,uBAAuB,EAAE;IAC3B;EACF,CAAC,EACD,CACEb,EAAE,CACA,UAAU,EACV;IAAEqB,IAAI,EAAE;EAAQ,CAAC,EACjB,CACEtB,GAAG,CAACG,IAAI,CAACO,IAAI,GACTT,EAAE,CAAC,MAAM,EAAE;IACTU,KAAK,EAAE;MACLO,IAAI,EAAElB,GAAG,CAACG,IAAI,CAACO,IAAI,IAAIV,GAAG,CAACG,IAAI,CAACO,IAAI,CAACQ,IAAI;MACzCC,KAAK,EAAEnB,GAAG,CAACG,IAAI,CAACO,IAAI,CAACS;IACvB;EACF,CAAC,CAAC,GACFnB,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACDpB,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACG,IAAI,CAACG,QAAQ,EAAE,UAAUkB,KAAK,EAAE;IACzC,OAAOvB,EAAE,CAAC,cAAc,EAAE;MACxBwB,GAAG,EAAED,KAAK,CAACV,IAAI;MACfY,WAAW,EAAE,WAAW;MACxBf,KAAK,EAAE;QACL,SAAS,EAAE,IAAI;QACfR,IAAI,EAAEqB,KAAK;QACX,WAAW,EAAExB,GAAG,CAACa,WAAW,CAACW,KAAK,CAACV,IAAI;MACzC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACN,EACD,CACF,CAAC,GACDd,GAAG,CAACoB,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAIO,eAAe,GAAG,EAAE;AACxB5B,MAAM,CAAC6B,aAAa,GAAG,IAAI;AAE3B,SAAS7B,MAAM,EAAE4B,eAAe", "ignoreList": []}]}