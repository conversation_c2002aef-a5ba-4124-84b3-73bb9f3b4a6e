{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovMjAyNVx1NTkyN1x1NTIxQl9cdTU3MzBcdTRFMEJcdTc1MzBcdTVFODQvdnVlLWVsZW1lbnQtYWRtaW43LjAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBMb2dvIGZyb20gJy4vTG9nbyc7CmltcG9ydCBTaWRlYmFySXRlbSBmcm9tICcuL1NpZGViYXJJdGVtJzsKaW1wb3J0IF92YXJpYWJsZXMgZnJvbSAnQC9zdHlsZXMvdmFyaWFibGVzLnNjc3MnOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgU2lkZWJhckl0ZW06IFNpZGViYXJJdGVtLAogICAgTG9nbzogTG9nbwogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgbWFwR2V0dGVycyhbJ3Blcm1pc3Npb25fcm91dGVzJywgJ3NpZGViYXInXSkpLCB7fSwgewogICAgYWN0aXZlTWVudTogZnVuY3Rpb24gYWN0aXZlTWVudSgpIHsKICAgICAgdmFyIHJvdXRlID0gdGhpcy4kcm91dGU7CiAgICAgIHZhciBtZXRhID0gcm91dGUubWV0YSwKICAgICAgICBwYXRoID0gcm91dGUucGF0aDsKICAgICAgLy8gaWYgc2V0IHBhdGgsIHRoZSBzaWRlYmFyIHdpbGwgaGlnaGxpZ2h0IHRoZSBwYXRoIHlvdSBzZXQKICAgICAgaWYgKG1ldGEuYWN0aXZlTWVudSkgewogICAgICAgIHJldHVybiBtZXRhLmFjdGl2ZU1lbnU7CiAgICAgIH0KICAgICAgcmV0dXJuIHBhdGg7CiAgICB9LAogICAgc2hvd0xvZ286IGZ1bmN0aW9uIHNob3dMb2dvKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2lkZWJhckxvZ287CiAgICB9LAogICAgdmFyaWFibGVzOiBmdW5jdGlvbiB2YXJpYWJsZXMoKSB7CiAgICAgIHJldHVybiBfdmFyaWFibGVzOwogICAgfSwKICAgIGlzQ29sbGFwc2U6IGZ1bmN0aW9uIGlzQ29sbGFwc2UoKSB7CiAgICAgIHJldHVybiAhdGhpcy5zaWRlYmFyLm9wZW5lZDsKICAgIH0KICB9KQp9Ow=="}, {"version": 3, "names": ["mapGetters", "Logo", "SidebarItem", "variables", "components", "computed", "_objectSpread", "activeMenu", "route", "$route", "meta", "path", "showLogo", "$store", "state", "settings", "sidebarLogo", "isCollapse", "sidebar", "opened"], "sources": ["src/layout/components/Sidebar/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"{'has-logo':showLogo}\">\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n    <el-scrollbar wrap-class=\"scrollbar-wrapper\">\n      <el-menu\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        :background-color=\"variables.menuBg\"\n        :text-color=\"variables.menuText\"\n        :unique-opened=\"false\"\n        :active-text-color=\"variables.menuActiveText\"\n        :collapse-transition=\"false\"\n        mode=\"vertical\"\n      >\n        <sidebar-item v-for=\"route in permission_routes\" :key=\"route.path\" :item=\"route\" :base-path=\"route.path\" />\n      </el-menu>\n    </el-scrollbar>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Logo from './Logo'\nimport SidebarItem from './SidebarItem'\nimport variables from '@/styles/variables.scss'\n\nexport default {\n  components: { SidebarItem, Logo },\n  computed: {\n    ...mapGetters([\n      'permission_routes',\n      'sidebar'\n    ]),\n    activeMenu() {\n      const route = this.$route\n      const { meta, path } = route\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        return meta.activeMenu\n      }\n      return path\n    },\n    showLogo() {\n      return this.$store.state.settings.sidebarLogo\n    },\n    variables() {\n      return variables\n    },\n    isCollapse() {\n      return !this.sidebar.opened\n    }\n  }\n}\n</script>\n"], "mappings": ";AAqBA,SAAAA,UAAA;AACA,OAAAC,IAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA;EACAC,UAAA;IAAAF,WAAA,EAAAA,WAAA;IAAAD,IAAA,EAAAA;EAAA;EACAI,QAAA,EAAAC,aAAA,CAAAA,aAAA,KACAN,UAAA,EACA,qBACA,UACA;IACAO,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAE,IAAA;QAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA;MACA;MACA,IAAAD,IAAA,CAAAH,UAAA;QACA,OAAAG,IAAA,CAAAH,UAAA;MACA;MACA,OAAAI,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,WAAA;IACA;IACAb,SAAA,WAAAA,UAAA;MACA,OAAAA,UAAA;IACA;IACAc,UAAA,WAAAA,WAAA;MACA,aAAAC,OAAA,CAAAC,MAAA;IACA;EAAA;AAEA", "ignoreList": []}]}