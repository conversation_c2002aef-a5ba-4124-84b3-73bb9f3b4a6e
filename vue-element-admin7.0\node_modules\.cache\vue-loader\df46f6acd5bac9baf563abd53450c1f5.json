{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue?vue&type=style&index=0&id=126b135a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue", "mtime": 1747749632954}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5kcmF3ZXItY29udGFpbmVyIHsKICBwYWRkaW5nOiAyNHB4OwogIGZvbnQtc2l6ZTogMTRweDsKICBsaW5lLWhlaWdodDogMS41OwogIHdvcmQtd3JhcDogYnJlYWstd29yZDsKCiAgLmRyYXdlci10aXRsZSB7CiAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjg1KTsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGxpbmUtaGVpZ2h0OiAyMnB4OwogIH0KCiAgLmRyYXdlci1pdGVtIHsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC42NSk7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBwYWRkaW5nOiAxMnB4IDA7CiAgfQoKICAuZHJhd2VyLXN3aXRjaCB7CiAgICBmbG9hdDogcmlnaHQKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Settings", "sourcesContent": ["<template>\n  <div class=\"drawer-container\">\n    <div>\n      <h3 class=\"drawer-title\">页面样式设置</h3>\n\n      <!-- <div class=\"drawer-item\">\n        <span>Te Cohemlor</span>\n        <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n      </div> -->\n\n      <div class=\"drawer-item\">\n        <span>开启标签页面可见</span>\n        <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>固定标签头</span>\n        <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>侧边栏的标志可见</span>\n        <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n      </div>\n\n    </div>\n  </div>\n</template>\n\n<script>\n// import ThemePicker from '@/components/ThemePicker'\n\nexport default {\n  components: {},\n  data() {\n    return {}\n  },\n  computed: {\n    fixedHeader: {\n      get() {\n        return this.$store.state.settings.fixedHeader\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        })\n      }\n    },\n    tagsView: {\n      get() {\n        return this.$store.state.settings.tagsView\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        })\n      }\n    },\n    sidebarLogo: {\n      get() {\n        return this.$store.state.settings.sidebarLogo\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        })\n      }\n    }\n  },\n  methods: {\n    themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.drawer-container {\n  padding: 24px;\n  font-size: 14px;\n  line-height: 1.5;\n  word-wrap: break-word;\n\n  .drawer-title {\n    margin-bottom: 12px;\n    color: rgba(0, 0, 0, .85);\n    font-size: 14px;\n    line-height: 22px;\n  }\n\n  .drawer-item {\n    color: rgba(0, 0, 0, .65);\n    font-size: 14px;\n    padding: 12px 0;\n  }\n\n  .drawer-switch {\n    float: right\n  }\n}\n</style>\n"]}]}