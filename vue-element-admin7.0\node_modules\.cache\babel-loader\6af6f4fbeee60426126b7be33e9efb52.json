{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\updateFile.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\router\\modules\\updateFile.js", "mtime": 1747748935262}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIGZyb20gIkQ6LzIwMjVcdTU5MjdcdTUyMUJfXHU1NzMwXHU0RTBCXHU3NTMwXHU1RTg0L3Z1ZS1lbGVtZW50LWFkbWluNy4wL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgTGF5b3V0IGZyb20gJ0AvbGF5b3V0JzsKdmFyIHVwZGF0ZUZpbGVSb3V0ZXIgPSB7CiAgcGF0aDogJy91cGRhdGVGaWxlJywKICBjb21wb25lbnQ6IExheW91dCwKICByZWRpcmVjdDogJ25vUmVkaXJlY3QnLAogIG5hbWU6ICdVcGRhdGVGaWxlJywKICBtZXRhOiB7CiAgICB0aXRsZTogJ+S4iuS8oOaWh+S7ticsCiAgICBpY29uOiAnY29tcG9uZW50JwogIH0sCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAndXBkYXRlRmlsZScsCiAgICBjb21wb25lbnQ6IGZ1bmN0aW9uIGNvbXBvbmVudCgpIHsKICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCdAL3ZpZXdzL3VwZGF0ZS91cGRhdGVGaWxlLnZ1ZScpKTsKICAgICAgfSk7CiAgICB9LAogICAgbmFtZTogJ1VwZGF0ZUZpbGUnLAogICAgbWV0YTogewogICAgICB0aXRsZTogJ+WvvOWFpeaVsOaNricKICAgIH0KICB9XQp9OwpleHBvcnQgZGVmYXVsdCB1cGRhdGVGaWxlUm91dGVyOw=="}, {"version": 3, "names": ["Layout", "updateFileRouter", "path", "component", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard", "require"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/router/modules/updateFile.js"], "sourcesContent": ["import Layout from '@/layout'\r\n\r\nconst updateFileRouter = {\r\n  path: '/updateFile',\r\n  component: Layout,\r\n  redirect: 'noRedirect',\r\n  name: 'UpdateFile',\r\n  meta: {\r\n    title: '上传文件',\r\n    icon: 'component'\r\n  },\r\n  children: [\r\n    {\r\n      path: 'updateFile',\r\n      component: () => import('@/views/update/updateFile.vue'),\r\n      name: 'UpdateFile',\r\n      meta: { title: '导入数据' }\r\n    }\r\n  ]\r\n}\r\nexport default updateFileRouter\r\n"], "mappings": ";;;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,IAAMC,gBAAgB,GAAG;EACvBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEH,MAAM;EACjBI,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAO,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDT,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC;AACD,eAAeN,gBAAgB", "ignoreList": []}]}