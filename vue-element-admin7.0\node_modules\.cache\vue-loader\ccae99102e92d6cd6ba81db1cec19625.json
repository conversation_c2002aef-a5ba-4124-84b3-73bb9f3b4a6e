{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\role.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\role.vue", "mtime": 1732096978000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["role.vue"], "names": [], "mappings": ";AA8DA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "role.vue", "sourceRoot": "src/views/permission", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-button type=\"primary\" @click=\"handleAddRole\">New Role</el-button>\n\n    <el-table :data=\"rolesList\" style=\"width: 100%;margin-top:30px;\" border>\n      <el-table-column align=\"center\" label=\"Role Key\" width=\"220\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.key }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"center\" label=\"Role Name\" width=\"220\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.name }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"header-center\" label=\"Description\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.description }}\n        </template>\n      </el-table-column>\n      <el-table-column align=\"center\" label=\"Operations\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"primary\" size=\"small\" @click=\"handleEdit(scope)\">Edit</el-button>\n          <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope)\">Delete</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <el-dialog :visible.sync=\"dialogVisible\" :title=\"dialogType==='edit'?'Edit Role':'New Role'\">\n      <el-form :model=\"role\" label-width=\"80px\" label-position=\"left\">\n        <el-form-item label=\"Name\">\n          <el-input v-model=\"role.name\" placeholder=\"Role Name\" />\n        </el-form-item>\n        <el-form-item label=\"Desc\">\n          <el-input\n            v-model=\"role.description\"\n            :autosize=\"{ minRows: 2, maxRows: 4}\"\n            type=\"textarea\"\n            placeholder=\"Role Description\"\n          />\n        </el-form-item>\n        <el-form-item label=\"Menus\">\n          <el-tree\n            ref=\"tree\"\n            :check-strictly=\"checkStrictly\"\n            :data=\"routesData\"\n            :props=\"defaultProps\"\n            show-checkbox\n            node-key=\"path\"\n            class=\"permission-tree\"\n          />\n        </el-form-item>\n      </el-form>\n      <div style=\"text-align:right;\">\n        <el-button type=\"danger\" @click=\"dialogVisible=false\">Cancel</el-button>\n        <el-button type=\"primary\" @click=\"confirmRole\">Confirm</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport path from 'path'\nimport { deepClone } from '@/utils'\nimport { getRoutes, getRoles, addRole, deleteRole, updateRole } from '@/api/role'\n\nconst defaultRole = {\n  key: '',\n  name: '',\n  description: '',\n  routes: []\n}\n\nexport default {\n  data() {\n    return {\n      role: Object.assign({}, defaultRole),\n      routes: [],\n      rolesList: [],\n      dialogVisible: false,\n      dialogType: 'new',\n      checkStrictly: false,\n      defaultProps: {\n        children: 'children',\n        label: 'title'\n      }\n    }\n  },\n  computed: {\n    routesData() {\n      return this.routes\n    }\n  },\n  created() {\n    // Mock: get all routes and roles list from server\n    this.getRoutes()\n    this.getRoles()\n  },\n  methods: {\n    async getRoutes() {\n      const res = await getRoutes()\n      this.serviceRoutes = res.data\n      this.routes = this.generateRoutes(res.data)\n    },\n    async getRoles() {\n      const res = await getRoles()\n      this.rolesList = res.data\n    },\n\n    // Reshape the routes structure so that it looks the same as the sidebar\n    generateRoutes(routes, basePath = '/') {\n      const res = []\n\n      for (let route of routes) {\n        // skip some route\n        if (route.hidden) { continue }\n\n        const onlyOneShowingChild = this.onlyOneShowingChild(route.children, route)\n\n        if (route.children && onlyOneShowingChild && !route.alwaysShow) {\n          route = onlyOneShowingChild\n        }\n\n        const data = {\n          path: path.resolve(basePath, route.path),\n          title: route.meta && route.meta.title\n\n        }\n\n        // recursive child routes\n        if (route.children) {\n          data.children = this.generateRoutes(route.children, data.path)\n        }\n        res.push(data)\n      }\n      return res\n    },\n    generateArr(routes) {\n      let data = []\n      routes.forEach(route => {\n        data.push(route)\n        if (route.children) {\n          const temp = this.generateArr(route.children)\n          if (temp.length > 0) {\n            data = [...data, ...temp]\n          }\n        }\n      })\n      return data\n    },\n    handleAddRole() {\n      this.role = Object.assign({}, defaultRole)\n      if (this.$refs.tree) {\n        this.$refs.tree.setCheckedNodes([])\n      }\n      this.dialogType = 'new'\n      this.dialogVisible = true\n    },\n    handleEdit(scope) {\n      this.dialogType = 'edit'\n      this.dialogVisible = true\n      this.checkStrictly = true\n      this.role = deepClone(scope.row)\n      this.$nextTick(() => {\n        const routes = this.generateRoutes(this.role.routes)\n        this.$refs.tree.setCheckedNodes(this.generateArr(routes))\n        // set checked state of a node not affects its father and child nodes\n        this.checkStrictly = false\n      })\n    },\n    handleDelete({ $index, row }) {\n      this.$confirm('Confirm to remove the role?', 'Warning', {\n        confirmButtonText: 'Confirm',\n        cancelButtonText: 'Cancel',\n        type: 'warning'\n      })\n        .then(async() => {\n          await deleteRole(row.key)\n          this.rolesList.splice($index, 1)\n          this.$message({\n            type: 'success',\n            message: 'Delete succed!'\n          })\n        })\n        .catch(error => { console.log(error) })\n    },\n    generateTree(routes, basePath = '/', checkedKeys) {\n      const res = []\n\n      for (const route of routes) {\n        const routePath = path.resolve(basePath, route.path)\n\n        // recursive child routes\n        if (route.children) {\n          route.children = this.generateTree(route.children, routePath, checkedKeys)\n        }\n\n        if (checkedKeys.includes(routePath) || (route.children && route.children.length >= 1)) {\n          res.push(route)\n        }\n      }\n      return res\n    },\n    async confirmRole() {\n      const isEdit = this.dialogType === 'edit'\n\n      const checkedKeys = this.$refs.tree.getCheckedKeys()\n      this.role.routes = this.generateTree(deepClone(this.serviceRoutes), '/', checkedKeys)\n\n      if (isEdit) {\n        await updateRole(this.role.key, this.role)\n        for (let index = 0; index < this.rolesList.length; index++) {\n          if (this.rolesList[index].key === this.role.key) {\n            this.rolesList.splice(index, 1, Object.assign({}, this.role))\n            break\n          }\n        }\n      } else {\n        const { data } = await addRole(this.role)\n        this.role.key = data.key\n        this.rolesList.push(this.role)\n      }\n\n      const { description, key, name } = this.role\n      this.dialogVisible = false\n      this.$notify({\n        title: 'Success',\n        dangerouslyUseHTMLString: true,\n        message: `\n            <div>Role Key: ${key}</div>\n            <div>Role Name: ${name}</div>\n            <div>Description: ${description}</div>\n          `,\n        type: 'success'\n      })\n    },\n    // reference: src/view/layout/components/Sidebar/SidebarItem.vue\n    onlyOneShowingChild(children = [], parent) {\n      let onlyOneChild = null\n      const showingChildren = children.filter(item => !item.hidden)\n\n      // When there is only one child route, the child route is displayed by default\n      if (showingChildren.length === 1) {\n        onlyOneChild = showingChildren[0]\n        onlyOneChild.path = path.resolve(parent.path, onlyOneChild.path)\n        return onlyOneChild\n      }\n\n      // Show parent if there are no child route to display\n      if (showingChildren.length === 0) {\n        onlyOneChild = { ... parent, path: '', noShowingChildren: true }\n        return onlyOneChild\n      }\n\n      return false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .roles-table {\n    margin-top: 30px;\n  }\n  .permission-tree {\n    margin-bottom: 30px;\n  }\n}\n</style>\n"]}]}