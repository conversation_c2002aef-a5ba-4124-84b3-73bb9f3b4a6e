{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovMjAyNVx1NTkyN1x1NTIxQl9cdTU3MzBcdTRFMEJcdTc1MzBcdTVFODQvdnVlLWVsZW1lbnQtYWRtaW43LjAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZpbHRlci5qcyI7CmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnOwppbXBvcnQgeyBpc0V4dGVybmFsIH0gZnJvbSAnQC91dGlscy92YWxpZGF0ZSc7CmltcG9ydCBJdGVtIGZyb20gJy4vSXRlbSc7CmltcG9ydCBBcHBMaW5rIGZyb20gJy4vTGluayc7CmltcG9ydCBGaXhpT1NCdWcgZnJvbSAnLi9GaXhpT1NCdWcnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NpZGViYXJJdGVtJywKICBjb21wb25lbnRzOiB7CiAgICBJdGVtOiBJdGVtLAogICAgQXBwTGluazogQXBwTGluawogIH0sCiAgbWl4aW5zOiBbRml4aU9TQnVnXSwKICBwcm9wczogewogICAgLy8gcm91dGUgb2JqZWN0CiAgICBpdGVtOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBpc05lc3Q6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICBiYXNlUGF0aDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgLy8gVG8gZml4IGh0dHBzOi8vZ2l0aHViLmNvbS9QYW5KaWFDaGVuL3Z1ZS1hZG1pbi10ZW1wbGF0ZS9pc3N1ZXMvMjM3CiAgICAvLyBUT0RPOiByZWZhY3RvciB3aXRoIHJlbmRlciBmdW5jdGlvbgogICAgdGhpcy5vbmx5T25lQ2hpbGQgPSBudWxsOwogICAgcmV0dXJuIHt9OwogIH0sCiAgbWV0aG9kczogewogICAgaGFzT25lU2hvd2luZ0NoaWxkOiBmdW5jdGlvbiBoYXNPbmVTaG93aW5nQ2hpbGQoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciBjaGlsZHJlbiA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogW107CiAgICAgIHZhciBwYXJlbnQgPSBhcmd1bWVudHMubGVuZ3RoID4gMSA/IGFyZ3VtZW50c1sxXSA6IHVuZGVmaW5lZDsKICAgICAgdmFyIHNob3dpbmdDaGlsZHJlbiA9IGNoaWxkcmVuLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChpdGVtLmhpZGRlbikgewogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyBUZW1wIHNldCh3aWxsIGJlIHVzZWQgaWYgb25seSBoYXMgb25lIHNob3dpbmcgY2hpbGQpCiAgICAgICAgICBfdGhpcy5vbmx5T25lQ2hpbGQgPSBpdGVtOwogICAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIC8vIFdoZW4gdGhlcmUgaXMgb25seSBvbmUgY2hpbGQgcm91dGVyLCB0aGUgY2hpbGQgcm91dGVyIGlzIGRpc3BsYXllZCBieSBkZWZhdWx0CiAgICAgIGlmIChzaG93aW5nQ2hpbGRyZW4ubGVuZ3RoID09PSAxKSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KCiAgICAgIC8vIFNob3cgcGFyZW50IGlmIHRoZXJlIGFyZSBubyBjaGlsZCByb3V0ZXIgdG8gZGlzcGxheQogICAgICBpZiAoc2hvd2luZ0NoaWxkcmVuLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMub25seU9uZUNoaWxkID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBwYXJlbnQpLCB7fSwgewogICAgICAgICAgcGF0aDogJycsCiAgICAgICAgICBub1Nob3dpbmdDaGlsZHJlbjogdHJ1ZQogICAgICAgIH0pOwogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0sCiAgICByZXNvbHZlUGF0aDogZnVuY3Rpb24gcmVzb2x2ZVBhdGgocm91dGVQYXRoKSB7CiAgICAgIGlmIChpc0V4dGVybmFsKHJvdXRlUGF0aCkpIHsKICAgICAgICByZXR1cm4gcm91dGVQYXRoOwogICAgICB9CiAgICAgIGlmIChpc0V4dGVybmFsKHRoaXMuYmFzZVBhdGgpKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuYmFzZVBhdGg7CiAgICAgIH0KICAgICAgcmV0dXJuIHBhdGgucmVzb2x2ZSh0aGlzLmJhc2VQYXRoLCByb3V0ZVBhdGgpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["path", "isExternal", "<PERSON><PERSON>", "AppLink", "FixiOSBug", "name", "components", "mixins", "props", "item", "type", "Object", "required", "isNest", "Boolean", "default", "basePath", "String", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods", "hasOneShowingChild", "_this", "children", "arguments", "length", "undefined", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "hidden", "_objectSpread", "noShowingChildren", "<PERSON><PERSON><PERSON>", "routePath", "resolve"], "sources": ["src/layout/components/Sidebar/SidebarItem.vue"], "sourcesContent": ["<template>\n  <div v-if=\"!item.hidden\">\n    <template v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path)\">\n        <el-menu-item :index=\"resolvePath(onlyOneChild.path)\" :class=\"{'submenu-title-noDropdown':!isNest}\">\n          <item :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\" :title=\"onlyOneChild.meta.title\" />\n        </el-menu-item>\n      </app-link>\n    </template>\n\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\n      <template slot=\"title\">\n        <item v-if=\"item.meta\" :icon=\"item.meta && item.meta.icon\" :title=\"item.meta.title\" />\n      </template>\n      <sidebar-item\n        v-for=\"child in item.children\"\n        :key=\"child.path\"\n        :is-nest=\"true\"\n        :item=\"child\"\n        :base-path=\"resolvePath(child.path)\"\n        class=\"nest-menu\"\n      />\n    </el-submenu>\n  </div>\n</template>\n\n<script>\nimport path from 'path'\nimport { isExternal } from '@/utils/validate'\nimport Item from './Item'\nimport AppLink from './Link'\nimport FixiOSBug from './FixiOSBug'\n\nexport default {\n  name: 'SidebarItem',\n  components: { Item, AppLink },\n  mixins: [FixiOSBug],\n  props: {\n    // route object\n    item: {\n      type: Object,\n      required: true\n    },\n    isNest: {\n      type: Boolean,\n      default: false\n    },\n    basePath: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237\n    // TODO: refactor with render function\n    this.onlyOneChild = null\n    return {}\n  },\n  methods: {\n    hasOneShowingChild(children = [], parent) {\n      const showingChildren = children.filter(item => {\n        if (item.hidden) {\n          return false\n        } else {\n          // Temp set(will be used if only has one showing child)\n          this.onlyOneChild = item\n          return true\n        }\n      })\n\n      // When there is only one child router, the child router is displayed by default\n      if (showingChildren.length === 1) {\n        return true\n      }\n\n      // Show parent if there are no child router to display\n      if (showingChildren.length === 0) {\n        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }\n        return true\n      }\n\n      return false\n    },\n    resolvePath(routePath) {\n      if (isExternal(routePath)) {\n        return routePath\n      }\n      if (isExternal(this.basePath)) {\n        return this.basePath\n      }\n      return path.resolve(this.basePath, routePath)\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;AA2BA,OAAAA,IAAA;AACA,SAAAC,UAAA;AACA,OAAAC,IAAA;AACA,OAAAC,OAAA;AACA,OAAAC,SAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAJ,IAAA,EAAAA,IAAA;IAAAC,OAAA,EAAAA;EAAA;EACAI,MAAA,GAAAH,SAAA;EACAI,KAAA;IACA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAN,IAAA,EAAAO,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;IACA;IACA,KAAAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,MAAA,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACA,IAAAE,eAAA,GAAAL,QAAA,CAAAM,MAAA,WAAApB,IAAA;QACA,IAAAA,IAAA,CAAAqB,MAAA;UACA;QACA;UACA;UACAR,KAAA,CAAAH,YAAA,GAAAV,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAmB,eAAA,CAAAH,MAAA;QACA;MACA;;MAEA;MACA,IAAAG,eAAA,CAAAH,MAAA;QACA,KAAAN,YAAA,GAAAY,aAAA,CAAAA,aAAA,KAAAJ,MAAA;UAAA3B,IAAA;UAAAgC,iBAAA;QAAA;QACA;MACA;MAEA;IACA;IACAC,WAAA,WAAAA,YAAAC,SAAA;MACA,IAAAjC,UAAA,CAAAiC,SAAA;QACA,OAAAA,SAAA;MACA;MACA,IAAAjC,UAAA,MAAAe,QAAA;QACA,YAAAA,QAAA;MACA;MACA,OAAAhB,IAAA,CAAAmC,OAAA,MAAAnB,QAAA,EAAAkB,SAAA;IACA;EACA;AACA", "ignoreList": []}]}