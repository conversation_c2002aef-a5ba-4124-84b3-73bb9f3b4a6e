{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\components\\SocialSignin.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\components\\SocialSignin.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gaW1wb3J0IG9wZW5XaW5kb3cgZnJvbSAnQC91dGlscy9vcGVuLXdpbmRvdycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU29jaWFsU2lnbmluJywKICBtZXRob2RzOiB7CiAgICB3ZWNoYXRIYW5kbGVDbGljazogZnVuY3Rpb24gd2VjaGF0SGFuZGxlQ2xpY2sodGhpcmRwYXJ0KSB7CiAgICAgIGFsZXJ0KCdvaycpOwogICAgICAvLyB0aGlzLiRzdG9yZS5jb21taXQoJ1NFVF9BVVRIX1RZUEUnLCB0aGlyZHBhcnQpCiAgICAgIC8vIGNvbnN0IGFwcGlkID0gJ3h4eHh4JwogICAgICAvLyBjb25zdCByZWRpcmVjdF91cmkgPSBlbmNvZGVVUklDb21wb25lbnQoJ3h4eC9yZWRpcmVjdD9yZWRpcmVjdD0nICsgd2luZG93LmxvY2F0aW9uLm9yaWdpbiArICcvYXV0aC1yZWRpcmVjdCcpCiAgICAgIC8vIGNvbnN0IHVybCA9ICdodHRwczovL29wZW4ud2VpeGluLnFxLmNvbS9jb25uZWN0L3FyY29ubmVjdD9hcHBpZD0nICsgYXBwaWQgKyAnJnJlZGlyZWN0X3VyaT0nICsgcmVkaXJlY3RfdXJpICsgJyZyZXNwb25zZV90eXBlPWNvZGUmc2NvcGU9c25zYXBpX2xvZ2luI3dlY2hhdF9yZWRpcmVjdCcKICAgICAgLy8gb3BlbldpbmRvdyh1cmwsIHRoaXJkcGFydCwgNTQwLCA1NDApCiAgICB9LAogICAgdGVuY2VudEhhbmRsZUNsaWNrOiBmdW5jdGlvbiB0ZW5jZW50SGFuZGxlQ2xpY2sodGhpcmRwYXJ0KSB7CiAgICAgIGFsZXJ0KCdvaycpOwogICAgICAvLyB0aGlzLiRzdG9yZS5jb21taXQoJ1NFVF9BVVRIX1RZUEUnLCB0aGlyZHBhcnQpCiAgICAgIC8vIGNvbnN0IGNsaWVudF9pZCA9ICd4eHh4eCcKICAgICAgLy8gY29uc3QgcmVkaXJlY3RfdXJpID0gZW5jb2RlVVJJQ29tcG9uZW50KCd4eHgvcmVkaXJlY3Q/cmVkaXJlY3Q9JyArIHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4gKyAnL2F1dGgtcmVkaXJlY3QnKQogICAgICAvLyBjb25zdCB1cmwgPSAnaHR0cHM6Ly9ncmFwaC5xcS5jb20vb2F1dGgyLjAvYXV0aG9yaXplP3Jlc3BvbnNlX3R5cGU9Y29kZSZjbGllbnRfaWQ9JyArIGNsaWVudF9pZCArICcmcmVkaXJlY3RfdXJpPScgKyByZWRpcmVjdF91cmkKICAgICAgLy8gb3BlbldpbmRvdyh1cmwsIHRoaXJkcGFydCwgNTQwLCA1NDApCiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "methods", "wechatHandleClick", "thirdpart", "alert", "tencentHandleClick"], "sources": ["src/views/login/components/SocialSignin.vue"], "sourcesContent": ["<template>\n  <div class=\"social-signup-container\">\n    <div class=\"sign-btn\" @click=\"wechatHandleClick('wechat')\">\n      <span class=\"wx-svg-container\"><svg-icon icon-class=\"wechat\" class=\"icon\" /></span>\n      WeChat\n    </div>\n    <div class=\"sign-btn\" @click=\"tencentHandleClick('tencent')\">\n      <span class=\"qq-svg-container\"><svg-icon icon-class=\"qq\" class=\"icon\" /></span>\n      QQ\n    </div>\n  </div>\n</template>\n\n<script>\n// import openWindow from '@/utils/open-window'\n\nexport default {\n  name: 'SocialSignin',\n  methods: {\n    wechatHandleClick(thirdpart) {\n      alert('ok')\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\n      // const appid = 'xxxxx'\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\n      // const url = 'https://open.weixin.qq.com/connect/qrconnect?appid=' + appid + '&redirect_uri=' + redirect_uri + '&response_type=code&scope=snsapi_login#wechat_redirect'\n      // openWindow(url, thirdpart, 540, 540)\n    },\n    tencentHandleClick(thirdpart) {\n      alert('ok')\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\n      // const client_id = 'xxxxx'\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\n      // const url = 'https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=' + client_id + '&redirect_uri=' + redirect_uri\n      // openWindow(url, thirdpart, 540, 540)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .social-signup-container {\n    margin: 20px 0;\n    .sign-btn {\n      display: inline-block;\n      cursor: pointer;\n    }\n    .icon {\n      color: #fff;\n      font-size: 24px;\n      margin-top: 8px;\n    }\n    .wx-svg-container,\n    .qq-svg-container {\n      display: inline-block;\n      width: 40px;\n      height: 40px;\n      line-height: 40px;\n      text-align: center;\n      padding-top: 1px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n      margin-right: 5px;\n    }\n    .wx-svg-container {\n      background-color: #24da70;\n    }\n    .qq-svg-container {\n      background-color: #6BA2D6;\n      margin-left: 50px;\n    }\n  }\n</style>\n"], "mappings": "AAcA;;AAEA;EACAA,IAAA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAAC,SAAA;MACAC,KAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAAF,SAAA;MACAC,KAAA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}