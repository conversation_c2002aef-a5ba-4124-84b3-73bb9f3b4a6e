{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\editor\\index.vue?vue&type=style&index=0&id=4dfc1336&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\editor\\index.vue", "mtime": 1731833050000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5lbXB0eUdpZiB7CiAgZGlzcGxheTogYmxvY2s7CiAgd2lkdGg6IDQ1JTsKICBtYXJnaW46IDAgYXV0bzsKfQoKLmRhc2hib2FyZC1lZGl0b3ItY29udGFpbmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTNlM2UzOwogIG1pbi1oZWlnaHQ6IDEwMHZoOwogIHBhZGRpbmc6IDUwcHggNjBweCAwcHg7CiAgLnBhbi1pbmZvLXJvbGVzIHsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgIGZvbnQtd2VpZ2h0OiA3MDA7CiAgICBjb2xvcjogIzMzMzsKICAgIGRpc3BsYXk6IGJsb2NrOwogIH0KICAuaW5mby1jb250YWluZXIgewogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgbWFyZ2luLWxlZnQ6IDE5MHB4OwogICAgaGVpZ2h0OiAxNTBweDsKICAgIGxpbmUtaGVpZ2h0OiAyMDBweDsKICAgIC5kaXNwbGF5X25hbWUgewogICAgICBmb250LXNpemU6IDQ4cHg7CiAgICAgIGxpbmUtaGVpZ2h0OiA0OHB4OwogICAgICBjb2xvcjogIzIxMjEyMTsKICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICB0b3A6IDI1cHg7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard/editor", "sourcesContent": ["<template>\n  <div class=\"dashboard-editor-container\">\n    <div class=\" clearfix\">\n      <p>哈哈哈哈</p>\n      <pan-thumb :image=\"avatar\" style=\"float: left\">\n        Your roles:\n        <span v-for=\"item in roles\" :key=\"item\" class=\"pan-info-roles\">{{ item }}</span>\n      </pan-thumb>\n      <div class=\"info-container\">\n        <span class=\"display_name\">{{ name }}</span>\n        <span style=\"font-size:20px;padding-top:20px;display:inline-block;\">Editor's Dashboard</span>\n      </div>\n    </div>\n    <div>\n      <img :src=\"emptyGif\" class=\"emptyGif\">\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport PanThumb from '@/components/PanThumb'\n\nexport default {\n  name: 'DashboardEditor',\n  components: { PanThumb },\n  data() {\n    return {\n      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3'\n    }\n  },\n  computed: {\n    ...mapGetters([\n      'name',\n      'avatar',\n      'roles'\n    ])\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .emptyGif {\n    display: block;\n    width: 45%;\n    margin: 0 auto;\n  }\n\n  .dashboard-editor-container {\n    background-color: #e3e3e3;\n    min-height: 100vh;\n    padding: 50px 60px 0px;\n    .pan-info-roles {\n      font-size: 12px;\n      font-weight: 700;\n      color: #333;\n      display: block;\n    }\n    .info-container {\n      position: relative;\n      margin-left: 190px;\n      height: 150px;\n      line-height: 200px;\n      .display_name {\n        font-size: 48px;\n        line-height: 48px;\n        color: #212121;\n        position: absolute;\n        top: 25px;\n      }\n    }\n  }\n</style>\n"]}]}