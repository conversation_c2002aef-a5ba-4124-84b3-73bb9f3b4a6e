{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\index.vue", "mtime": 1731833096000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgYWRtaW5EYXNoYm9hcmQgZnJvbSAnLi9hZG1pbicKaW1wb3J0IGVkaXRvckRhc2hib2FyZCBmcm9tICcuL2VkaXRvcicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGFzaGJvYXJkJywKICBjb21wb25lbnRzOiB7IGFkbWluRGFzaGJvYXJkLCBlZGl0b3JEYXNoYm9hcmQgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY3VycmVudFJvbGU6ICdhZG1pbkRhc2hib2FyZCcKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKFsKICAgICAgJ3JvbGVzJwogICAgXSkKICB9LAogIGNyZWF0ZWQoKSB7CiAgICAvLyBpZiAoIXRoaXMucm9sZXMuaW5jbHVkZXMoJ2FkbWluJykpIHsKICAgIC8vICAgdGhpcy5jdXJyZW50Um9sZSA9ICdlZGl0b3JEYXNoYm9hcmQnCiAgICAvLyB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAOA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <component :is=\"currentRole\" />\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport adminDashboard from './admin'\nimport editorDashboard from './editor'\n\nexport default {\n  name: 'Dashboard',\n  components: { adminDashboard, editorDashboard },\n  data() {\n    return {\n      currentRole: 'adminDashboard'\n    }\n  },\n  computed: {\n    ...mapGetters([\n      'roles'\n    ])\n  },\n  created() {\n    // if (!this.roles.includes('admin')) {\n    //   this.currentRole = 'editorDashboard'\n    // }\n  }\n}\n</script>\n"]}]}