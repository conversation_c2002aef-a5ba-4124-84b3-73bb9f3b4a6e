{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\logManager\\logManager1.vue?vue&type=style&index=0&id=6286f1dc&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\logManager\\logManager1.vue", "mtime": 1747749486341}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5maWx0ZXItaXRlbSB7CiAgbWFyZ2luOiAwIDE1cHg7Cn0KLnNlYXJjaF9zdWJtaXQgewogIHBvc2l0aW9uOiByZWxhdGl2ZTsgLyog55u45a+55a6a5L2N77yM5Lul5L6/5Lyq5YWD57Sg5a6a5L2NICovCiAgb3ZlcmZsb3c6IGhpZGRlbjsgLyog6ZqQ6JeP5rqi5Ye66YOo5YiGICovCiAgYm9yZGVyOiBub25lOyAvKiDljrvmjonmjInpkq7ovrnmoYYgKi8KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA3YmZmZDU7IC8qIOaMiemSruiDjOaZr+minOiJsiAqLwogIGNvbG9yOiB3aGl0ZTsgLyog5a2X5L2T6aKc6ImyICovCiAgcGFkZGluZzogMTBweCAyMHB4OyAvKiDmjInpkq7lhoXovrnot50gKi8KICBib3JkZXItcmFkaXVzOiA0cHg7IC8qIOWchuinkiAqLwogIGN1cnNvcjogcG9pbnRlcjsgLyog6byg5qCH5oyH6ZKIICovCiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjNzOyAvKiDog4zmma/popzoibLov4fmuKEgKi8KfQo="}, {"version": 3, "sources": ["logManager1.vue"], "names": [], "mappings": ";AAwMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "logManager1.vue", "sourceRoot": "src/views/logManager", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <div class=\"filter-container\">\r\n        <!-- <el-input\r\n          v-model=\"time\"\r\n          placeholder=\"时间\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        /> -->\r\n        <el-date-picker\r\n            v-model=\"time\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期时间\"\r\n            end-placeholder=\"结束日期时间\"\r\n            format=\"yyyy-MM-dd HH:mm:ss\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 350px; margin-top: 7px;\"\r\n        />\r\n\r\n        <el-input\r\n          v-model=\"username\"\r\n          placeholder=\"用户名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n\r\n        <el-input\r\n          v-model=\"fileName\"\r\n          placeholder=\"文件名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n        <el-input\r\n          v-model=\"databaseName\"\r\n          placeholder=\"数据库名\"\r\n          style=\"width: 200px; margin-top: 7px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"handleFilter\"\r\n        />\r\n        <el-button\r\n          class=\"filter-item search_submit\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          @click=\"handleFilter\"\r\n        >\r\n          搜索\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :key=\"tableKey\"\r\n        v-loading=\"listLoading\"\r\n        :data=\"list\"\r\n        border\r\n        fit\r\n        highlight-current-row\r\n        style=\"width: 100%\"\r\n        @sort-change=\"sortChange\"\r\n      >\r\n        <el-table-column\r\n          label=\"时间\"\r\n          prop=\"time\"\r\n          width=\"150px\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.time }}</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"用户名\" prop=\"username\" min-width=\"150px\">\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.username }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文件名\" prop=\"fileName\" min-width=\"150px\">\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.fileName }}</span>\r\n            <!-- <span class=\"link-cardNumber\" @click=\"handleUpdate(row)\">{{ row.title }}</span>\r\n            <el-tag>{{ row.type | typeFilter }}</el-tag> -->\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"数据库名\"\r\n          prop=\"databaseName\"\r\n          width=\"160px\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"{ row }\">\r\n            <span>{{ row.databaseName }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"listQuery.page\"\r\n        :limit.sync=\"listQuery.pagelimit\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n  </template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport waves from '@/directive/waves' // waves directive\r\n// import { parseTime } from '@/utils'\r\nimport Pagination from '@/components/Pagination' // secondary package based on el-pagination\r\n\r\nexport default {\r\n  name: 'LogManager',\r\n  components: { Pagination },\r\n  directives: { waves },\r\n  data() {\r\n    return {\r\n      list: null,\r\n      rawData: null,\r\n      tableKey: 0,\r\n      total: 0,\r\n      listLoading: true,\r\n      time: [],\r\n      username: '',\r\n      fileName: '',\r\n      databaseName: '',\r\n      listQuery: {\r\n        page: 1,\r\n        pagelimit: 10,\r\n        time: null,\r\n        username: null,\r\n        fileName: null,\r\n        databaseName: null\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.listLoading = true\r\n      axios\r\n        .post('http://127.0.0.1:8000/all_log_history', this.listQuery) // 使用 POST 请求\r\n        .then((response) => {\r\n          this.rawData = response.data.items\r\n          this.total = response.data.total\r\n          this.list = this.rawData.map((item) => {\r\n            return {\r\n              time: item[0] === 'None' ? '空' : item[0],\r\n              username: item[1] === 'None' ? '空' : item[1],\r\n              fileName: item[2] === 'None' ? '空' : item[2],\r\n              databaseName: item[3] === 'None' ? '空' : item[3]\r\n            }\r\n          })\r\n        })\r\n        .catch((error) => {\r\n          console.error('Error fetching the list:', error)\r\n        })\r\n        .finally(() => {\r\n          // 模拟请求时间\r\n          setTimeout(() => {\r\n            this.listLoading = false\r\n          }, 1500)\r\n        })\r\n    },\r\n    handleFilter() {\r\n      console.log('handleFilter called')\r\n      this.listQuery.page = 1\r\n      this.listQuery.time = this.time || null\r\n      this.listQuery.username = this.username || null\r\n      this.listQuery.fileName = this.fileName || null\r\n      this.listQuery.databaseName = this.databaseName\r\n      console.log(this.listQuery)\r\n      this.getList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style>\r\n  .filter-item {\r\n    margin: 0 15px;\r\n  }\r\n  .search_submit {\r\n    position: relative; /* 相对定位，以便伪元素定位 */\r\n    overflow: hidden; /* 隐藏溢出部分 */\r\n    border: none; /* 去掉按钮边框 */\r\n    background-color: #007bffd5; /* 按钮背景颜色 */\r\n    color: white; /* 字体颜色 */\r\n    padding: 10px 20px; /* 按钮内边距 */\r\n    border-radius: 4px; /* 圆角 */\r\n    cursor: pointer; /* 鼠标指针 */\r\n    transition: background-color 0.3s; /* 背景颜色过渡 */\r\n  }\r\n  </style>\r\n"]}]}