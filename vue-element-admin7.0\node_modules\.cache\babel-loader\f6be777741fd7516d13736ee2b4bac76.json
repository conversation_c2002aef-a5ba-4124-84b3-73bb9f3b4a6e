{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ScrollPane", "path", "components", "data", "visible", "top", "left", "selectedTag", "affixTags", "computed", "visitedViews", "$store", "state", "tagsView", "routes", "permission", "watch", "$route", "addTags", "moveToCurrentTag", "value", "document", "body", "addEventListener", "closeMenu", "removeEventListener", "mounted", "initTags", "methods", "isActive", "route", "isAffix", "tag", "meta", "affix", "filterAffixTags", "_this", "basePath", "arguments", "length", "undefined", "tags", "for<PERSON>ach", "tagPath", "resolve", "push", "fullPath", "name", "_objectSpread", "children", "tempTags", "concat", "_toConsumableArray", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "dispatch", "err", "e", "f", "_this2", "$refs", "$nextTick", "_iterator2", "_step2", "to", "scrollPane", "move<PERSON><PERSON><PERSON>arget", "refreshSelectedTag", "view", "_this3", "then", "$router", "replace", "closeSelectedTag", "_this4", "_ref", "toLastView", "closeOthersTags", "_this5", "closeAllTags", "_this6", "_ref2", "some", "latestView", "slice", "openMenu", "menu<PERSON>in<PERSON>idth", "offsetLeft", "$el", "getBoundingClientRect", "offsetWidth", "maxLeft", "clientX", "clientY", "handleScroll"], "sources": ["src/layout/components/TagsView/index.vue"], "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"isActive(tag)?'active':''\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\">Refresh</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\">Close</li>\n      <li @click=\"closeOthersTags\">Close Others</li>\n      <li @click=\"closeAllTags(selectedTag)\">Close All</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    },\n    routes() {\n      return this.$store.state.permission.routes\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      routes.forEach(route => {\n        if (route.meta && route.meta.affix) {\n          const tagPath = path.resolve(basePath, route.path)\n          tags.push({\n            fullPath: tagPath,\n            path: tagPath,\n            name: route.name,\n            meta: { ...route.meta }\n          })\n        }\n        if (route.children) {\n          const tempTags = this.filterAffixTags(route.children, route.path)\n          if (tempTags.length >= 1) {\n            tags = [...tags, ...tempTags]\n          }\n        }\n      })\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      if (name) {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n      return false\n    },\n    moveToCurrentTag() {\n      const tags = this.$refs.tag\n      this.$nextTick(() => {\n        for (const tag of tags) {\n          if (tag.to.path === this.$route.path) {\n            this.$refs.scrollPane.moveToTarget(tag)\n            // when query is different then update\n            if (tag.to.fullPath !== this.$route.fullPath) {\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n            }\n            break\n          }\n        }\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\n        const { fullPath } = view\n        this.$nextTick(() => {\n          this.$router.replace({\n            path: '/redirect' + fullPath\n          })\n        })\n      })\n    },\n    closeSelectedTag(view) {\n      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag)\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.path === view.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === 'Dashboard') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-view-container {\n  height: 34px;\n  width: 100%;\n  background: #fff;\n  border-bottom: 1px solid #d8dce5;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 26px;\n      line-height: 26px;\n      border: 1px solid #d8dce5;\n      color: #495060;\n      background: #fff;\n      padding: 0 8px;\n      font-size: 12px;\n      margin-left: 5px;\n      margin-top: 4px;\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n      &.active {\n        background-color: #42b983;\n        color: #fff;\n        border-color: #42b983;\n        &::before {\n          content: '';\n          background: #fff;\n          display: inline-block;\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 2px;\n        }\n      }\n    }\n  }\n  .contextmenu {\n    margin: 0;\n    background: #fff;\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 5px 0;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: 400;\n    color: #333;\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\n    li {\n      margin: 0;\n      padding: 7px 16px;\n      cursor: pointer;\n      &:hover {\n        background: #eee;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\n      transform-origin: 100% 50%;\n      &:before {\n        transform: scale(.6);\n        display: inline-block;\n        vertical-align: -3px;\n      }\n      &:hover {\n        background-color: #b4bccc;\n        color: #fff;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AA4BA,OAAAA,UAAA;AACA,OAAAC,IAAA;AAEA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,YAAA;IACA;IACAI,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAG,UAAA,CAAAD,MAAA;IACA;EACA;EACAE,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;IACA;IACAf,OAAA,WAAAA,QAAAgB,KAAA;MACA,IAAAA,KAAA;QACAC,QAAA,CAAAC,IAAA,CAAAC,gBAAA,eAAAC,SAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAG,mBAAA,eAAAD,SAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAT,OAAA;EACA;EACAU,OAAA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MACA,OAAAA,KAAA,CAAA7B,IAAA,UAAAgB,MAAA,CAAAhB,IAAA;IACA;IACA8B,OAAA,WAAAA,QAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,IAAA,IAAAD,GAAA,CAAAC,IAAA,CAAAC,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAArB,MAAA;MAAA,IAAAsB,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAG,IAAA;MACA3B,MAAA,CAAA4B,OAAA,WAAAZ,KAAA;QACA,IAAAA,KAAA,CAAAG,IAAA,IAAAH,KAAA,CAAAG,IAAA,CAAAC,KAAA;UACA,IAAAS,OAAA,GAAA1C,IAAA,CAAA2C,OAAA,CAAAP,QAAA,EAAAP,KAAA,CAAA7B,IAAA;UACAwC,IAAA,CAAAI,IAAA;YACAC,QAAA,EAAAH,OAAA;YACA1C,IAAA,EAAA0C,OAAA;YACAI,IAAA,EAAAjB,KAAA,CAAAiB,IAAA;YACAd,IAAA,EAAAe,aAAA,KAAAlB,KAAA,CAAAG,IAAA;UACA;QACA;QACA,IAAAH,KAAA,CAAAmB,QAAA;UACA,IAAAC,QAAA,GAAAd,KAAA,CAAAD,eAAA,CAAAL,KAAA,CAAAmB,QAAA,EAAAnB,KAAA,CAAA7B,IAAA;UACA,IAAAiD,QAAA,CAAAX,MAAA;YACAE,IAAA,MAAAU,MAAA,CAAAC,kBAAA,CAAAX,IAAA,GAAAW,kBAAA,CAAAF,QAAA;UACA;QACA;MACA;MACA,OAAAT,IAAA;IACA;IACAd,QAAA,WAAAA,SAAA;MACA,IAAAnB,SAAA,QAAAA,SAAA,QAAA2B,eAAA,MAAArB,MAAA;MAAA,IAAAuC,SAAA,GAAAC,0BAAA,CACA9C,SAAA;QAAA+C,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAA1B,GAAA,GAAAuB,KAAA,CAAAnC,KAAA;UACA;UACA,IAAAY,GAAA,CAAAe,IAAA;YACA,KAAApC,MAAA,CAAAgD,QAAA,4BAAA3B,GAAA;UACA;QACA;MAAA,SAAA4B,GAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;MAAA;QAAAP,SAAA,CAAAS,CAAA;MAAA;IACA;IACA5C,OAAA,WAAAA,QAAA;MACA,IAAA6B,IAAA,QAAA9B,MAAA,CAAA8B,IAAA;MACA,IAAAA,IAAA;QACA,KAAApC,MAAA,CAAAgD,QAAA,0BAAA1C,MAAA;MACA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MAAA,IAAA4C,MAAA;MACA,IAAAtB,IAAA,QAAAuB,KAAA,CAAAhC,GAAA;MACA,KAAAiC,SAAA;QAAA,IAAAC,UAAA,GAAAZ,0BAAA,CACAb,IAAA;UAAA0B,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAV,CAAA,MAAAW,MAAA,GAAAD,UAAA,CAAAT,CAAA,IAAAC,IAAA;YAAA,IAAA1B,GAAA,GAAAmC,MAAA,CAAA/C,KAAA;YACA,IAAAY,GAAA,CAAAoC,EAAA,CAAAnE,IAAA,KAAA8D,MAAA,CAAA9C,MAAA,CAAAhB,IAAA;cACA8D,MAAA,CAAAC,KAAA,CAAAK,UAAA,CAAAC,YAAA,CAAAtC,GAAA;cACA;cACA,IAAAA,GAAA,CAAAoC,EAAA,CAAAtB,QAAA,KAAAiB,MAAA,CAAA9C,MAAA,CAAA6B,QAAA;gBACAiB,MAAA,CAAApD,MAAA,CAAAgD,QAAA,+BAAAI,MAAA,CAAA9C,MAAA;cACA;cACA;YACA;UACA;QAAA,SAAA2C,GAAA;UAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;QAAA;UAAAM,UAAA,CAAAJ,CAAA;QAAA;MACA;IACA;IACAS,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAA9D,MAAA,CAAAgD,QAAA,2BAAAa,IAAA,EAAAE,IAAA;QACA,IAAA5B,QAAA,GAAA0B,IAAA,CAAA1B,QAAA;QACA2B,MAAA,CAAAR,SAAA;UACAQ,MAAA,CAAAE,OAAA,CAAAC,OAAA;YACA3E,IAAA,gBAAA6C;UACA;QACA;MACA;IACA;IACA+B,gBAAA,WAAAA,iBAAAL,IAAA;MAAA,IAAAM,MAAA;MACA,KAAAnE,MAAA,CAAAgD,QAAA,qBAAAa,IAAA,EAAAE,IAAA,WAAAK,IAAA;QAAA,IAAArE,YAAA,GAAAqE,IAAA,CAAArE,YAAA;QACA,IAAAoE,MAAA,CAAAjD,QAAA,CAAA2C,IAAA;UACAM,MAAA,CAAAE,UAAA,CAAAtE,YAAA,EAAA8D,IAAA;QACA;MACA;IACA;IACAS,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAP,OAAA,CAAA9B,IAAA,MAAAtC,WAAA;MACA,KAAAI,MAAA,CAAAgD,QAAA,iCAAApD,WAAA,EAAAmE,IAAA;QACAQ,MAAA,CAAA/D,gBAAA;MACA;IACA;IACAgE,YAAA,WAAAA,aAAAX,IAAA;MAAA,IAAAY,MAAA;MACA,KAAAzE,MAAA,CAAAgD,QAAA,yBAAAe,IAAA,WAAAW,KAAA;QAAA,IAAA3E,YAAA,GAAA2E,KAAA,CAAA3E,YAAA;QACA,IAAA0E,MAAA,CAAA5E,SAAA,CAAA8E,IAAA,WAAAtD,GAAA;UAAA,OAAAA,GAAA,CAAA/B,IAAA,KAAAuE,IAAA,CAAAvE,IAAA;QAAA;UACA;QACA;QACAmF,MAAA,CAAAJ,UAAA,CAAAtE,YAAA,EAAA8D,IAAA;MACA;IACA;IACAQ,UAAA,WAAAA,WAAAtE,YAAA,EAAA8D,IAAA;MACA,IAAAe,UAAA,GAAA7E,YAAA,CAAA8E,KAAA;MACA,IAAAD,UAAA;QACA,KAAAZ,OAAA,CAAA9B,IAAA,CAAA0C,UAAA,CAAAzC,QAAA;MACA;QACA;QACA;QACA,IAAA0B,IAAA,CAAAzB,IAAA;UACA;UACA,KAAA4B,OAAA,CAAAC,OAAA;YAAA3E,IAAA,gBAAAuE,IAAA,CAAA1B;UAAA;QACA;UACA,KAAA6B,OAAA,CAAA9B,IAAA;QACA;MACA;IACA;IACA4C,QAAA,WAAAA,SAAAzD,GAAA,EAAA6B,CAAA;MACA,IAAA6B,YAAA;MACA,IAAAC,UAAA,QAAAC,GAAA,CAAAC,qBAAA,GAAAvF,IAAA;MACA,IAAAwF,WAAA,QAAAF,GAAA,CAAAE,WAAA;MACA,IAAAC,OAAA,GAAAD,WAAA,GAAAJ,YAAA;MACA,IAAApF,IAAA,GAAAuD,CAAA,CAAAmC,OAAA,GAAAL,UAAA;;MAEA,IAAArF,IAAA,GAAAyF,OAAA;QACA,KAAAzF,IAAA,GAAAyF,OAAA;MACA;QACA,KAAAzF,IAAA,GAAAA,IAAA;MACA;MAEA,KAAAD,GAAA,GAAAwD,CAAA,CAAAoC,OAAA;MACA,KAAA7F,OAAA;MACA,KAAAG,WAAA,GAAAyB,GAAA;IACA;IACAR,SAAA,WAAAA,UAAA;MACA,KAAApB,OAAA;IACA;IACA8F,YAAA,WAAAA,aAAA;MACA,KAAA1E,SAAA;IACA;EACA;AACA", "ignoreList": []}]}