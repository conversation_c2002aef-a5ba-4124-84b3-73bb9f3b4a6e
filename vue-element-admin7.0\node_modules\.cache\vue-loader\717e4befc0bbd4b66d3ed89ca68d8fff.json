{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTWVudUl0ZW0nLAogIGZ1bmN0aW9uYWw6IHRydWUsCiAgcHJvcHM6IHsKICAgIGljb246IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHRpdGxlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIHJlbmRlcihoLCBjb250ZXh0KSB7CiAgICBjb25zdCB7IGljb24sIHRpdGxlIH0gPSBjb250ZXh0LnByb3BzCiAgICBjb25zdCB2bm9kZXMgPSBbXQoKICAgIGlmIChpY29uKSB7CiAgICAgIGlmIChpY29uLmluY2x1ZGVzKCdlbC1pY29uJykpIHsKICAgICAgICB2bm9kZXMucHVzaCg8aSBjbGFzcz17W2ljb24sICdzdWItZWwtaWNvbiddfSAvPikKICAgICAgfSBlbHNlIHsKICAgICAgICB2bm9kZXMucHVzaCg8c3ZnLWljb24gaWNvbi1jbGFzcz17aWNvbn0vPikKICAgICAgfQogICAgfQoKICAgIGlmICh0aXRsZSkgewogICAgICB2bm9kZXMucHVzaCg8c3BhbiBzbG90PSd0aXRsZSc+eyh0aXRsZSl9PC9zcGFuPikKICAgIH0KICAgIHJldHVybiB2bm9kZXMKICB9Cn0K"}, {"version": 3, "sources": ["Item.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Item.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<script>\nexport default {\n  name: 'MenuItem',\n  functional: true,\n  props: {\n    icon: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  render(h, context) {\n    const { icon, title } = context.props\n    const vnodes = []\n\n    if (icon) {\n      if (icon.includes('el-icon')) {\n        vnodes.push(<i class={[icon, 'sub-el-icon']} />)\n      } else {\n        vnodes.push(<svg-icon icon-class={icon}/>)\n      }\n    }\n\n    if (title) {\n      vnodes.push(<span slot='title'>{(title)}</span>)\n    }\n    return vnodes\n  }\n}\n</script>\n\n<style scoped>\n.sub-el-icon {\n  color: currentColor;\n  width: 1em;\n  height: 1em;\n}\n</style>\n"]}]}