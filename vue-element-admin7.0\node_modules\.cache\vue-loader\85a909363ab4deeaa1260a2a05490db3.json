{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\components\\SwitchRoles.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\components\\SwitchRoles.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wdXRlZDogewogICAgcm9sZXMoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzCiAgICB9LAogICAgc3dpdGNoUm9sZXM6IHsKICAgICAgZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLnJvbGVzWzBdCiAgICAgIH0sCiAgICAgIHNldCh2YWwpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndXNlci9jaGFuZ2VSb2xlcycsIHZhbCkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRlbWl0KCdjaGFuZ2UnKQogICAgICAgIH0pCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["SwitchRoles.vue"], "names": [], "mappings": ";AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SwitchRoles.vue", "sourceRoot": "src/views/permission/components", "sourcesContent": ["<template>\n  <div>\n    <div style=\"margin-bottom:15px;\">\n      Your roles: {{ roles }}\n    </div>\n    Switch roles:\n    <el-radio-group v-model=\"switchRoles\">\n      <el-radio-button label=\"editor\" />\n      <el-radio-button label=\"admin\" />\n    </el-radio-group>\n  </div>\n</template>\n\n<script>\nexport default {\n  computed: {\n    roles() {\n      return this.$store.getters.roles\n    },\n    switchRoles: {\n      get() {\n        return this.roles[0]\n      },\n      set(val) {\n        this.$store.dispatch('user/changeRoles', val).then(() => {\n          this.$emit('change')\n        })\n      }\n    }\n  }\n}\n</script>\n"]}]}