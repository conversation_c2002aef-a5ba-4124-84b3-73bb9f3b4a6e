{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\index.vue", "mtime": 1747748935260}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IHZhbGlkVXNlcm5hbWUgfSBmcm9tICdAL3V0aWxzL3ZhbGlkYXRlJwppbXBvcnQgU29jaWFsU2lnbiBmcm9tICcuL2NvbXBvbmVudHMvU29jaWFsU2lnbmluJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdMb2dpbicsCiAgY29tcG9uZW50czogeyBTb2NpYWxTaWduIH0sCiAgZGF0YSgpIHsKICAgIGNvbnN0IHZhbGlkYXRlVXNlcm5hbWUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICghdmFsaWRVc2VybmFtZSh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ1BsZWFzZSBlbnRlciB0aGUgY29ycmVjdCB1c2VyIG5hbWUnKSkKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpCiAgICAgIH0KICAgIH0KICAgIGNvbnN0IHZhbGlkYXRlUGFzc3dvcmQgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICh2YWx1ZS5sZW5ndGggPCA2KSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCdUaGUgcGFzc3dvcmQgY2FuIG5vdCBiZSBsZXNzIHRoYW4gNiBkaWdpdHMnKSkKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpCiAgICAgIH0KICAgIH0KICAgIHJldHVybiB7CiAgICAgIGxvZ2luRm9ybTogewogICAgICAgIHVzZXJuYW1lOiAnZWRpdG9yJywKICAgICAgICBwYXNzd29yZDogJzExMTExMScKICAgICAgfSwKICAgICAgbG9naW5SdWxlczogewogICAgICAgIHVzZXJuYW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogJ2JsdXInLCB2YWxpZGF0b3I6IHZhbGlkYXRlVXNlcm5hbWUgfV0sCiAgICAgICAgcGFzc3dvcmQ6IFt7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAnYmx1cicsIHZhbGlkYXRvcjogdmFsaWRhdGVQYXNzd29yZCB9XQogICAgICB9LAogICAgICBwYXNzd29yZFR5cGU6ICdwYXNzd29yZCcsCiAgICAgIGNhcHNUb29sdGlwOiBmYWxzZSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNob3dEaWFsb2c6IGZhbHNlLAogICAgICByZWRpcmVjdDogdW5kZWZpbmVkLAogICAgICBvdGhlclF1ZXJ5OiB7fQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbihyb3V0ZSkgewogICAgICAgIGNvbnN0IHF1ZXJ5ID0gcm91dGUucXVlcnkKICAgICAgICBpZiAocXVlcnkpIHsKICAgICAgICAgIHRoaXMucmVkaXJlY3QgPSBxdWVyeS5yZWRpcmVjdAogICAgICAgICAgdGhpcy5vdGhlclF1ZXJ5ID0gdGhpcy5nZXRPdGhlclF1ZXJ5KHF1ZXJ5KQogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgLy8gd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3N0b3JhZ2UnLCB0aGlzLmFmdGVyUVJTY2FuKQogIH0sCiAgbW91bnRlZCgpIHsKICAgIGlmICh0aGlzLmxvZ2luRm9ybS51c2VybmFtZSA9PT0gJycpIHsKICAgICAgdGhpcy4kcmVmcy51c2VybmFtZS5mb2N1cygpCiAgICB9IGVsc2UgaWYgKHRoaXMubG9naW5Gb3JtLnBhc3N3b3JkID09PSAnJykgewogICAgICB0aGlzLiRyZWZzLnBhc3N3b3JkLmZvY3VzKCkKICAgIH0KICB9LAogIGRlc3Ryb3llZCgpIHsKICAgIC8vIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzdG9yYWdlJywgdGhpcy5hZnRlclFSU2NhbikKICB9LAogIG1ldGhvZHM6IHsKICAgIGNoZWNrQ2Fwc2xvY2soZSkgewogICAgICBjb25zdCB7IGtleSB9ID0gZQogICAgICB0aGlzLmNhcHNUb29sdGlwID0ga2V5ICYmIGtleS5sZW5ndGggPT09IDEgJiYgKGtleSA+PSAnQScgJiYga2V5IDw9ICdaJykKICAgIH0sCiAgICBzaG93UHdkKCkgewogICAgICBpZiAodGhpcy5wYXNzd29yZFR5cGUgPT09ICdwYXNzd29yZCcpIHsKICAgICAgICB0aGlzLnBhc3N3b3JkVHlwZSA9ICcnCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5wYXNzd29yZFR5cGUgPSAncGFzc3dvcmQnCiAgICAgIH0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnMucGFzc3dvcmQuZm9jdXMoKQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZUxvZ2luKCkgewogICAgICB0aGlzLiRyZWZzLmxvZ2luRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndXNlci9sb2dpbicsIHRoaXMubG9naW5Gb3JtKQogICAgICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiB0aGlzLnJlZGlyZWN0IHx8ICcvJywgcXVlcnk6IHRoaXMub3RoZXJRdWVyeSB9KQogICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5sb2coJ2Vycm9yIHN1Ym1pdCEhJykKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBnZXRPdGhlclF1ZXJ5KHF1ZXJ5KSB7CiAgICAgIHJldHVybiBPYmplY3Qua2V5cyhxdWVyeSkucmVkdWNlKChhY2MsIGN1cikgPT4gewogICAgICAgIGlmIChjdXIgIT09ICdyZWRpcmVjdCcpIHsKICAgICAgICAgIGFjY1tjdXJdID0gcXVlcnlbY3VyXQogICAgICAgIH0KICAgICAgICByZXR1cm4gYWNjCiAgICAgIH0sIHt9KQogICAgfQogICAgLy8gYWZ0ZXJRUlNjYW4oKSB7CiAgICAvLyAgIGlmIChlLmtleSA9PT0gJ3gtYWRtaW4tb2F1dGgtY29kZScpIHsKICAgIC8vICAgICBjb25zdCBjb2RlID0gZ2V0UXVlcnlPYmplY3QoZS5uZXdWYWx1ZSkKICAgIC8vICAgICBjb25zdCBjb2RlTWFwID0gewogICAgLy8gICAgICAgd2VjaGF0OiAnY29kZScsCiAgICAvLyAgICAgICB0ZW5jZW50OiAnY29kZScKICAgIC8vICAgICB9CiAgICAvLyAgICAgY29uc3QgdHlwZSA9IGNvZGVNYXBbdGhpcy5hdXRoX3R5cGVdCiAgICAvLyAgICAgY29uc3QgY29kZU5hbWUgPSBjb2RlW3R5cGVdCiAgICAvLyAgICAgaWYgKGNvZGVOYW1lKSB7CiAgICAvLyAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnTG9naW5CeVRoaXJkcGFydHknLCBjb2RlTmFtZSkudGhlbigoKSA9PiB7CiAgICAvLyAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogdGhpcy5yZWRpcmVjdCB8fCAnLycgfSkKICAgIC8vICAgICAgIH0pCiAgICAvLyAgICAgfSBlbHNlIHsKICAgIC8vICAgICAgIGFsZXJ0KCfnrKzkuInmlrnnmbvlvZXlpLHotKUnKQogICAgLy8gICAgIH0KICAgIC8vICAgfQogICAgLy8gfQogIH0KfQoKLy8g5a+85Ye6IFVTRVIg5L2c5Li65bi46YePCmV4cG9ydCBjb25zdCBVU0VSID0gJ2VkaXRvcicK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA6EA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/views/login", "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <img src=\"../../assets/used_images/北邮logo白色-去背景.png\" class=\"small-image\" alt=\"Small Image\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" autocomplete=\"on\" label-position=\"left\">\n\n      <div class=\"title-container\">\n        <h3 class=\"title\">地下钱庄大数据智能分析系统</h3>\n      </div>\n\n      <el-form-item prop=\"username\">\n        <span class=\"svg-container\">\n          <svg-icon icon-class=\"user\" />\n        </span>\n        <el-input\n          ref=\"username\"\n          v-model=\"loginForm.username\"\n          placeholder=\"Username\"\n          name=\"username\"\n          type=\"text\"\n          tabindex=\"1\"\n          autocomplete=\"on\"\n        />\n      </el-form-item>\n\n      <el-tooltip v-model=\"capsTooltip\" content=\"Caps lock is On\" placement=\"right\" manual>\n        <el-form-item prop=\"password\">\n          <span class=\"svg-container\">\n            <svg-icon icon-class=\"password\" />\n          </span>\n          <el-input\n            :key=\"passwordType\"\n            ref=\"password\"\n            v-model=\"loginForm.password\"\n            :type=\"passwordType\"\n            placeholder=\"Password\"\n            name=\"password\"\n            tabindex=\"2\"\n            autocomplete=\"on\"\n            @keyup.native=\"checkCapslock\"\n            @blur=\"capsTooltip = false\"\n            @keyup.enter.native=\"handleLogin\"\n          />\n          <span class=\"show-pwd\" @click=\"showPwd\">\n            <svg-icon :icon-class=\"passwordType === 'password' ? 'eye' : 'eye-open'\" />\n          </span>\n        </el-form-item>\n      </el-tooltip>\n\n      <el-button :loading=\"loading\" type=\"primary\" style=\"width:100%;margin-bottom:30px;\" @click.native.prevent=\"handleLogin\">Login</el-button>\n\n      <!-- <div style=\"position:relative\">\n        <div class=\"tips\">\n          <span>Username : admin</span>\n          <span>Password : any</span>\n        </div>\n        <div class=\"tips\">\n          <span style=\"margin-right:18px;\">Username : editor</span>\n          <span>Password : any</span>\n        </div>\n\n        <el-button class=\"thirdparty-button\" type=\"primary\" @click=\"showDialog=true\">\n          Or connect with\n        </el-button>\n      </div> -->\n    </el-form>\n\n    <el-dialog title=\"Or connect with\" :visible.sync=\"showDialog\">\n      Can not be simulated on local, so please combine you own business simulation! ! !\n      <br>\n      <br>\n      <br>\n      <social-sign />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { validUsername } from '@/utils/validate'\nimport SocialSign from './components/SocialSignin'\n\nexport default {\n  name: 'Login',\n  components: { SocialSign },\n  data() {\n    const validateUsername = (rule, value, callback) => {\n      if (!validUsername(value)) {\n        callback(new Error('Please enter the correct user name'))\n      } else {\n        callback()\n      }\n    }\n    const validatePassword = (rule, value, callback) => {\n      if (value.length < 6) {\n        callback(new Error('The password can not be less than 6 digits'))\n      } else {\n        callback()\n      }\n    }\n    return {\n      loginForm: {\n        username: 'editor',\n        password: '111111'\n      },\n      loginRules: {\n        username: [{ required: true, trigger: 'blur', validator: validateUsername }],\n        password: [{ required: true, trigger: 'blur', validator: validatePassword }]\n      },\n      passwordType: 'password',\n      capsTooltip: false,\n      loading: false,\n      showDialog: false,\n      redirect: undefined,\n      otherQuery: {}\n    }\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        const query = route.query\n        if (query) {\n          this.redirect = query.redirect\n          this.otherQuery = this.getOtherQuery(query)\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    // window.addEventListener('storage', this.afterQRScan)\n  },\n  mounted() {\n    if (this.loginForm.username === '') {\n      this.$refs.username.focus()\n    } else if (this.loginForm.password === '') {\n      this.$refs.password.focus()\n    }\n  },\n  destroyed() {\n    // window.removeEventListener('storage', this.afterQRScan)\n  },\n  methods: {\n    checkCapslock(e) {\n      const { key } = e\n      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')\n    },\n    showPwd() {\n      if (this.passwordType === 'password') {\n        this.passwordType = ''\n      } else {\n        this.passwordType = 'password'\n      }\n      this.$nextTick(() => {\n        this.$refs.password.focus()\n      })\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          this.$store.dispatch('user/login', this.loginForm)\n            .then(() => {\n              this.$router.push({ path: this.redirect || '/', query: this.otherQuery })\n              this.loading = false\n            })\n            .catch(() => {\n              this.loading = false\n            })\n        } else {\n          console.log('error submit!!')\n          return false\n        }\n      })\n    },\n    getOtherQuery(query) {\n      return Object.keys(query).reduce((acc, cur) => {\n        if (cur !== 'redirect') {\n          acc[cur] = query[cur]\n        }\n        return acc\n      }, {})\n    }\n    // afterQRScan() {\n    //   if (e.key === 'x-admin-oauth-code') {\n    //     const code = getQueryObject(e.newValue)\n    //     const codeMap = {\n    //       wechat: 'code',\n    //       tencent: 'code'\n    //     }\n    //     const type = codeMap[this.auth_type]\n    //     const codeName = code[type]\n    //     if (codeName) {\n    //       this.$store.dispatch('LoginByThirdparty', codeName).then(() => {\n    //         this.$router.push({ path: this.redirect || '/' })\n    //       })\n    //     } else {\n    //       alert('第三方登录失败')\n    //     }\n    //   }\n    // }\n  }\n}\n\n// 导出 USER 作为常量\nexport const USER = 'editor'\n</script>\n\n<style lang=\"scss\">\n/* 修复input 背景不协调 和光标变色 */\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\n\n$bg:#283443;\n$light_gray:#fff;\n$cursor: #fff;\n\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\n  .login-container .el-input input {\n    color: $cursor;\n  }\n}\n\n/* reset element-ui css */\n.login-container {\n  .el-input {\n    display: inline-block;\n    height: 47px;\n    width: 85%;\n\n    input {\n      background: transparent;\n      border: 0px;\n      -webkit-appearance: none;\n      border-radius: 0px;\n      padding: 12px 5px 12px 15px;\n      color: $light_gray;\n      height: 47px;\n      caret-color: $cursor;\n\n      &:-webkit-autofill {\n        box-shadow: 0 0 0px 1000px $bg inset !important;\n        -webkit-text-fill-color: $cursor !important;\n      }\n    }\n  }\n\n  .el-form-item {\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    background: rgba(0, 0, 0, 0.1);\n    border-radius: 5px;\n    color: #454545;\n  }\n}\n</style>\n\n<style lang=\"scss\" scoped>\n$bg:#2d3a4b;\n$dark_gray:#889aa4;\n$light_gray:#eee;\n\n.login-container {\n  background-image: url('../../assets/used_images/background1.jpg'); /* 替换为你的图片路径 */\n  background-size: cover; /* 使背景图像覆盖整个容器 */\n  background-position: center; /* 将背景图像居中 */\n  background-repeat: no-repeat; /* 不重复 */\n  height: 100vh; /* 设置容器的高度 */\n  display: flex; /* 使用 Flexbox 使内容居中 */\n  align-items: center; /* 垂直居中 */\n  justify-content: center; /* 水平居中 */\n}\n\n.small-image {\n  position: absolute; /* 绝对定位 */\n  top: 10px; /* 距离上方的距离 */\n  left: 10px; /* 距离左侧的距离 */\n  width: 130px; /* 设置宽度 */\n  height: 130px; /* 高度自适应 */\n  opacity: 1; /* 设置透明度 */\n  z-index: 10; /* 确保小图在其它元素上方 */\n  transition: transform 0.3s; /* 添加过渡效果 */\n}\n\n.login-container {\n  min-height: 100%;\n  width: 100%;\n  background-color: $bg;\n  overflow: hidden;\n\n  .login-form {\n    position: relative;\n    width: 520px;\n    max-width: 100%;\n    padding: 60px 35px 0;\n    margin: 0 auto;\n    overflow: hidden;\n  }\n\n  .tips {\n    font-size: 14px;\n    color: #fff;\n    margin-bottom: 10px;\n\n    span {\n      &:first-of-type {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .svg-container {\n    padding: 6px 5px 6px 15px;\n    color: $dark_gray;\n    vertical-align: middle;\n    width: 30px;\n    display: inline-block;\n  }\n\n  .title-container {\n    position: relative;\n\n    .title {\n      font-size: 26px;\n      color: $light_gray;\n      margin: 0px auto 40px auto;\n      text-align: center;\n      font-weight: bold;\n    }\n  }\n\n  .show-pwd {\n    position: absolute;\n    right: 10px;\n    top: 7px;\n    font-size: 16px;\n    color: $dark_gray;\n    cursor: pointer;\n    user-select: none;\n  }\n\n  .thirdparty-button {\n    position: absolute;\n    right: 0;\n    bottom: 6px;\n  }\n\n  @media only screen and (max-width: 470px) {\n    .thirdparty-button {\n      display: none;\n    }\n  }\n}\n</style>\n"]}]}