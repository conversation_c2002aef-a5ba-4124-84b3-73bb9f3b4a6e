{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\permission.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\permission.js", "mtime": 1731832578000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["asyncRoutes", "constantRoutes", "hasPermission", "roles", "route", "meta", "some", "role", "includes", "filterAsyncRoutes", "routes", "res", "for<PERSON>ach", "tmp", "_objectSpread", "children", "push", "state", "addRoutes", "mutations", "SET_ROUTES", "concat", "actions", "generateRoutes", "_ref", "commit", "Promise", "resolve", "accessedRoutes", "namespaced"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/store/modules/permission.js"], "sourcesContent": ["import { asyncRoutes, constantRoutes } from '@/router'\n\n/**\n * Use meta.role to determine if the current user has permission\n * @param roles\n * @param route\n */\nfunction hasPermission(roles, route) {\n  if (route.meta && route.meta.roles) {\n    return roles.some(role => route.meta.roles.includes(role))\n  } else {\n    return true\n  }\n}\n\n/**\n * Filter asynchronous routing tables by recursion\n * @param routes asyncRoutes\n * @param roles\n */\nexport function filterAsyncRoutes(routes, roles) {\n  const res = []\n\n  routes.forEach(route => {\n    const tmp = { ...route }\n    if (hasPermission(roles, tmp)) {\n      if (tmp.children) {\n        tmp.children = filterAsyncRoutes(tmp.children, roles)\n      }\n      res.push(tmp)\n    }\n  })\n\n  return res\n}\n\nconst state = {\n  routes: [],\n  addRoutes: []\n}\n\nconst mutations = {\n  SET_ROUTES: (state, routes) => {\n    state.addRoutes = routes\n    state.routes = constantRoutes.concat(routes)\n  }\n}\n\nconst actions = {\n  generateRoutes({ commit }, roles) {\n    return new Promise(resolve => {\n      let accessedRoutes\n      if (roles.includes('admin')) {\n        accessedRoutes = asyncRoutes || []\n        // accessedRoutes = []\n      } else {\n        accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)\n      }\n      commit('SET_ROUTES', accessedRoutes)\n      resolve(accessedRoutes)\n    })\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";;;;;;;;;AAAA,SAASA,WAAW,EAAEC,cAAc,QAAQ,UAAU;;AAEtD;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACF,KAAK,EAAE;IAClC,OAAOA,KAAK,CAACG,IAAI,CAAC,UAAAC,IAAI;MAAA,OAAIH,KAAK,CAACC,IAAI,CAACF,KAAK,CAACK,QAAQ,CAACD,IAAI,CAAC;IAAA,EAAC;EAC5D,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,iBAAiBA,CAACC,MAAM,EAAEP,KAAK,EAAE;EAC/C,IAAMQ,GAAG,GAAG,EAAE;EAEdD,MAAM,CAACE,OAAO,CAAC,UAAAR,KAAK,EAAI;IACtB,IAAMS,GAAG,GAAAC,aAAA,KAAQV,KAAK,CAAE;IACxB,IAAIF,aAAa,CAACC,KAAK,EAAEU,GAAG,CAAC,EAAE;MAC7B,IAAIA,GAAG,CAACE,QAAQ,EAAE;QAChBF,GAAG,CAACE,QAAQ,GAAGN,iBAAiB,CAACI,GAAG,CAACE,QAAQ,EAAEZ,KAAK,CAAC;MACvD;MACAQ,GAAG,CAACK,IAAI,CAACH,GAAG,CAAC;IACf;EACF,CAAC,CAAC;EAEF,OAAOF,GAAG;AACZ;AAEA,IAAMM,KAAK,GAAG;EACZP,MAAM,EAAE,EAAE;EACVQ,SAAS,EAAE;AACb,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE,SAAZA,UAAUA,CAAGH,KAAK,EAAEP,MAAM,EAAK;IAC7BO,KAAK,CAACC,SAAS,GAAGR,MAAM;IACxBO,KAAK,CAACP,MAAM,GAAGT,cAAc,CAACoB,MAAM,CAACX,MAAM,CAAC;EAC9C;AACF,CAAC;AAED,IAAMY,OAAO,GAAG;EACdC,cAAc,WAAdA,cAAcA,CAAAC,IAAA,EAAarB,KAAK,EAAE;IAAA,IAAjBsB,MAAM,GAAAD,IAAA,CAANC,MAAM;IACrB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5B,IAAIC,cAAc;MAClB,IAAIzB,KAAK,CAACK,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC3BoB,cAAc,GAAG5B,WAAW,IAAI,EAAE;QAClC;MACF,CAAC,MAAM;QACL4B,cAAc,GAAGnB,iBAAiB,CAACT,WAAW,EAAEG,KAAK,CAAC;MACxD;MACAsB,MAAM,CAAC,YAAY,EAAEG,cAAc,CAAC;MACpCD,OAAO,CAACC,cAAc,CAAC;IACzB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe;EACbC,UAAU,EAAE,IAAI;EAChBZ,KAAK,EAALA,KAAK;EACLE,SAAS,EAATA,SAAS;EACTG,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}