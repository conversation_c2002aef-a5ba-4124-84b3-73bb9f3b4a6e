{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\errorLog.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\errorLog.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIjsKdmFyIHN0YXRlID0gewogIGxvZ3M6IFtdCn07CnZhciBtdXRhdGlvbnMgPSB7CiAgQUREX0VSUk9SX0xPRzogZnVuY3Rpb24gQUREX0VSUk9SX0xPRyhzdGF0ZSwgbG9nKSB7CiAgICBzdGF0ZS5sb2dzLnB1c2gobG9nKTsKICB9LAogIENMRUFSX0VSUk9SX0xPRzogZnVuY3Rpb24gQ0xFQVJfRVJST1JfTE9HKHN0YXRlKSB7CiAgICBzdGF0ZS5sb2dzLnNwbGljZSgwKTsKICB9Cn07CnZhciBhY3Rpb25zID0gewogIGFkZEVycm9yTG9nOiBmdW5jdGlvbiBhZGRFcnJvckxvZyhfcmVmLCBsb2cpIHsKICAgIHZhciBjb21taXQgPSBfcmVmLmNvbW1pdDsKICAgIGNvbW1pdCgnQUREX0VSUk9SX0xPRycsIGxvZyk7CiAgfSwKICBjbGVhckVycm9yTG9nOiBmdW5jdGlvbiBjbGVhckVycm9yTG9nKF9yZWYyKSB7CiAgICB2YXIgY29tbWl0ID0gX3JlZjIuY29tbWl0OwogICAgY29tbWl0KCdDTEVBUl9FUlJPUl9MT0cnKTsKICB9Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lc3BhY2VkOiB0cnVlLAogIHN0YXRlOiBzdGF0ZSwKICBtdXRhdGlvbnM6IG11dGF0aW9ucywKICBhY3Rpb25zOiBhY3Rpb25zCn07"}, {"version": 3, "names": ["state", "logs", "mutations", "ADD_ERROR_LOG", "log", "push", "CLEAR_ERROR_LOG", "splice", "actions", "addErrorLog", "_ref", "commit", "clearErrorLog", "_ref2", "namespaced"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/store/modules/errorLog.js"], "sourcesContent": ["const state = {\n  logs: []\n}\n\nconst mutations = {\n  ADD_ERROR_LOG: (state, log) => {\n    state.logs.push(log)\n  },\n  CLEAR_ERROR_LOG: (state) => {\n    state.logs.splice(0)\n  }\n}\n\nconst actions = {\n  addErrorLog({ commit }, log) {\n    commit('ADD_ERROR_LOG', log)\n  },\n  clearErrorLog({ commit }) {\n    commit('CLEAR_ERROR_LOG')\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";AAAA,IAAMA,KAAK,GAAG;EACZC,IAAI,EAAE;AACR,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,aAAa,EAAE,SAAfA,aAAaA,CAAGH,KAAK,EAAEI,GAAG,EAAK;IAC7BJ,KAAK,CAACC,IAAI,CAACI,IAAI,CAACD,GAAG,CAAC;EACtB,CAAC;EACDE,eAAe,EAAE,SAAjBA,eAAeA,CAAGN,KAAK,EAAK;IAC1BA,KAAK,CAACC,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;EACtB;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACdC,WAAW,WAAXA,WAAWA,CAAAC,IAAA,EAAaN,GAAG,EAAE;IAAA,IAAfO,MAAM,GAAAD,IAAA,CAANC,MAAM;IAClBA,MAAM,CAAC,eAAe,EAAEP,GAAG,CAAC;EAC9B,CAAC;EACDQ,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAa;IAAA,IAAVF,MAAM,GAAAE,KAAA,CAANF,MAAM;IACpBA,MAAM,CAAC,iBAAiB,CAAC;EAC3B;AACF,CAAC;AAED,eAAe;EACbG,UAAU,EAAE,IAAI;EAChBd,KAAK,EAALA,KAAK;EACLE,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}