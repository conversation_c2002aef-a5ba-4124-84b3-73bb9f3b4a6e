{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\editor\\index.vue", "mtime": 1731833050000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovMjAyNVx1NTkyN1x1NTIxQl9cdTU3MzBcdTRFMEJcdTc1MzBcdTVFODQvdnVlLWVsZW1lbnQtYWRtaW43LjAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBQYW5UaHVtYiBmcm9tICdAL2NvbXBvbmVudHMvUGFuVGh1bWInOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Rhc2hib2FyZEVkaXRvcicsCiAgY29tcG9uZW50czogewogICAgUGFuVGh1bWI6IFBhblRodW1iCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZW1wdHlHaWY6ICdodHRwczovL3dwaW1nLndhbGxzdGNuLmNvbS8wZTAzYjdkYS1kYjllLTQ4MTktYmExMC05MDE2ZGRmZGFlZDMnCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoe30sIG1hcEdldHRlcnMoWyduYW1lJywgJ2F2YXRhcicsICdyb2xlcyddKSkKfTs="}, {"version": 3, "names": ["mapGetters", "PanThumb", "name", "components", "data", "emptyGif", "computed", "_objectSpread"], "sources": ["src/views/dashboard/editor/index.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-editor-container\">\n    <div class=\" clearfix\">\n      <p>哈哈哈哈</p>\n      <pan-thumb :image=\"avatar\" style=\"float: left\">\n        Your roles:\n        <span v-for=\"item in roles\" :key=\"item\" class=\"pan-info-roles\">{{ item }}</span>\n      </pan-thumb>\n      <div class=\"info-container\">\n        <span class=\"display_name\">{{ name }}</span>\n        <span style=\"font-size:20px;padding-top:20px;display:inline-block;\">Editor's Dashboard</span>\n      </div>\n    </div>\n    <div>\n      <img :src=\"emptyGif\" class=\"emptyGif\">\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport PanThumb from '@/components/PanThumb'\n\nexport default {\n  name: 'DashboardEditor',\n  components: { PanThumb },\n  data() {\n    return {\n      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3'\n    }\n  },\n  computed: {\n    ...mapGetters([\n      'name',\n      'avatar',\n      'roles'\n    ])\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .emptyGif {\n    display: block;\n    width: 45%;\n    margin: 0 auto;\n  }\n\n  .dashboard-editor-container {\n    background-color: #e3e3e3;\n    min-height: 100vh;\n    padding: 50px 60px 0px;\n    .pan-info-roles {\n      font-size: 12px;\n      font-weight: 700;\n      color: #333;\n      display: block;\n    }\n    .info-container {\n      position: relative;\n      margin-left: 190px;\n      height: 150px;\n      line-height: 200px;\n      .display_name {\n        font-size: 48px;\n        line-height: 48px;\n        color: #212121;\n        position: absolute;\n        top: 25px;\n      }\n    }\n  }\n</style>\n"], "mappings": ";AAoBA,SAAAA,UAAA;AACA,OAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,QAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,KACAP,UAAA,EACA,QACA,UACA,QACA;AAEA", "ignoreList": []}]}