{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\knowledgeGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\knowledgeGraph.vue", "mtime": 1747748935261}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IENoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9DaGFydHMvS25vd2xlZGdlR3JhcGgnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0tub3dsZWRnZUdyYXBoJywKICBjb21wb25lbnRzOiB7CiAgICBDaGFydDogQ2hhcnQKICB9Cn07"}, {"version": 3, "names": ["Chart", "name", "components"], "sources": ["src/views/charts/knowledgeGraph.vue"], "sourcesContent": ["<template>\r\n    <div class=\"chart-container\">\r\n      <chart height=\"100%\" width=\"100%\" />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport Chart from '@/components/Charts/KnowledgeGraph'\r\n\r\nexport default {\r\n  name: 'KnowledgeGraph',\r\n  components: { Chart }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chart-container{\r\n  position: relative;\r\n  width: 100%;\r\n  height: calc(100vh - 84px);\r\n}\r\n</style>\n"], "mappings": "AAOA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,KAAA,EAAAA;EAAA;AACA", "ignoreList": []}]}