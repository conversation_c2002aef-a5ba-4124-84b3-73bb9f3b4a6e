{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\RateGraph.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\charts\\RateGraph.vue", "mtime": 1747748935261}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgQ2hhcnQgZnJvbSAnQC9jb21wb25lbnRzL0NoYXJ0cy9SYXRlR3JhcGgnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1JhdGVHcmFwaCcsDQogIGNvbXBvbmVudHM6IHsgQ2hhcnQgfQ0KfQ0K"}, {"version": 3, "sources": ["RateGraph.vue"], "names": [], "mappings": ";AAOA;;AAEA;AACA;AACA;AACA", "file": "RateGraph.vue", "sourceRoot": "src/views/charts", "sourcesContent": ["<template>\r\n    <div class=\"chart-container\">\r\n      <chart height=\"100%\" width=\"100%\" />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport Chart from '@/components/Charts/RateGraph'\r\n\r\nexport default {\r\n  name: 'RateGraph',\r\n  components: { Chart }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chart-container{\r\n  position: relative;\r\n  width: 100%;\r\n  height: calc(100vh - 84px);\r\n}\r\n</style>\n"]}]}