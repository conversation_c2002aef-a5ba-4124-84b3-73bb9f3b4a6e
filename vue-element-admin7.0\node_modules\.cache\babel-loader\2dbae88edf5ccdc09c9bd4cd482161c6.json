{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\index.vue", "mtime": 1747748935260}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["validUsername", "SocialSign", "name", "components", "data", "validateUsername", "rule", "value", "callback", "Error", "validatePassword", "length", "loginForm", "username", "password", "loginRules", "required", "trigger", "validator", "passwordType", "capsTooltip", "loading", "showDialog", "redirect", "undefined", "other<PERSON><PERSON>y", "watch", "$route", "handler", "route", "query", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immediate", "created", "mounted", "$refs", "focus", "destroyed", "methods", "checkCapslock", "e", "key", "showPwd", "_this", "$nextTick", "handleLogin", "_this2", "validate", "valid", "$store", "dispatch", "then", "$router", "push", "path", "catch", "console", "log", "Object", "keys", "reduce", "acc", "cur", "USER"], "sources": ["src/views/login/index.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <img src=\"../../assets/used_images/北邮logo白色-去背景.png\" class=\"small-image\" alt=\"Small Image\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" autocomplete=\"on\" label-position=\"left\">\n\n      <div class=\"title-container\">\n        <h3 class=\"title\">地下钱庄大数据智能分析系统</h3>\n      </div>\n\n      <el-form-item prop=\"username\">\n        <span class=\"svg-container\">\n          <svg-icon icon-class=\"user\" />\n        </span>\n        <el-input\n          ref=\"username\"\n          v-model=\"loginForm.username\"\n          placeholder=\"Username\"\n          name=\"username\"\n          type=\"text\"\n          tabindex=\"1\"\n          autocomplete=\"on\"\n        />\n      </el-form-item>\n\n      <el-tooltip v-model=\"capsTooltip\" content=\"Caps lock is On\" placement=\"right\" manual>\n        <el-form-item prop=\"password\">\n          <span class=\"svg-container\">\n            <svg-icon icon-class=\"password\" />\n          </span>\n          <el-input\n            :key=\"passwordType\"\n            ref=\"password\"\n            v-model=\"loginForm.password\"\n            :type=\"passwordType\"\n            placeholder=\"Password\"\n            name=\"password\"\n            tabindex=\"2\"\n            autocomplete=\"on\"\n            @keyup.native=\"checkCapslock\"\n            @blur=\"capsTooltip = false\"\n            @keyup.enter.native=\"handleLogin\"\n          />\n          <span class=\"show-pwd\" @click=\"showPwd\">\n            <svg-icon :icon-class=\"passwordType === 'password' ? 'eye' : 'eye-open'\" />\n          </span>\n        </el-form-item>\n      </el-tooltip>\n\n      <el-button :loading=\"loading\" type=\"primary\" style=\"width:100%;margin-bottom:30px;\" @click.native.prevent=\"handleLogin\">Login</el-button>\n\n      <!-- <div style=\"position:relative\">\n        <div class=\"tips\">\n          <span>Username : admin</span>\n          <span>Password : any</span>\n        </div>\n        <div class=\"tips\">\n          <span style=\"margin-right:18px;\">Username : editor</span>\n          <span>Password : any</span>\n        </div>\n\n        <el-button class=\"thirdparty-button\" type=\"primary\" @click=\"showDialog=true\">\n          Or connect with\n        </el-button>\n      </div> -->\n    </el-form>\n\n    <el-dialog title=\"Or connect with\" :visible.sync=\"showDialog\">\n      Can not be simulated on local, so please combine you own business simulation! ! !\n      <br>\n      <br>\n      <br>\n      <social-sign />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { validUsername } from '@/utils/validate'\nimport SocialSign from './components/SocialSignin'\n\nexport default {\n  name: 'Login',\n  components: { SocialSign },\n  data() {\n    const validateUsername = (rule, value, callback) => {\n      if (!validUsername(value)) {\n        callback(new Error('Please enter the correct user name'))\n      } else {\n        callback()\n      }\n    }\n    const validatePassword = (rule, value, callback) => {\n      if (value.length < 6) {\n        callback(new Error('The password can not be less than 6 digits'))\n      } else {\n        callback()\n      }\n    }\n    return {\n      loginForm: {\n        username: 'editor',\n        password: '111111'\n      },\n      loginRules: {\n        username: [{ required: true, trigger: 'blur', validator: validateUsername }],\n        password: [{ required: true, trigger: 'blur', validator: validatePassword }]\n      },\n      passwordType: 'password',\n      capsTooltip: false,\n      loading: false,\n      showDialog: false,\n      redirect: undefined,\n      otherQuery: {}\n    }\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        const query = route.query\n        if (query) {\n          this.redirect = query.redirect\n          this.otherQuery = this.getOtherQuery(query)\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    // window.addEventListener('storage', this.afterQRScan)\n  },\n  mounted() {\n    if (this.loginForm.username === '') {\n      this.$refs.username.focus()\n    } else if (this.loginForm.password === '') {\n      this.$refs.password.focus()\n    }\n  },\n  destroyed() {\n    // window.removeEventListener('storage', this.afterQRScan)\n  },\n  methods: {\n    checkCapslock(e) {\n      const { key } = e\n      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')\n    },\n    showPwd() {\n      if (this.passwordType === 'password') {\n        this.passwordType = ''\n      } else {\n        this.passwordType = 'password'\n      }\n      this.$nextTick(() => {\n        this.$refs.password.focus()\n      })\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          this.$store.dispatch('user/login', this.loginForm)\n            .then(() => {\n              this.$router.push({ path: this.redirect || '/', query: this.otherQuery })\n              this.loading = false\n            })\n            .catch(() => {\n              this.loading = false\n            })\n        } else {\n          console.log('error submit!!')\n          return false\n        }\n      })\n    },\n    getOtherQuery(query) {\n      return Object.keys(query).reduce((acc, cur) => {\n        if (cur !== 'redirect') {\n          acc[cur] = query[cur]\n        }\n        return acc\n      }, {})\n    }\n    // afterQRScan() {\n    //   if (e.key === 'x-admin-oauth-code') {\n    //     const code = getQueryObject(e.newValue)\n    //     const codeMap = {\n    //       wechat: 'code',\n    //       tencent: 'code'\n    //     }\n    //     const type = codeMap[this.auth_type]\n    //     const codeName = code[type]\n    //     if (codeName) {\n    //       this.$store.dispatch('LoginByThirdparty', codeName).then(() => {\n    //         this.$router.push({ path: this.redirect || '/' })\n    //       })\n    //     } else {\n    //       alert('第三方登录失败')\n    //     }\n    //   }\n    // }\n  }\n}\n\n// 导出 USER 作为常量\nexport const USER = 'editor'\n</script>\n\n<style lang=\"scss\">\n/* 修复input 背景不协调 和光标变色 */\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\n\n$bg:#283443;\n$light_gray:#fff;\n$cursor: #fff;\n\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\n  .login-container .el-input input {\n    color: $cursor;\n  }\n}\n\n/* reset element-ui css */\n.login-container {\n  .el-input {\n    display: inline-block;\n    height: 47px;\n    width: 85%;\n\n    input {\n      background: transparent;\n      border: 0px;\n      -webkit-appearance: none;\n      border-radius: 0px;\n      padding: 12px 5px 12px 15px;\n      color: $light_gray;\n      height: 47px;\n      caret-color: $cursor;\n\n      &:-webkit-autofill {\n        box-shadow: 0 0 0px 1000px $bg inset !important;\n        -webkit-text-fill-color: $cursor !important;\n      }\n    }\n  }\n\n  .el-form-item {\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    background: rgba(0, 0, 0, 0.1);\n    border-radius: 5px;\n    color: #454545;\n  }\n}\n</style>\n\n<style lang=\"scss\" scoped>\n$bg:#2d3a4b;\n$dark_gray:#889aa4;\n$light_gray:#eee;\n\n.login-container {\n  background-image: url('../../assets/used_images/background1.jpg'); /* 替换为你的图片路径 */\n  background-size: cover; /* 使背景图像覆盖整个容器 */\n  background-position: center; /* 将背景图像居中 */\n  background-repeat: no-repeat; /* 不重复 */\n  height: 100vh; /* 设置容器的高度 */\n  display: flex; /* 使用 Flexbox 使内容居中 */\n  align-items: center; /* 垂直居中 */\n  justify-content: center; /* 水平居中 */\n}\n\n.small-image {\n  position: absolute; /* 绝对定位 */\n  top: 10px; /* 距离上方的距离 */\n  left: 10px; /* 距离左侧的距离 */\n  width: 130px; /* 设置宽度 */\n  height: 130px; /* 高度自适应 */\n  opacity: 1; /* 设置透明度 */\n  z-index: 10; /* 确保小图在其它元素上方 */\n  transition: transform 0.3s; /* 添加过渡效果 */\n}\n\n.login-container {\n  min-height: 100%;\n  width: 100%;\n  background-color: $bg;\n  overflow: hidden;\n\n  .login-form {\n    position: relative;\n    width: 520px;\n    max-width: 100%;\n    padding: 60px 35px 0;\n    margin: 0 auto;\n    overflow: hidden;\n  }\n\n  .tips {\n    font-size: 14px;\n    color: #fff;\n    margin-bottom: 10px;\n\n    span {\n      &:first-of-type {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .svg-container {\n    padding: 6px 5px 6px 15px;\n    color: $dark_gray;\n    vertical-align: middle;\n    width: 30px;\n    display: inline-block;\n  }\n\n  .title-container {\n    position: relative;\n\n    .title {\n      font-size: 26px;\n      color: $light_gray;\n      margin: 0px auto 40px auto;\n      text-align: center;\n      font-weight: bold;\n    }\n  }\n\n  .show-pwd {\n    position: absolute;\n    right: 10px;\n    top: 7px;\n    font-size: 16px;\n    color: $dark_gray;\n    cursor: pointer;\n    user-select: none;\n  }\n\n  .thirdparty-button {\n    position: absolute;\n    right: 0;\n    bottom: 6px;\n  }\n\n  @media only screen and (max-width: 470px) {\n    .thirdparty-button {\n      display: none;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;AA6EA,SAAAA,aAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA,IAAAC,gBAAA,YAAAA,iBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAR,aAAA,CAAAO,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA,IAAAE,gBAAA,YAAAA,iBAAAJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA,CAAAI,MAAA;QACAH,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA;MACAI,SAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,UAAA;QACAF,QAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,SAAA,EAAAb;QAAA;QACAS,QAAA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,SAAA,EAAAR;QAAA;MACA;MACAS,YAAA;MACAC,WAAA;MACAC,OAAA;MACAC,UAAA;MACAC,QAAA,EAAAC,SAAA;MACAC,UAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,KAAA;QACA,IAAAC,KAAA,GAAAD,KAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAP,QAAA,GAAAO,KAAA,CAAAP,QAAA;UACA,KAAAE,UAAA,QAAAM,aAAA,CAAAD,KAAA;QACA;MACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAtB,SAAA,CAAAC,QAAA;MACA,KAAAsB,KAAA,CAAAtB,QAAA,CAAAuB,KAAA;IACA,gBAAAxB,SAAA,CAAAE,QAAA;MACA,KAAAqB,KAAA,CAAArB,QAAA,CAAAsB,KAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAAC,CAAA;MACA,IAAAC,GAAA,GAAAD,CAAA,CAAAC,GAAA;MACA,KAAArB,WAAA,GAAAqB,GAAA,IAAAA,GAAA,CAAA9B,MAAA,UAAA8B,GAAA,WAAAA,GAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,SAAAxB,YAAA;QACA,KAAAA,YAAA;MACA;QACA,KAAAA,YAAA;MACA;MACA,KAAAyB,SAAA;QACAD,KAAA,CAAAR,KAAA,CAAArB,QAAA,CAAAsB,KAAA;MACA;IACA;IACAS,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAX,KAAA,CAAAvB,SAAA,CAAAmC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAzB,OAAA;UACAyB,MAAA,CAAAG,MAAA,CAAAC,QAAA,eAAAJ,MAAA,CAAAlC,SAAA,EACAuC,IAAA;YACAL,MAAA,CAAAM,OAAA,CAAAC,IAAA;cAAAC,IAAA,EAAAR,MAAA,CAAAvB,QAAA;cAAAO,KAAA,EAAAgB,MAAA,CAAArB;YAAA;YACAqB,MAAA,CAAAzB,OAAA;UACA,GACAkC,KAAA;YACAT,MAAA,CAAAzB,OAAA;UACA;QACA;UACAmC,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACA1B,aAAA,WAAAA,cAAAD,KAAA;MACA,OAAA4B,MAAA,CAAAC,IAAA,CAAA7B,KAAA,EAAA8B,MAAA,WAAAC,GAAA,EAAAC,GAAA;QACA,IAAAA,GAAA;UACAD,GAAA,CAAAC,GAAA,IAAAhC,KAAA,CAAAgC,GAAA;QACA;QACA,OAAAD,GAAA;MACA;IACA,EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA;;AAEA;AACA,WAAAE,IAAA", "ignoreList": []}]}