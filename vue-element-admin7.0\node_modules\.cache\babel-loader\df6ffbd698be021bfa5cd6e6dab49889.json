{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\directive\\permission\\permission.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\directive\\permission\\permission.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLnNvbWUuanMiOwppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7CmZ1bmN0aW9uIGNoZWNrUGVybWlzc2lvbihlbCwgYmluZGluZykgewogIHZhciB2YWx1ZSA9IGJpbmRpbmcudmFsdWU7CiAgdmFyIHJvbGVzID0gc3RvcmUuZ2V0dGVycyAmJiBzdG9yZS5nZXR0ZXJzLnJvbGVzOwogIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICBpZiAodmFsdWUubGVuZ3RoID4gMCkgewogICAgICB2YXIgcGVybWlzc2lvblJvbGVzID0gdmFsdWU7CiAgICAgIHZhciBoYXNQZXJtaXNzaW9uID0gcm9sZXMuc29tZShmdW5jdGlvbiAocm9sZSkgewogICAgICAgIHJldHVybiBwZXJtaXNzaW9uUm9sZXMuaW5jbHVkZXMocm9sZSk7CiAgICAgIH0pOwogICAgICBpZiAoIWhhc1Blcm1pc3Npb24pIHsKICAgICAgICBlbC5wYXJlbnROb2RlICYmIGVsLnBhcmVudE5vZGUucmVtb3ZlQ2hpbGQoZWwpOwogICAgICB9CiAgICB9CiAgfSBlbHNlIHsKICAgIHRocm93IG5ldyBFcnJvcigibmVlZCByb2xlcyEgTGlrZSB2LXBlcm1pc3Npb249XCJbJ2FkbWluJywnZWRpdG9yJ11cIiIpOwogIH0KfQpleHBvcnQgZGVmYXVsdCB7CiAgaW5zZXJ0ZWQ6IGZ1bmN0aW9uIGluc2VydGVkKGVsLCBiaW5kaW5nKSB7CiAgICBjaGVja1Blcm1pc3Npb24oZWwsIGJpbmRpbmcpOwogIH0sCiAgdXBkYXRlOiBmdW5jdGlvbiB1cGRhdGUoZWwsIGJpbmRpbmcpIHsKICAgIGNoZWNrUGVybWlzc2lvbihlbCwgYmluZGluZyk7CiAgfQp9Ow=="}, {"version": 3, "names": ["store", "checkPermission", "el", "binding", "value", "roles", "getters", "Array", "length", "permissionRoles", "hasPermission", "some", "role", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error", "inserted", "update"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/directive/permission/permission.js"], "sourcesContent": ["import store from '@/store'\n\nfunction checkPermission(el, binding) {\n  const { value } = binding\n  const roles = store.getters && store.getters.roles\n\n  if (value && value instanceof Array) {\n    if (value.length > 0) {\n      const permissionRoles = value\n\n      const hasPermission = roles.some(role => {\n        return permissionRoles.includes(role)\n      })\n\n      if (!hasPermission) {\n        el.parentNode && el.parentNode.removeChild(el)\n      }\n    }\n  } else {\n    throw new Error(`need roles! Like v-permission=\"['admin','editor']\"`)\n  }\n}\n\nexport default {\n  inserted(el, binding) {\n    checkPermission(el, binding)\n  },\n  update(el, binding) {\n    checkPermission(el, binding)\n  }\n}\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,SAAS;AAE3B,SAASC,eAAeA,CAACC,EAAE,EAAEC,OAAO,EAAE;EACpC,IAAQC,KAAK,GAAKD,OAAO,CAAjBC,KAAK;EACb,IAAMC,KAAK,GAAGL,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACM,OAAO,CAACD,KAAK;EAElD,IAAID,KAAK,IAAIA,KAAK,YAAYG,KAAK,EAAE;IACnC,IAAIH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACpB,IAAMC,eAAe,GAAGL,KAAK;MAE7B,IAAMM,aAAa,GAAGL,KAAK,CAACM,IAAI,CAAC,UAAAC,IAAI,EAAI;QACvC,OAAOH,eAAe,CAACI,QAAQ,CAACD,IAAI,CAAC;MACvC,CAAC,CAAC;MAEF,IAAI,CAACF,aAAa,EAAE;QAClBR,EAAE,CAACY,UAAU,IAAIZ,EAAE,CAACY,UAAU,CAACC,WAAW,CAACb,EAAE,CAAC;MAChD;IACF;EACF,CAAC,MAAM;IACL,MAAM,IAAIc,KAAK,uDAAqD,CAAC;EACvE;AACF;AAEA,eAAe;EACbC,QAAQ,WAARA,QAAQA,CAACf,EAAE,EAAEC,OAAO,EAAE;IACpBF,eAAe,CAACC,EAAE,EAAEC,OAAO,CAAC;EAC9B,CAAC;EACDe,MAAM,WAANA,MAAMA,CAAChB,EAAE,EAAEC,OAAO,EAAE;IAClBF,eAAe,CAACC,EAAE,EAAEC,OAAO,CAAC;EAC9B;AACF,CAAC", "ignoreList": []}]}