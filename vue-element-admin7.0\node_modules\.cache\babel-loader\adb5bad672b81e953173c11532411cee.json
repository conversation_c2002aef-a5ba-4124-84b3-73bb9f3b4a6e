{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\tagsView.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\store\\modules\\tagsView.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["state", "visitedViews", "cachedViews", "mutations", "ADD_VISITED_VIEW", "view", "some", "v", "path", "push", "Object", "assign", "title", "meta", "ADD_CACHED_VIEW", "includes", "name", "noCache", "DEL_VISITED_VIEW", "_iterator", "_createForOfIteratorHelper", "entries", "_step", "s", "n", "done", "_step$value", "_slicedToArray", "value", "i", "splice", "err", "e", "f", "DEL_CACHED_VIEW", "index", "indexOf", "DEL_OTHERS_VISITED_VIEWS", "filter", "affix", "DEL_OTHERS_CACHED_VIEWS", "slice", "DEL_ALL_VISITED_VIEWS", "affixTags", "tag", "DEL_ALL_CACHED_VIEWS", "UPDATE_VISITED_VIEW", "_iterator2", "_step2", "actions", "add<PERSON><PERSON><PERSON>", "_ref", "dispatch", "addVisitedView", "_ref2", "commit", "add<PERSON><PERSON>d<PERSON>iew", "_ref3", "<PERSON><PERSON><PERSON><PERSON>", "_ref4", "Promise", "resolve", "_toConsumableArray", "delVisitedView", "_ref5", "del<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref6", "delOthersViews", "_ref7", "delOthersVisitedViews", "_ref8", "delOthersCachedViews", "_ref9", "delAllViews", "_ref0", "delAllVisitedViews", "_ref1", "delAllCachedViews", "_ref10", "updateVisitedView", "_ref11", "namespaced"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/store/modules/tagsView.js"], "sourcesContent": ["const state = {\n  visitedViews: [],\n  cachedViews: []\n}\n\nconst mutations = {\n  ADD_VISITED_VIEW: (state, view) => {\n    if (state.visitedViews.some(v => v.path === view.path)) return\n    state.visitedViews.push(\n      Object.assign({}, view, {\n        title: view.meta.title || 'no-name'\n      })\n    )\n  },\n  ADD_CACHED_VIEW: (state, view) => {\n    if (state.cachedViews.includes(view.name)) return\n    if (!view.meta.noCache) {\n      state.cachedViews.push(view.name)\n    }\n  },\n\n  DEL_VISITED_VIEW: (state, view) => {\n    for (const [i, v] of state.visitedViews.entries()) {\n      if (v.path === view.path) {\n        state.visitedViews.splice(i, 1)\n        break\n      }\n    }\n  },\n  DEL_CACHED_VIEW: (state, view) => {\n    const index = state.cachedViews.indexOf(view.name)\n    index > -1 && state.cachedViews.splice(index, 1)\n  },\n\n  DEL_OTHERS_VISITED_VIEWS: (state, view) => {\n    state.visitedViews = state.visitedViews.filter(v => {\n      return v.meta.affix || v.path === view.path\n    })\n  },\n  DEL_OTHERS_CACHED_VIEWS: (state, view) => {\n    const index = state.cachedViews.indexOf(view.name)\n    if (index > -1) {\n      state.cachedViews = state.cachedViews.slice(index, index + 1)\n    } else {\n      // if index = -1, there is no cached tags\n      state.cachedViews = []\n    }\n  },\n\n  DEL_ALL_VISITED_VIEWS: state => {\n    // keep affix tags\n    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)\n    state.visitedViews = affixTags\n  },\n  DEL_ALL_CACHED_VIEWS: state => {\n    state.cachedViews = []\n  },\n\n  UPDATE_VISITED_VIEW: (state, view) => {\n    for (let v of state.visitedViews) {\n      if (v.path === view.path) {\n        v = Object.assign(v, view)\n        break\n      }\n    }\n  }\n}\n\nconst actions = {\n  addView({ dispatch }, view) {\n    dispatch('addVisitedView', view)\n    dispatch('addCachedView', view)\n  },\n  addVisitedView({ commit }, view) {\n    commit('ADD_VISITED_VIEW', view)\n  },\n  addCachedView({ commit }, view) {\n    commit('ADD_CACHED_VIEW', view)\n  },\n\n  delView({ dispatch, state }, view) {\n    return new Promise(resolve => {\n      dispatch('delVisitedView', view)\n      dispatch('delCachedView', view)\n      resolve({\n        visitedViews: [...state.visitedViews],\n        cachedViews: [...state.cachedViews]\n      })\n    })\n  },\n  delVisitedView({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_VISITED_VIEW', view)\n      resolve([...state.visitedViews])\n    })\n  },\n  delCachedView({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_CACHED_VIEW', view)\n      resolve([...state.cachedViews])\n    })\n  },\n\n  delOthersViews({ dispatch, state }, view) {\n    return new Promise(resolve => {\n      dispatch('delOthersVisitedViews', view)\n      dispatch('delOthersCachedViews', view)\n      resolve({\n        visitedViews: [...state.visitedViews],\n        cachedViews: [...state.cachedViews]\n      })\n    })\n  },\n  delOthersVisitedViews({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_OTHERS_VISITED_VIEWS', view)\n      resolve([...state.visitedViews])\n    })\n  },\n  delOthersCachedViews({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_OTHERS_CACHED_VIEWS', view)\n      resolve([...state.cachedViews])\n    })\n  },\n\n  delAllViews({ dispatch, state }, view) {\n    return new Promise(resolve => {\n      dispatch('delAllVisitedViews', view)\n      dispatch('delAllCachedViews', view)\n      resolve({\n        visitedViews: [...state.visitedViews],\n        cachedViews: [...state.cachedViews]\n      })\n    })\n  },\n  delAllVisitedViews({ commit, state }) {\n    return new Promise(resolve => {\n      commit('DEL_ALL_VISITED_VIEWS')\n      resolve([...state.visitedViews])\n    })\n  },\n  delAllCachedViews({ commit, state }) {\n    return new Promise(resolve => {\n      commit('DEL_ALL_CACHED_VIEWS')\n      resolve([...state.cachedViews])\n    })\n  },\n\n  updateVisitedView({ commit }, view) {\n    commit('UPDATE_VISITED_VIEW', view)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAMA,KAAK,GAAG;EACZC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE;AACf,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGJ,KAAK,EAAEK,IAAI,EAAK;IACjC,IAAIL,KAAK,CAACC,YAAY,CAACK,IAAI,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI;IAAA,EAAC,EAAE;IACxDR,KAAK,CAACC,YAAY,CAACQ,IAAI,CACrBC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,IAAI,EAAE;MACtBO,KAAK,EAAEP,IAAI,CAACQ,IAAI,CAACD,KAAK,IAAI;IAC5B,CAAC,CACH,CAAC;EACH,CAAC;EACDE,eAAe,EAAE,SAAjBA,eAAeA,CAAGd,KAAK,EAAEK,IAAI,EAAK;IAChC,IAAIL,KAAK,CAACE,WAAW,CAACa,QAAQ,CAACV,IAAI,CAACW,IAAI,CAAC,EAAE;IAC3C,IAAI,CAACX,IAAI,CAACQ,IAAI,CAACI,OAAO,EAAE;MACtBjB,KAAK,CAACE,WAAW,CAACO,IAAI,CAACJ,IAAI,CAACW,IAAI,CAAC;IACnC;EACF,CAAC;EAEDE,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGlB,KAAK,EAAEK,IAAI,EAAK;IAAA,IAAAc,SAAA,GAAAC,0BAAA,CACZpB,KAAK,CAACC,YAAY,CAACoB,OAAO,CAAC,CAAC;MAAAC,KAAA;IAAA;MAAjD,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA,GAAmD;QAAA,IAAAC,WAAA,GAAAC,cAAA,CAAAL,KAAA,CAAAM,KAAA;UAAvCC,CAAC,GAAAH,WAAA;UAAEnB,CAAC,GAAAmB,WAAA;QACd,IAAInB,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI,EAAE;UACxBR,KAAK,CAACC,YAAY,CAAC6B,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UAC/B;QACF;MACF;IAAC,SAAAE,GAAA;MAAAZ,SAAA,CAAAa,CAAA,CAAAD,GAAA;IAAA;MAAAZ,SAAA,CAAAc,CAAA;IAAA;EACH,CAAC;EACDC,eAAe,EAAE,SAAjBA,eAAeA,CAAGlC,KAAK,EAAEK,IAAI,EAAK;IAChC,IAAM8B,KAAK,GAAGnC,KAAK,CAACE,WAAW,CAACkC,OAAO,CAAC/B,IAAI,CAACW,IAAI,CAAC;IAClDmB,KAAK,GAAG,CAAC,CAAC,IAAInC,KAAK,CAACE,WAAW,CAAC4B,MAAM,CAACK,KAAK,EAAE,CAAC,CAAC;EAClD,CAAC;EAEDE,wBAAwB,EAAE,SAA1BA,wBAAwBA,CAAGrC,KAAK,EAAEK,IAAI,EAAK;IACzCL,KAAK,CAACC,YAAY,GAAGD,KAAK,CAACC,YAAY,CAACqC,MAAM,CAAC,UAAA/B,CAAC,EAAI;MAClD,OAAOA,CAAC,CAACM,IAAI,CAAC0B,KAAK,IAAIhC,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI;IAC7C,CAAC,CAAC;EACJ,CAAC;EACDgC,uBAAuB,EAAE,SAAzBA,uBAAuBA,CAAGxC,KAAK,EAAEK,IAAI,EAAK;IACxC,IAAM8B,KAAK,GAAGnC,KAAK,CAACE,WAAW,CAACkC,OAAO,CAAC/B,IAAI,CAACW,IAAI,CAAC;IAClD,IAAImB,KAAK,GAAG,CAAC,CAAC,EAAE;MACdnC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACuC,KAAK,CAACN,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL;MACAnC,KAAK,CAACE,WAAW,GAAG,EAAE;IACxB;EACF,CAAC;EAEDwC,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAE1C,KAAK,EAAI;IAC9B;IACA,IAAM2C,SAAS,GAAG3C,KAAK,CAACC,YAAY,CAACqC,MAAM,CAAC,UAAAM,GAAG;MAAA,OAAIA,GAAG,CAAC/B,IAAI,CAAC0B,KAAK;IAAA,EAAC;IAClEvC,KAAK,CAACC,YAAY,GAAG0C,SAAS;EAChC,CAAC;EACDE,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAE7C,KAAK,EAAI;IAC7BA,KAAK,CAACE,WAAW,GAAG,EAAE;EACxB,CAAC;EAED4C,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAG9C,KAAK,EAAEK,IAAI,EAAK;IAAA,IAAA0C,UAAA,GAAA3B,0BAAA,CACtBpB,KAAK,CAACC,YAAY;MAAA+C,MAAA;IAAA;MAAhC,KAAAD,UAAA,CAAAxB,CAAA,MAAAyB,MAAA,GAAAD,UAAA,CAAAvB,CAAA,IAAAC,IAAA,GAAkC;QAAA,IAAzBlB,CAAC,GAAAyC,MAAA,CAAApB,KAAA;QACR,IAAIrB,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI,EAAE;UACxBD,CAAC,GAAGG,MAAM,CAACC,MAAM,CAACJ,CAAC,EAAEF,IAAI,CAAC;UAC1B;QACF;MACF;IAAC,SAAA0B,GAAA;MAAAgB,UAAA,CAAAf,CAAA,CAAAD,GAAA;IAAA;MAAAgB,UAAA,CAAAd,CAAA;IAAA;EACH;AACF,CAAC;AAED,IAAMgB,OAAO,GAAG;EACdC,OAAO,WAAPA,OAAOA,CAAAC,IAAA,EAAe9C,IAAI,EAAE;IAAA,IAAlB+C,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAChBA,QAAQ,CAAC,gBAAgB,EAAE/C,IAAI,CAAC;IAChC+C,QAAQ,CAAC,eAAe,EAAE/C,IAAI,CAAC;EACjC,CAAC;EACDgD,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAajD,IAAI,EAAE;IAAA,IAAhBkD,MAAM,GAAAD,KAAA,CAANC,MAAM;IACrBA,MAAM,CAAC,kBAAkB,EAAElD,IAAI,CAAC;EAClC,CAAC;EACDmD,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAapD,IAAI,EAAE;IAAA,IAAhBkD,MAAM,GAAAE,KAAA,CAANF,MAAM;IACpBA,MAAM,CAAC,iBAAiB,EAAElD,IAAI,CAAC;EACjC,CAAC;EAEDqD,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAsBtD,IAAI,EAAE;IAAA,IAAzB+C,QAAQ,GAAAO,KAAA,CAARP,QAAQ;MAAEpD,KAAK,GAAA2D,KAAA,CAAL3D,KAAK;IACvB,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BT,QAAQ,CAAC,gBAAgB,EAAE/C,IAAI,CAAC;MAChC+C,QAAQ,CAAC,eAAe,EAAE/C,IAAI,CAAC;MAC/BwD,OAAO,CAAC;QACN5D,YAAY,EAAA6D,kBAAA,CAAM9D,KAAK,CAACC,YAAY,CAAC;QACrCC,WAAW,EAAA4D,kBAAA,CAAM9D,KAAK,CAACE,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD6D,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAoB3D,IAAI,EAAE;IAAA,IAAvBkD,MAAM,GAAAS,KAAA,CAANT,MAAM;MAAEvD,KAAK,GAAAgE,KAAA,CAALhE,KAAK;IAC5B,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,kBAAkB,EAAElD,IAAI,CAAC;MAChCwD,OAAO,CAAAC,kBAAA,CAAK9D,KAAK,CAACC,YAAY,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACDgE,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAoB7D,IAAI,EAAE;IAAA,IAAvBkD,MAAM,GAAAW,KAAA,CAANX,MAAM;MAAEvD,KAAK,GAAAkE,KAAA,CAALlE,KAAK;IAC3B,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,iBAAiB,EAAElD,IAAI,CAAC;MAC/BwD,OAAO,CAAAC,kBAAA,CAAK9D,KAAK,CAACE,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAEDiE,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAsB/D,IAAI,EAAE;IAAA,IAAzB+C,QAAQ,GAAAgB,KAAA,CAARhB,QAAQ;MAAEpD,KAAK,GAAAoE,KAAA,CAALpE,KAAK;IAC9B,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BT,QAAQ,CAAC,uBAAuB,EAAE/C,IAAI,CAAC;MACvC+C,QAAQ,CAAC,sBAAsB,EAAE/C,IAAI,CAAC;MACtCwD,OAAO,CAAC;QACN5D,YAAY,EAAA6D,kBAAA,CAAM9D,KAAK,CAACC,YAAY,CAAC;QACrCC,WAAW,EAAA4D,kBAAA,CAAM9D,KAAK,CAACE,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDmE,qBAAqB,WAArBA,qBAAqBA,CAAAC,KAAA,EAAoBjE,IAAI,EAAE;IAAA,IAAvBkD,MAAM,GAAAe,KAAA,CAANf,MAAM;MAAEvD,KAAK,GAAAsE,KAAA,CAALtE,KAAK;IACnC,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,0BAA0B,EAAElD,IAAI,CAAC;MACxCwD,OAAO,CAAAC,kBAAA,CAAK9D,KAAK,CAACC,YAAY,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACDsE,oBAAoB,WAApBA,oBAAoBA,CAAAC,KAAA,EAAoBnE,IAAI,EAAE;IAAA,IAAvBkD,MAAM,GAAAiB,KAAA,CAANjB,MAAM;MAAEvD,KAAK,GAAAwE,KAAA,CAALxE,KAAK;IAClC,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,yBAAyB,EAAElD,IAAI,CAAC;MACvCwD,OAAO,CAAAC,kBAAA,CAAK9D,KAAK,CAACE,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAEDuE,WAAW,WAAXA,WAAWA,CAAAC,KAAA,EAAsBrE,IAAI,EAAE;IAAA,IAAzB+C,QAAQ,GAAAsB,KAAA,CAARtB,QAAQ;MAAEpD,KAAK,GAAA0E,KAAA,CAAL1E,KAAK;IAC3B,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BT,QAAQ,CAAC,oBAAoB,EAAE/C,IAAI,CAAC;MACpC+C,QAAQ,CAAC,mBAAmB,EAAE/C,IAAI,CAAC;MACnCwD,OAAO,CAAC;QACN5D,YAAY,EAAA6D,kBAAA,CAAM9D,KAAK,CAACC,YAAY,CAAC;QACrCC,WAAW,EAAA4D,kBAAA,CAAM9D,KAAK,CAACE,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDyE,kBAAkB,WAAlBA,kBAAkBA,CAAAC,KAAA,EAAoB;IAAA,IAAjBrB,MAAM,GAAAqB,KAAA,CAANrB,MAAM;MAAEvD,KAAK,GAAA4E,KAAA,CAAL5E,KAAK;IAChC,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,uBAAuB,CAAC;MAC/BM,OAAO,CAAAC,kBAAA,CAAK9D,KAAK,CAACC,YAAY,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACD4E,iBAAiB,WAAjBA,iBAAiBA,CAAAC,MAAA,EAAoB;IAAA,IAAjBvB,MAAM,GAAAuB,MAAA,CAANvB,MAAM;MAAEvD,KAAK,GAAA8E,MAAA,CAAL9E,KAAK;IAC/B,OAAO,IAAI4D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,sBAAsB,CAAC;MAC9BM,OAAO,CAAAC,kBAAA,CAAK9D,KAAK,CAACE,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAED6E,iBAAiB,WAAjBA,iBAAiBA,CAAAC,MAAA,EAAa3E,IAAI,EAAE;IAAA,IAAhBkD,MAAM,GAAAyB,MAAA,CAANzB,MAAM;IACxBA,MAAM,CAAC,qBAAqB,EAAElD,IAAI,CAAC;EACrC;AACF,CAAC;AAED,eAAe;EACb4E,UAAU,EAAE,IAAI;EAChBjF,KAAK,EAALA,KAAK;EACLG,SAAS,EAATA,SAAS;EACT8C,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}