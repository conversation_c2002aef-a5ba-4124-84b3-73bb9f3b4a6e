{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\api\\user.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\api\\user.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKZXhwb3J0IGZ1bmN0aW9uIGxvZ2luKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvdnVlLWVsZW1lbnQtYWRtaW4vdXNlci9sb2dpbicsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gZ2V0SW5mbyh0b2tlbikgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy92dWUtZWxlbWVudC1hZG1pbi91c2VyL2luZm8nLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogewogICAgICB0b2tlbjogdG9rZW4KICAgIH0KICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gbG9nb3V0KCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy92dWUtZWxlbWVudC1hZG1pbi91c2VyL2xvZ291dCcsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9"}, {"version": 3, "names": ["request", "login", "data", "url", "method", "getInfo", "token", "params", "logout"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/api/user.js"], "sourcesContent": ["import request from '@/utils/request'\n\nexport function login(data) {\n  return request({\n    url: '/vue-element-admin/user/login',\n    method: 'post',\n    data\n  })\n}\n\nexport function getInfo(token) {\n  return request({\n    url: '/vue-element-admin/user/info',\n    method: 'get',\n    params: { token }\n  })\n}\n\nexport function logout() {\n  return request({\n    url: '/vue-element-admin/user/logout',\n    method: 'post'\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASG,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbG,MAAM,EAAE;MAAED,KAAK,EAALA;IAAM;EAClB,CAAC,CAAC;AACJ;AAEA,OAAO,SAASE,MAAMA,CAAA,EAAG;EACvB,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}