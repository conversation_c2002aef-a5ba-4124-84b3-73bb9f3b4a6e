{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749173686857}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";AA6KA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n<div class=\"app-container\">\r\n<div class=\"upload-and-select-container\">\r\n<!-- 文件上传区域 -->\r\n<div class=\"upload-section\">\r\n<div class=\"section-header\">\r\n<h3>文件上传</h3>\r\n<p class=\"section-desc\">上传新的Excel文件到服务器（上传后会自动刷新下方的文件列表）</p>\r\n</div>\r\n<el-upload\r\nref=\"upload\"\r\nclass=\"upload-demo\"\r\naction=\"\"\r\n:on-change=\"handleFileChange\"\r\n:on-remove=\"handleFileRemove\"\r\n:before-upload=\"beforeUpload\"\r\n:auto-upload=\"false\"\r\n:file-list=\"uploadFileList\"\r\nmultiple\r\naccept=\".xlsx,.xls\"\r\ndrag\r\n>\r\n<i class=\"el-icon-upload\"></i>\r\n<div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击选择文件</em></div>\r\n<div class=\"el-upload__tip\" slot=\"tip\">支持选择多个Excel文件(.xlsx, .xls格式)</div>\r\n</el-upload>\r\n<div class=\"upload-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-upload2\"\r\n:loading=\"uploading\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"handleUpload\"\r\n>\r\n{{ uploading ? '上传中...' : '上传文件' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"uploadFileList.length === 0\"\r\n@click=\"clearUploadFiles\"\r\n>\r\n清空文件\r\n</el-button>\r\n</div>\r\n</div>\r\n\r\n<!-- Excel文件选择区域 -->\r\n<div class=\"selection-section\">\r\n<div class=\"section-header\">\r\n<h3>选择Excel文件进行异常检测</h3>\r\n<p class=\"section-desc\">从服务器已有的Excel文件中选择一个或多个文件进行合并分析（这些是服务器上已存在的数据文件）</p>\r\n</div>\r\n\r\n<!-- 文件列表展示 -->\r\n<div class=\"file-list-container\">\r\n<div class=\"file-table-wrapper\">\r\n<el-table\r\nref=\"tableList\"\r\n:data=\"availableTables\"\r\nborder\r\nfit\r\nhighlight-current-row\r\nstyle=\"width: 100%\"\r\nheight=\"300\"\r\n@selection-change=\"handleSelectionChange\"\r\n>\r\n<el-table-column\r\ntype=\"selection\"\r\nwidth=\"55\"\r\nalign=\"center\"\r\n/>\r\n<el-table-column prop=\"tableName\" label=\"文件名\" min-width=\"250\">\r\n<template #default=\"{row}\">\r\n<i class=\"el-icon-s-grid\" />\r\n<span style=\"margin-left: 8px;\">{{ row.tableName }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column prop=\"createDate\" label=\"创建时间\" width=\"180\" align=\"center\" />\r\n<el-table-column prop=\"recordCount\" label=\"记录数\" width=\"120\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<span class=\"record-count\">{{ row.recordCount ? row.recordCount.toLocaleString() : '-' }}</span>\r\n</template>\r\n</el-table-column>\r\n<el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n<template #default=\"{row}\">\r\n<el-tag :type=\"row.status === 'available' ? 'success' : 'info'\" size=\"small\">\r\n{{ row.status === 'available' ? '可用' : '处理中' }}\r\n</el-tag>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n</div>\r\n</div>\r\n</div>\r\n\r\n<!-- 已选择Excel文件显示 -->\r\n<div v-if=\"selectedTables.length > 0\" class=\"selected-tables-section\">\r\n<div class=\"selected-header\">\r\n<span>已选择 {{ selectedTables.length }} 个Excel文件</span>\r\n<div class=\"header-actions\">\r\n<span v-if=\"selectedTables.length > 8\" class=\"scroll-tip\">可滚动查看更多</span>\r\n<el-button type=\"text\" @click=\"clearSelection\">清空选择</el-button>\r\n</div>\r\n</div>\r\n<div class=\"selected-tables-list\">\r\n<el-tag\r\nv-for=\"table in selectedTables\"\r\n:key=\"table.id\"\r\nclosable\r\nsize=\"small\"\r\ntype=\"info\"\r\nstyle=\"margin: 2px 4px 2px 0;\"\r\n@close=\"removeSelectedTable(table)\"\r\n>\r\n{{ table.tableName }}\r\n</el-tag>\r\n</div>\r\n</div>\r\n\r\n</div>\r\n\r\n<!-- 操作按钮区域 - 移到容器外部 -->\r\n<div class=\"action-buttons-wrapper\">\r\n<div class=\"action-buttons\">\r\n<el-button\r\ntype=\"primary\"\r\nicon=\"el-icon-refresh\"\r\n:loading=\"loadingFiles\"\r\n@click=\"loadAvailableFiles\"\r\n>\r\n刷新Excel文件列表\r\n</el-button>\r\n<el-button\r\ntype=\"success\"\r\nicon=\"el-icon-s-data\"\r\n:loading=\"processing\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"processSelectedTables\"\r\nsize=\"medium\"\r\n>\r\n{{ processing ? '处理中...' : '🔍 异常检测分析' }}\r\n</el-button>\r\n<el-button\r\nicon=\"el-icon-delete\"\r\n:disabled=\"selectedTables.length === 0\"\r\n@click=\"clearSelection\"\r\n>\r\n清空选择\r\n</el-button>\r\n</div>\r\n\r\n<!-- 进度显示 -->\r\n<div v-if=\"uploading || processing\" class=\"progress-section\">\r\n<el-progress\r\n  :percentage=\"uploading ? uploadProgress : processProgress\"\r\n:status=\"(uploading ? uploadProgress : processProgress) === 100 ? 'success' : ''\"\r\n  :stroke-width=\"8\"\r\n/>\r\n<p class=\"progress-text\">{{ uploading ? uploadProgressText : progressText }}</p>\r\n</div>\r\n</div>\r\n\r\n<!-- 异常结果展示组件 -->\r\n<exception-results\r\n  :exception-data=\"exceptionList\"\r\n  @export-results=\"handleExportResults\"\r\n  @clear-results=\"handleClearResults\"\r\n/>\r\n</div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport ExceptionResults from './ExceptionResults.vue'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  components: {\r\n    ExceptionResults\r\n  },\r\n  data() {\r\n    return {\r\n      // 文件上传相关\r\n      uploadFileList: [],\r\n      uploading: false,\r\n      uploadProgress: 0,\r\n      uploadProgressText: '',\r\n\r\n      // Excel文件选择相关\r\n      availableTables: [], // 从后端动态加载\r\n      selectedTables: [],\r\n      loadingFiles: false,\r\n      processing: false,\r\n      processProgress: 0,\r\n      progressText: '',\r\n\r\n      // 异常数据列表\r\n      exceptionList: [], // 从后端异常检测获取\r\n      exceptionColumns: [], // 动态生成的表格列\r\n      scrollContainer: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时清空异常数据列表，等待用户选择文件\r\n    this.exceptionList = []\r\n    // 加载可用文件列表\r\n    this.loadAvailableFiles()\r\n  },\r\n  methods: {\r\n    // 文件上传相关方法\r\n    handleFileChange(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('上传文件列表更新:', fileList)\r\n    },\r\n\r\n    handleFileRemove(file, fileList) {\r\n      this.uploadFileList = fileList\r\n      console.log('文件已移除:', file.name)\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n      return false // 阻止自动上传，手动控制\r\n    },\r\n\r\n    clearUploadFiles() {\r\n      this.uploadFileList = []\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info('已清空上传文件列表')\r\n    },\r\n\r\n    async handleUpload() {\r\n      if (this.uploadFileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      this.uploadProgress = 0\r\n      this.uploadProgressText = '准备上传文件...'\r\n\r\n      try {\r\n        const formData = new FormData()\r\n\r\n        // 添加所有文件到FormData\r\n        this.uploadFileList.forEach((fileItem, index) => {\r\n          formData.append('files', fileItem.raw)\r\n        })\r\n\r\n        // 模拟进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.uploadProgress < 90) {\r\n            this.uploadProgress += Math.random() * 10\r\n            this.uploadProgressText = `正在上传文件... ${Math.round(this.uploadProgress)}%`\r\n          }\r\n        }, 200)\r\n\r\n        // 真正调用后端API上传文件\r\n        // 注意：如果后端没有实现 /upload-files 接口，请注释掉下面的代码，使用模拟上传\r\n        try {\r\n          const response = await axios.post('http://127.0.0.1:8000/upload-files', formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            timeout: 60000\r\n          })\r\n\r\n          // 检查上传结果\r\n          if (!response.data || !response.data.success) {\r\n            throw new Error(response.data?.message || '上传失败')\r\n          }\r\n        } catch (uploadError) {\r\n          // 如果上传接口不存在，使用模拟上传\r\n          if (uploadError.response && uploadError.response.status === 404) {\r\n            console.warn('上传接口不存在，使用模拟上传')\r\n            await new Promise(resolve => setTimeout(resolve, 2000))\r\n          } else {\r\n            throw uploadError\r\n          }\r\n        }\r\n\r\n        clearInterval(progressInterval)\r\n        this.uploadProgress = 100\r\n        this.uploadProgressText = '文件上传完成！'\r\n\r\n        // 上传成功后，重新加载服务器上的Excel文件列表\r\n        await this.loadAvailableFiles()\r\n\r\n        this.$message.success(`成功上传 ${this.uploadFileList.length} 个文件`)\r\n        this.clearUploadFiles()\r\n      } catch (error) {\r\n        console.error('上传失败:', error)\r\n        this.uploadProgress = 0\r\n        this.uploadProgressText = ''\r\n        this.$message.error(`上传失败: ${error.message}`)\r\n      } finally {\r\n        this.uploading = false\r\n        setTimeout(() => {\r\n          this.uploadProgress = 0\r\n          this.uploadProgressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    // 加载可用数据表列表\r\n    async loadAvailableFiles() {\r\n      this.loadingFiles = true\r\n      try {\r\n        // 调用后端API获取所有Excel文件路径\r\n        const response = await axios.post('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        console.log('后端返回的Excel文件路径:', response.data)\r\n        console.log('这些文件来自path_default文件夹:', response.data.paths)\r\n\r\n        if (response.data && response.data.paths) {\r\n          // 将文件路径转换为前端显示格式\r\n          this.availableTables = response.data.paths.map((filePath, index) => {\r\n            // 提取文件名作为表名显示\r\n            const fileName = filePath.split('\\\\').pop() || filePath.split('/').pop()\r\n            const tableName = fileName.replace('.xlsx', '') // 移除扩展名\r\n\r\n            return {\r\n              id: index + 1,\r\n              tableName: tableName, // 显示文件名（不含扩展名）\r\n              filePath: filePath, // 保存完整路径用于后端处理\r\n              createDate: '2024-12-20 10:00:00', // 后端没有提供时间，使用默认值\r\n              recordCount: null, // 后端没有提供记录数\r\n              status: 'available'\r\n            }\r\n          })\r\n          this.$message.success(`加载了 ${this.availableTables.length} 个Excel文件`)\r\n        } else {\r\n          this.$message.warning('没有找到可用的Excel文件')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载Excel文件列表失败:', error)\r\n        this.$message.error('加载Excel文件列表失败: ' + error.message)\r\n      } finally {\r\n        this.loadingFiles = false\r\n      }\r\n    },\r\n\r\n    // 处理Excel文件选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedTables = selection\r\n      console.log('已选择Excel文件:', selection)\r\n    },\r\n\r\n    // 移除已选择的Excel文件\r\n    removeSelectedTable(table) {\r\n      const index = this.selectedTables.findIndex(t => t.id === table.id)\r\n      if (index > -1) {\r\n        this.selectedTables.splice(index, 1)\r\n      }\r\n      // 同时更新表格选择状态\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.toggleRowSelection(table, false)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedTables = []\r\n      // 清空表格选择\r\n      this.$nextTick(() => {\r\n        const tableRef = this.$refs.tableList\r\n        if (tableRef) {\r\n          tableRef.clearSelection()\r\n        }\r\n      })\r\n      this.$message.info('已清空Excel文件选择')\r\n    },\r\n    async processSelectedTables() {\r\n      if (this.selectedTables.length === 0) {\r\n        this.$message.warning('请先选择要处理的Excel文件')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n      this.processProgress = 0\r\n      this.progressText = '开始处理Excel文件...'\r\n\r\n      try {\r\n        // 进度更新\r\n        const progressInterval = setInterval(() => {\r\n          if (this.processProgress < 80) {\r\n            this.processProgress += Math.random() * 10\r\n            const currentStep = Math.floor(this.processProgress / 25)\r\n            const steps = ['正在读取Excel文件...', '正在合并数据...', '正在分析异常...', '处理中...']\r\n            this.progressText = steps[currentStep] || '处理中...'\r\n          }\r\n        }, 500)\r\n\r\n        // 调用后端异常检测接口\r\n        const filePaths = this.selectedTables.map(t => t.filePath)\r\n        console.log('选中的表格数据:', this.selectedTables)\r\n        console.log('发送到后端的文件路径:', filePaths)\r\n        console.log('这些路径来自path_default文件夹:', filePaths)\r\n\r\n        this.progressText = '正在调用后端分析接口...'\r\n\r\n        // 真正调用后端API\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: filePaths\r\n        })\r\n\r\n        clearInterval(progressInterval)\r\n        this.processProgress = 100\r\n        this.progressText = '数据处理完成！'\r\n\r\n        console.log('后端返回的异常检测结果:', response.data)\r\n\r\n        // 处理后端返回的异常数据\r\n        if (response.data) {\r\n          const exceptionList = []\r\n\r\n          console.log('后端返回的原始数据结构:', response.data)\r\n\r\n          // 遍历后端返回的各种异常类型\r\n          Object.keys(response.data).forEach(exceptionType => {\r\n            const exceptions = response.data[exceptionType]\r\n            console.log(`异常类型 ${exceptionType} 的数据:`, exceptions)\r\n\r\n            if (exceptions && exceptions.length > 0) {\r\n              exceptions.forEach((item, index) => {\r\n                // 直接使用后端返回的数据，添加异常类型\r\n                const exception = {\r\n                  异常类型: exceptionType, // 添加异常类型字段\r\n                  ...item // 展开后端返回的所有字段\r\n                }\r\n                exceptionList.push(exception)\r\n              })\r\n            }\r\n          })\r\n\r\n          // 根据detectTrade函数的返回结构，固定列配置\r\n          this.exceptionColumns = [\r\n            {\r\n              prop: '异常类型',\r\n              label: '异常类型',\r\n              width: 150,\r\n              align: 'center',\r\n              type: 'tag'\r\n            },\r\n            {\r\n              prop: '订单号',\r\n              label: '订单号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人姓名',\r\n              label: '支付人姓名',\r\n              width: 120,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '支付人身份证号',\r\n              label: '支付人身份证号',\r\n              width: 180,\r\n              align: 'center'\r\n            },\r\n            {\r\n              prop: '物流单号',\r\n              label: '物流单号',\r\n              width: 180,\r\n              align: 'center'\r\n            }\r\n          ]\r\n\r\n          this.exceptionList = exceptionList\r\n          console.log('处理后的异常数据列表:', this.exceptionList)\r\n          console.log('表格列配置:', this.exceptionColumns)\r\n\r\n          if (exceptionList.length > 0) {\r\n            this.$message.success(`成功处理 ${this.selectedTables.length} 个Excel文件，发现 ${exceptionList.length} 条异常数据`)\r\n          } else {\r\n            this.$message.info(`成功处理 ${this.selectedTables.length} 个Excel文件，未发现异常数据`)\r\n          }\r\n        } else {\r\n          this.$message.warning('后端返回数据格式异常')\r\n        }\r\n      } catch (error) {\r\n        console.error('处理失败:', error)\r\n        this.processProgress = 0\r\n        this.progressText = ''\r\n\r\n        if (error.response) {\r\n          this.$message.error(`处理失败: ${error.response.status} - ${error.response.data?.message || error.message}`)\r\n        } else if (error.request) {\r\n          this.$message.error('网络连接失败，请检查后端服务是否启动')\r\n        } else {\r\n          this.$message.error(`处理失败: ${error.message}`)\r\n        }\r\n      } finally {\r\n        this.processing = false\r\n        setTimeout(() => {\r\n          this.processProgress = 0\r\n          this.progressText = ''\r\n        }, 3000)\r\n      }\r\n    },\r\n\r\n    handleScroll(event) {\r\n      // 处理滚动事件\r\n      console.log('Scrolling...', event)\r\n    },\r\n\r\n    // 根据异常类型返回对应的标签颜色\r\n    getExceptionTypeColor(exceptionType) {\r\n      const colorMap = {\r\n        '同一姓名多个身份证': 'danger',\r\n        '同一身份证多个姓名': 'warning',\r\n        '物流单号重复': 'info',\r\n        '订单号多个身份证': 'success',\r\n        // 添加更多可能的异常类型\r\n        '重复订单': 'danger',\r\n        '异常物流': 'warning',\r\n        '身份证异常': 'danger',\r\n        '姓名异常': 'warning'\r\n      }\r\n      return colorMap[exceptionType] || 'primary'\r\n    },\r\n\r\n    // 根据列名获取列宽度\r\n    getColumnWidth(columnName) {\r\n      const widthMap = {\r\n        '订单号': 180,\r\n        '支付人姓名': 120,\r\n        '支付人身份证号': 180,\r\n        '物流单号': 180,\r\n        '异常类型': 150\r\n      }\r\n      return widthMap[columnName] || 120\r\n    },\r\n\r\n    // 根据列名获取对齐方式\r\n    getColumnAlign(columnName) {\r\n      const alignMap = {\r\n        '订单号': 'center',\r\n        '支付人姓名': 'center',\r\n        '支付人身份证号': 'center',\r\n        '物流单号': 'center',\r\n        '异常类型': 'center'\r\n      }\r\n      return alignMap[columnName] || 'left'\r\n    },\r\n\r\n    // 处理导出结果\r\n    handleExportResults(data) {\r\n      console.log('导出异常结果:', data)\r\n      // 这里可以实现具体的导出逻辑\r\n      this.$message.success('导出功能开发中...')\r\n    },\r\n\r\n    // 处理清空结果\r\n    handleClearResults() {\r\n      this.$confirm('确定要清空所有异常检测结果吗？', '确认清空', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.exceptionList = []\r\n        this.exceptionColumns = []\r\n        this.$message.success('已清空异常检测结果')\r\n      }).catch(() => {\r\n        this.$message.info('已取消清空操作')\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n/* 上传和选择容器样式 */\r\n.upload-and-select-container {\r\n  margin-bottom: 20px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n/* 上传区域样式 */\r\n.upload-section {\r\n  margin-bottom: 20px; /* 从30px减少到20px */\r\n  padding: 15px; /* 从20px减少到15px */\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.upload-demo .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100px; /* 从180px减少到100px */\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-demo .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-demo .el-upload-dragger .el-icon-upload {\r\n  font-size: 40px; /* 从67px减少到40px */\r\n  color: #c0c4cc;\r\n  margin: 15px 0 8px; /* 从40px 0 16px调整到15px 0 8px */\r\n  line-height: 30px; /* 从50px减少到30px */\r\n}\r\n\r\n.upload-demo .el-upload__text {\r\n  color: #606266;\r\n  font-size: 13px; /* 从14px减少到13px */\r\n  text-align: center;\r\n  margin-top: -5px; /* 向上调整位置 */\r\n}\r\n\r\n.upload-demo .el-upload__text em {\r\n  color: #409eff;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-demo .el-upload__tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 7px;\r\n}\r\n\r\n.upload-buttons {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.selection-section {\r\n  margin-bottom: 15px; /* 从20px减少到15px */\r\n}\r\n\r\n.section-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.section-desc {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表容器 */\r\n.file-list-container {\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-table-wrapper {\r\n  position: relative;\r\n  max-height: 400px;\r\n  overflow: auto;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.file-table-wrapper::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n.file-table-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 已选择数据表区域 */\r\n.selected-tables-section {\r\n  margin: 15px 0; /* 从20px减少到15px */\r\n  padding: 12px; /* 从15px减少到12px */\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n  max-height: 120px; /* 从200px减少到120px */\r\n  overflow: hidden; /* 隐藏溢出内容 */\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.scroll-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  font-weight: normal;\r\n}\r\n\r\n.selected-tables-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px; /* 从8px减少到6px */\r\n  max-height: 80px; /* 从120px减少到80px */\r\n  overflow-y: auto; /* 添加垂直滚动条 */\r\n  padding-right: 8px; /* 为滚动条留出空间 */\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.selected-tables-list::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.selected-tables-list::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n\r\n/* 操作按钮包装器 */\r\n.action-buttons-wrapper {\r\n  margin: 20px 0;\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #ffffff, #f8f9fa);\r\n  border: 2px solid #409eff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.action-buttons::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  background: linear-gradient(45deg, #409eff, #67c23a, #e6a23c, #f56c6c);\r\n  border-radius: 12px;\r\n  z-index: -1;\r\n  animation: borderGlow 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes borderGlow {\r\n  0%, 100% { opacity: 0.6; }\r\n  50% { opacity: 1; }\r\n}\r\n\r\n.action-buttons .el-button {\r\n  padding: 14px 24px;\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-buttons .el-button--success {\r\n  background: linear-gradient(135deg, #67c23a, #85ce61);\r\n  border: none;\r\n  box-shadow: 0 3px 10px rgba(103, 194, 58, 0.4);\r\n  font-size: 16px;\r\n  padding: 16px 32px;\r\n}\r\n\r\n.action-buttons .el-button--success:hover {\r\n  background: linear-gradient(135deg, #85ce61, #67c23a);\r\n  transform: translateY(-2px) scale(1.05);\r\n  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.5);\r\n}\r\n\r\n.action-buttons .el-button--primary {\r\n  background: linear-gradient(135deg, #409eff, #66b3ff);\r\n  border: none;\r\n  box-shadow: 0 3px 10px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n.action-buttons .el-button--primary:hover {\r\n  background: linear-gradient(135deg, #66b3ff, #409eff);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);\r\n}\r\n\r\n/* 进度显示区域 */\r\n.progress-section {\r\n  margin-top: 15px;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);\r\n  border-radius: 10px;\r\n  border: 1px solid #b3d8ff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.progress-text {\r\n  margin: 15px 0 0 0;\r\n  font-size: 15px;\r\n  color: #409eff;\r\n  text-align: center;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 卡片样式 */\r\n.box-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 15px;\r\n}\r\n\r\n/* 滚动容器 */\r\n.custom-scrollbar {\r\n  height: 100%;\r\n  overflow: auto;\r\n  padding-right: 12px;\r\n}\r\n\r\n/* 垂直滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar {\r\n  width: 8px; /* 垂直滚动条宽度 */\r\n}\r\n\r\n/* 水平滚动条 */\r\n.custom-scrollbar::-webkit-scrollbar:horizontal {\r\n  height: 8px; /* 水平滚动条高度 */\r\n  margin-bottom: 0px;;\r\n}\r\n\r\n/* 滚动条轨道 */\r\n.custom-scrollbar::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb {\r\n  background: #c0c4cc;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 滚动条滑块悬停效果 */\r\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  background: #a8aeb3;\r\n}\r\n/* 滚动容器 */\r\n/* 表格样式优化 */\r\n.file-list-container .el-table th {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.file-list-container .el-table td {\r\n  padding: 12px 0;\r\n}\r\n\r\n.file-list-container .el-table .el-icon-document {\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.file-list-container .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 记录数样式 */\r\n.file-list-container .el-table .record-count {\r\n  font-weight: 600;\r\n  color: #409eff;\r\n}\r\n\r\n/* 状态标签样式调整 */\r\n.file-list-container .el-tag {\r\n  font-weight: 500;\r\n}\r\n/* 异常结果组件容器 */\r\n.exception-results-wrapper {\r\n  margin-top: 30px;\r\n}\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n   .action-buttons {\r\n     flex-direction: column;\r\n   }\r\n   .action-buttons .el-button {\r\n     width: 100%;\r\n   }\r\n \r\n   .exception-header {\r\n     padding: 12px 15px;\r\n     margin: -15px -15px 15px -15px;\r\n   }\r\n \r\n   .header-title {\r\n     font-size: 16px;\r\n   }\r\n \r\n   .scroll-container {\r\n     height: 400px; \r\n   }\r\n \r\n   .table-summary {\r\n     padding: 12px 15px;\r\n   }\r\n\r\n   .summary-text {\r\n     font-size: 14px;\r\n   }\r\n\r\n   .exception-count {\r\n     font-size: 16px; \r\n   }\r\n  }\r\n</style>\r\n"]}]}