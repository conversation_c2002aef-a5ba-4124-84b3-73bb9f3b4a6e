{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\TagsView\\ScrollPane.vue?vue&type=style&index=0&id=be6b7bae&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\TagsView\\ScrollPane.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zY3JvbGwtY29udGFpbmVyIHsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHdpZHRoOiAxMDAlOwogIDo6di1kZWVwIHsKICAgIC5lbC1zY3JvbGxiYXJfX2JhciB7CiAgICAgIGJvdHRvbTogMHB4OwogICAgfQogICAgLmVsLXNjcm9sbGJhcl9fd3JhcCB7CiAgICAgIGhlaWdodDogNDlweDsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["ScrollPane.vue"], "names": [], "mappings": ";AA+EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ScrollPane.vue", "sourceRoot": "src/layout/components/TagsView", "sourcesContent": ["<template>\n  <el-scrollbar ref=\"scrollContainer\" :vertical=\"false\" class=\"scroll-container\" @wheel.native.prevent=\"handleScroll\">\n    <slot />\n  </el-scrollbar>\n</template>\n\n<script>\nconst tagAndTagSpacing = 4 // tagAndTagSpacing\n\nexport default {\n  name: 'ScrollPane',\n  data() {\n    return {\n      left: 0\n    }\n  },\n  computed: {\n    scrollWrapper() {\n      return this.$refs.scrollContainer.$refs.wrap\n    }\n  },\n  mounted() {\n    this.scrollWrapper.addEventListener('scroll', this.emitScroll, true)\n  },\n  beforeDestroy() {\n    this.scrollWrapper.removeEventListener('scroll', this.emitScroll)\n  },\n  methods: {\n    handleScroll(e) {\n      const eventDelta = e.wheelDelta || -e.deltaY * 40\n      const $scrollWrapper = this.scrollWrapper\n      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4\n    },\n    emitScroll() {\n      this.$emit('scroll')\n    },\n    moveToTarget(currentTag) {\n      const $container = this.$refs.scrollContainer.$el\n      const $containerWidth = $container.offsetWidth\n      const $scrollWrapper = this.scrollWrapper\n      const tagList = this.$parent.$refs.tag\n\n      let firstTag = null\n      let lastTag = null\n\n      // find first tag and last tag\n      if (tagList.length > 0) {\n        firstTag = tagList[0]\n        lastTag = tagList[tagList.length - 1]\n      }\n\n      if (firstTag === currentTag) {\n        $scrollWrapper.scrollLeft = 0\n      } else if (lastTag === currentTag) {\n        $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth\n      } else {\n        // find preTag and nextTag\n        const currentIndex = tagList.findIndex(item => item === currentTag)\n        const prevTag = tagList[currentIndex - 1]\n        const nextTag = tagList[currentIndex + 1]\n\n        // the tag's offsetLeft after of nextTag\n        const afterNextTagOffsetLeft = nextTag.$el.offsetLeft + nextTag.$el.offsetWidth + tagAndTagSpacing\n\n        // the tag's offsetLeft before of prevTag\n        const beforePrevTagOffsetLeft = prevTag.$el.offsetLeft - tagAndTagSpacing\n\n        if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {\n          $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth\n        } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {\n          $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.scroll-container {\n  white-space: nowrap;\n  position: relative;\n  overflow: hidden;\n  width: 100%;\n  ::v-deep {\n    .el-scrollbar__bar {\n      bottom: 0px;\n    }\n    .el-scrollbar__wrap {\n      height: 49px;\n    }\n  }\n}\n</style>\n"]}]}