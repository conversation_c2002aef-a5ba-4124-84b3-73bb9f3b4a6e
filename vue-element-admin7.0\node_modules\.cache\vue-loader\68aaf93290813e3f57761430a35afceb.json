{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\update\\updateFile.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\update\\updateFile.vue", "mtime": 1747748935259}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["updateFile.vue"], "names": [], "mappings": ";AAuCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "updateFile.vue", "sourceRoot": "src/views/update", "sourcesContent": ["<template>\r\n  <div style=\"margin-right: 40px\">\r\n    <el-select\r\n        v-model=\"value\"\r\n        placeholder=\"数据表\"\r\n        no-data-text=\"已经没有数据表了\"\r\n        style=\"margin-left: 20px;\"\r\n        @focus=\"handleSearch\"\r\n        @change=\"handleSelectChange\"\r\n    >\r\n        <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\n/>\r\n    </el-select>\r\n    <el-upload\r\n      ref=\"upload\"\r\n      class=\"upload-demo\"\r\n      action=\"http://localhost:8000/file\"\r\n      :on-preview=\"handlePreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-change=\"handleChange\"\r\n      :file-list=\"fileList\"\r\n      :auto-upload=\"false\"\r\n      :on-success=\"handleSuccess\"\r\n      :on-error=\"handleError\"\r\n      accept=\".xls,.xlsx\"\n>\r\n      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\r\n      <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"success\" @click=\"submitUpload\">导入数据</el-button>\r\n      <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"info\" @click=\"DownloadModel\">下载模板</el-button>\r\n      <div slot=\"tip\" class=\"el-upload__tip\">只能上传xls/xlsx文件</div>\r\n    </el-upload>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport * as XLSX from 'xlsx'\r\nimport { USER } from '../login/index.vue'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      value: '', // 选中的数据表\r\n      fileList: [],\r\n      transactionData: [], // 用于存储交易数据\r\n      requiredHeaders: [\r\n        '交易户名', '交易卡号', '交易账号', '交易时间',\r\n        '交易金额', '交易余额', '收付标志', '对手账号',\r\n        '现金标志', '对手户名', '对手身份证号', '对手开户银行',\r\n        '摘要说明', '交易币种', '交易网点名称', '交易发生地',\r\n        '交易是否成功', '传票号', 'IP地址', 'MAC地址',\r\n        '对手交易余额', '交易流水号', '日志号', '凭证种类',\r\n        '凭证号', '交易柜员号', '备注', '查询反馈结果原因'\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    submitUpload() {\r\n      const files = this.fileList\r\n      if (files.length > 0) {\r\n        // this.$refs.upload.submit();  //自动上传整个文件列表\r\n        // 遍历所有文件\r\n        files.forEach(fileItem => {\r\n          const file = fileItem.raw // 获取每个文件\r\n          console.log(file.name)\r\n\r\n          const reader = new FileReader()\r\n          reader.onload = (e) => {\r\n            const arrayBuffer = e.target.result // 使用 ArrayBuffer\r\n            const workbook = XLSX.read(arrayBuffer, { type: 'array' })\r\n\r\n            // 只处理第一个工作表\r\n            const firstSheetName = workbook.SheetNames[0]\r\n            const worksheet = workbook.Sheets[firstSheetName]\r\n            // 将数据转为 JSON 格式\r\n            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })\r\n            // 检查表头\r\n            const headers = jsonData[0]\r\n            const isValid = this.validateHeaders(headers)\r\n\r\n            if (isValid) {\r\n              const fileName = file.name\r\n              const nameWithoutExt = fileName.replace(/\\.[^/.]+$/, '')\r\n              // 使用正则表达式去除数字\r\n              const validName = nameWithoutExt.replace(/[0-9]/g, '')\r\n              console.log(validName)\r\n\r\n              // 保存交易数据，第一列作为交易户名\r\n              const header = jsonData[0] // 获取表头\r\n              const expectedHeader = '交易户名'\r\n              var flag = 0\r\n              // 检查表头第一个元素是否为交易户名\r\n              if (header[0] !== expectedHeader) {\r\n                // 手动添加交易户名到表头\r\n                header.unshift(expectedHeader) // 在开头插入交易户名\r\n                flag = 1\r\n              }\r\n              // 保存交易数据，第一列作为交易户名\r\n              for (let i = 1; i < jsonData.length; i++) {\r\n                const row = jsonData[i]\r\n                var transaction = {}\r\n                if (row.length > 0) {\r\n                  if (flag === 1) {\r\n                    transaction = {\r\n                      交易户名: validName, // 文件名作为交易户名\r\n                      交易卡号: row[0] ? String(row[0]).replace(/\\t/g, '').trim() : '',\r\n                      交易账号: row[1] ? String(row[1]).replace(/\\t/g, '').trim() : '',\r\n                      交易时间: row[2] ? String(row[2]).replace(/\\t/g, '').trim() : '',\r\n                      交易金额: row[3] ? String(row[3]).replace(/\\t/g, '').trim() : '',\r\n                      交易余额: row[4] ? String(row[4]).replace(/\\t/g, '').trim() : '',\r\n                      收付标志: row[5] ? String(row[5]).replace(/\\t/g, '').trim() : '',\r\n                      对手账号: row[6] ? String(row[6]).replace(/\\t/g, '').trim() : '',\r\n                      现金标志: row[7] ? String(row[7]).replace(/\\t/g, '').trim() : '',\r\n                      对手户名: row[8] ? String(row[8]).replace(/\\t/g, '').trim() : '',\r\n                      对手身份证号: row[9] ? String(row[9]).replace(/\\t/g, '').trim() : '',\r\n                      对手开户银行: row[10] ? String(row[10]).replace(/\\t/g, '').trim() : '',\r\n                      摘要说明: row[11] ? String(row[11]).replace(/\\t/g, '').trim() : '',\r\n                      交易币种: row[12] ? String(row[12]).replace(/\\t/g, '').trim() : '',\r\n                      交易网点名称: row[13] ? String(row[13]).replace(/\\t/g, '').trim() : '',\r\n                      交易发生地: row[14] ? String(row[14]).replace(/\\t/g, '').trim() : '',\r\n                      交易是否成功: row[15] ? String(row[15]).replace(/\\t/g, '').trim() : '',\r\n                      传票号: row[16] ? String(row[16]).replace(/\\t/g, '').trim() : '',\r\n                      IP地址: row[17] ? String(row[17]).replace(/\\t/g, '').trim() : '',\r\n                      MAC地址: row[18] ? String(row[18]).replace(/\\t/g, '').trim() : '',\r\n                      对手交易余额: row[19] ? String(row[19]).replace(/\\t/g, '').trim() : '',\r\n                      交易流水号: row[20] ? String(row[20]).replace(/\\t/g, '').trim() : '',\r\n                      日志号: row[21] ? String(row[21]).replace(/\\t/g, '').trim() : '',\r\n                      凭证种类: row[22] ? String(row[22]).replace(/\\t/g, '').trim() : '',\r\n                      凭证号: row[23] ? String(row[23]).replace(/\\t/g, '').trim() : '',\r\n                      交易柜员号: row[24] ? String(row[24]).replace(/\\t/g, '').trim() : '',\r\n                      备注: row[25] ? String(row[25]).replace(/\\t/g, '').trim() : '',\r\n                      查询反馈结果原因: row[26] ? String(row[26]).replace(/\\t/g, '').trim() : ''\r\n                    }\r\n                  } else {\r\n                    transaction = {\r\n                      交易户名: row[0],\r\n                      交易卡号: row[1] ? String(row[1]).replace(/\\t/g, '').trim() : '',\r\n                      交易账号: row[2] ? String(row[2]).replace(/\\t/g, '').trim() : '',\r\n                      交易时间: row[3] ? String(row[3]).replace(/\\t/g, '').trim() : '',\r\n                      交易金额: row[4] ? String(row[4]).replace(/\\t/g, '').trim() : '',\r\n                      交易余额: row[5] ? String(row[5]).replace(/\\t/g, '').trim() : '',\r\n                      收付标志: row[6] ? String(row[6]).replace(/\\t/g, '').trim() : '',\r\n                      对手账号: row[7] ? String(row[7]).replace(/\\t/g, '').trim() : '',\r\n                      现金标志: row[8] ? String(row[8]).replace(/\\t/g, '').trim() : '',\r\n                      对手户名: row[9] ? String(row[9]).replace(/\\t/g, '').trim() : '',\r\n                      对手身份证号: row[10] ? String(row[10]).replace(/\\t/g, '').trim() : '',\r\n                      对手开户银行: row[11] ? String(row[11]).replace(/\\t/g, '').trim() : '',\r\n                      摘要说明: row[12] ? String(row[12]).replace(/\\t/g, '').trim() : '',\r\n                      交易币种: row[13] ? String(row[13]).replace(/\\t/g, '').trim() : '',\r\n                      交易网点名称: row[14] ? String(row[14]).replace(/\\t/g, '').trim() : '',\r\n                      交易发生地: row[15] ? String(row[15]).replace(/\\t/g, '').trim() : '',\r\n                      交易是否成功: row[16] ? String(row[16]).replace(/\\t/g, '').trim() : '',\r\n                      传票号: row[17] ? String(row[17]).replace(/\\t/g, '').trim() : '',\r\n                      IP地址: row[18] ? String(row[18]).replace(/\\t/g, '').trim() : '',\r\n                      MAC地址: row[19] ? String(row[19]).replace(/\\t/g, '').trim() : '',\r\n                      对手交易余额: row[20] ? String(row[20]).replace(/\\t/g, '').trim() : '',\r\n                      交易流水号: row[21] ? String(row[21]).replace(/\\t/g, '').trim() : '',\r\n                      日志号: row[22] ? String(row[22]).replace(/\\t/g, '').trim() : '',\r\n                      凭证种类: row[23] ? String(row[23]).replace(/\\t/g, '').trim() : '',\r\n                      凭证号: row[24] ? String(row[24]).replace(/\\t/g, '').trim() : '',\r\n                      交易柜员号: row[25] ? String(row[25]).replace(/\\t/g, '').trim() : '',\r\n                      备注: row[26] ? String(row[26]).replace(/\\t/g, '').trim() : '',\r\n                      查询反馈结果原因: row[27] ? String(row[27]).replace(/\\t/g, '').trim() : ''\r\n                    }\r\n                  }\r\n\r\n                  this.transactionData.push(transaction)\r\n                }\r\n              }\r\n              // console.log('交易数据:', this.transactionData);\r\n              this.fetchData()\r\n            } else {\r\n              this.$message.error(file.name + '的文件格式错误，请检查表头是否符合要求。')\r\n            }\r\n          }\r\n          reader.readAsArrayBuffer(file)\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '未上传任何文件',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    fetchData() {\r\n      const filenames = Array.from(this.fileList).map(file => file.name)\r\n      axios.post('http://127.0.0.1:8000/upload', { user: USER, filename: filenames, tableName: this.value, newMember: this.transactionData })\r\n        .then(response => {\r\n          const success_lines = response.data.success_lines\r\n          const fail_lines = response.data.fail_lines\r\n          const repeat_members = response.data.intersection\r\n          // 原来替代再往下一行的<pre style=\"text-align: left; background: #f5f7fa; padding: 15px; border-radius: 4px;\">\r\n          this.$alert(`\r\n              <pre style=\"white-space: pre-wrap; text-align: left; background: #f5f7fa; \r\n               padding: 15px; border-radius: 4px; max-height: 400px; overflow-y: auto;\">\r\n符合要求的文件上传成功！\r\n成功行数: ${success_lines}\r\n失败行数: ${fail_lines}\r\n重复对象: ${repeat_members}\r\n              </pre>\r\n            `, {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定',\r\n            center: true,\r\n            // closeOnClickModal: true,\r\n            lockScroll: true,\r\n            showClose: true,\r\n            customClass: 'code-alert'\r\n          })\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    },\r\n    validateHeaders(headers) { // 检查表头是否符合要求\r\n      // 去除表头中的制表符和多余的空格\r\n      headers = headers.map(header => header.replace(/\\t/g, '').trim())\r\n      const cleanHeaders = [...headers]\r\n      // 检查表头的第一个元素是否为 \"交易户名\"\r\n      if (cleanHeaders[0] !== '交易户名') {\r\n        cleanHeaders.unshift('交易户名') // 如果不是，手动添加 \"交易户名\" 到开头\r\n      }\r\n      // console.log(JSON.stringify(cleanHeaders))\r\n      // console.log(JSON.stringify(this.requiredHeaders))\r\n      return JSON.stringify(cleanHeaders) === JSON.stringify(this.requiredHeaders)\r\n    },\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList // 更新 fileList\r\n    },\r\n    handleRemove(file, fileList) {\r\n      this.fileList = fileList\r\n      console.log(file, fileList)\r\n    },\r\n    handlePreview(file) {\r\n      console.log(file)\r\n    },\r\n    handleSuccess(response, file) {\r\n      console.log('上传成功:', response)\r\n    },\r\n    handleError(err, file) {\r\n      console.error('上传失败，请重新上传符合条件的文件格式:', err)\r\n    },\r\n    DownloadModel() {\r\n      const link = document.createElement('a')\r\n      link.href = '模板.xlsx'\r\n      link.download = '模板.xlsx' // 指定下载的文件名\r\n      document.body.appendChild(link)\r\n      link.click()\r\n      document.body.removeChild(link)\r\n    },\r\n    handleSearch() {\r\n      // 发送交易数据到后端\r\n      axios.get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.upload-demo {\r\n  margin: 20px; /* 增加整体边距 */\r\n}\r\n\r\n.el-upload {\r\n  margin-bottom: 20px; /* 上传组件下方边距 */\r\n}\r\n\r\n.el-button {\r\n  margin-top: 10px; /* 上传按钮上方边距 */\r\n}\r\n\r\n.el-upload__tip {\r\n  margin-top: 10px; /* 提示文本上方边距 */\r\n}\r\n\r\n.upload-container {\r\n  margin-right: 40px; /* 添加右侧间距 */\r\n}\r\n</style>\n"]}]}