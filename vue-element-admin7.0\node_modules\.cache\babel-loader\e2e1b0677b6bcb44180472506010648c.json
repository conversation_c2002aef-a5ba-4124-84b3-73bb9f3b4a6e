{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\redirect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\redirect\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzJCRyb3V0ZSA9IHRoaXMuJHJvdXRlLAogICAgICBwYXJhbXMgPSBfdGhpcyQkcm91dGUucGFyYW1zLAogICAgICBxdWVyeSA9IF90aGlzJCRyb3V0ZS5xdWVyeTsKICAgIHZhciBwYXRoID0gcGFyYW1zLnBhdGg7CiAgICB0aGlzLiRyb3V0ZXIucmVwbGFjZSh7CiAgICAgIHBhdGg6ICcvJyArIHBhdGgsCiAgICAgIHF1ZXJ5OiBxdWVyeQogICAgfSk7CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICByZXR1cm4gaCgpOyAvLyBhdm9pZCB3YXJuaW5nIG1lc3NhZ2UKICB9Cn07"}, {"version": 3, "names": ["created", "_this$$route", "$route", "params", "query", "path", "$router", "replace", "render", "h"], "sources": ["src/views/redirect/index.vue"], "sourcesContent": ["<script>\nexport default {\n  created() {\n    const { params, query } = this.$route\n    const { path } = params\n    this.$router.replace({ path: '/' + path, query })\n  },\n  render: function(h) {\n    return h() // avoid warning message\n  }\n}\n</script>\n"], "mappings": ";;AACA;EACAA,OAAA,WAAAA,QAAA;IACA,IAAAC,YAAA,QAAAC,MAAA;MAAAC,MAAA,GAAAF,YAAA,CAAAE,MAAA;MAAAC,KAAA,GAAAH,YAAA,CAAAG,KAAA;IACA,IAAAC,IAAA,GAAAF,MAAA,CAAAE,IAAA;IACA,KAAAC,OAAA,CAAAC,OAAA;MAAAF,IAAA,QAAAA,IAAA;MAAAD,KAAA,EAAAA;IAAA;EACA;EACAI,MAAA,WAAAA,OAAAC,CAAA;IACA,OAAAA,CAAA;EACA;AACA", "ignoreList": []}]}