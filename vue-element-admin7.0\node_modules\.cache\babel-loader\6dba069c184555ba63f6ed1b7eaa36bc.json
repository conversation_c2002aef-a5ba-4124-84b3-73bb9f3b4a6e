{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue?vue&type=template&id=126b135a&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue", "mtime": 1747749632954}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749148885234}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJkcmF3ZXItY29udGFpbmVyIgogIH0sIFtfYygiZGl2IiwgW19jKCJoMyIsIHsKICAgIHN0YXRpY0NsYXNzOiAiZHJhd2VyLXRpdGxlIgogIH0sIFtfdm0uX3YoIumhtemdouagt+W8j+iuvue9riIpXSksIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImRyYXdlci1pdGVtIgogIH0sIFtfYygic3BhbiIsIFtfdm0uX3YoIuW8gOWQr+agh+etvumhtemdouWPr+ingSIpXSksIF9jKCJlbC1zd2l0Y2giLCB7CiAgICBzdGF0aWNDbGFzczogImRyYXdlci1zd2l0Y2giLAogICAgbW9kZWw6IHsKICAgICAgdmFsdWU6IF92bS50YWdzVmlldywKICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKCQkdikgewogICAgICAgIF92bS50YWdzVmlldyA9ICQkdjsKICAgICAgfSwKICAgICAgZXhwcmVzc2lvbjogInRhZ3NWaWV3IgogICAgfQogIH0pXSwgMSksIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImRyYXdlci1pdGVtIgogIH0sIFtfYygic3BhbiIsIFtfdm0uX3YoIuWbuuWumuagh+etvuWktCIpXSksIF9jKCJlbC1zd2l0Y2giLCB7CiAgICBzdGF0aWNDbGFzczogImRyYXdlci1zd2l0Y2giLAogICAgbW9kZWw6IHsKICAgICAgdmFsdWU6IF92bS5maXhlZEhlYWRlciwKICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKCQkdikgewogICAgICAgIF92bS5maXhlZEhlYWRlciA9ICQkdjsKICAgICAgfSwKICAgICAgZXhwcmVzc2lvbjogImZpeGVkSGVhZGVyIgogICAgfQogIH0pXSwgMSksIF9jKCJkaXYiLCB7CiAgICBzdGF0aWNDbGFzczogImRyYXdlci1pdGVtIgogIH0sIFtfYygic3BhbiIsIFtfdm0uX3YoIuS+p+i+ueagj+eahOagh+W/l+WPr+ingSIpXSksIF9jKCJlbC1zd2l0Y2giLCB7CiAgICBzdGF0aWNDbGFzczogImRyYXdlci1zd2l0Y2giLAogICAgbW9kZWw6IHsKICAgICAgdmFsdWU6IF92bS5zaWRlYmFyTG9nbywKICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKCQkdikgewogICAgICAgIF92bS5zaWRlYmFyTG9nbyA9ICQkdjsKICAgICAgfSwKICAgICAgZXhwcmVzc2lvbjogInNpZGViYXJMb2dvIgogICAgfQogIH0pXSwgMSldKV0pOwp9Owp2YXIgc3RhdGljUmVuZGVyRm5zID0gW107CnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZTsKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfTs="}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "model", "value", "tagsView", "callback", "$$v", "expression", "fixedHeader", "sidebarLogo", "staticRenderFns", "_withStripped"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/layout/components/Settings/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"drawer-container\" }, [\n    _c(\"div\", [\n      _c(\"h3\", { staticClass: \"drawer-title\" }, [_vm._v(\"页面样式设置\")]),\n      _c(\n        \"div\",\n        { staticClass: \"drawer-item\" },\n        [\n          _c(\"span\", [_vm._v(\"开启标签页面可见\")]),\n          _c(\"el-switch\", {\n            staticClass: \"drawer-switch\",\n            model: {\n              value: _vm.tagsView,\n              callback: function ($$v) {\n                _vm.tagsView = $$v\n              },\n              expression: \"tagsView\",\n            },\n          }),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"drawer-item\" },\n        [\n          _c(\"span\", [_vm._v(\"固定标签头\")]),\n          _c(\"el-switch\", {\n            staticClass: \"drawer-switch\",\n            model: {\n              value: _vm.fixedHeader,\n              callback: function ($$v) {\n                _vm.fixedHeader = $$v\n              },\n              expression: \"fixedHeader\",\n            },\n          }),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"drawer-item\" },\n        [\n          _c(\"span\", [_vm._v(\"侧边栏的标志可见\")]),\n          _c(\"el-switch\", {\n            staticClass: \"drawer-switch\",\n            model: {\n              value: _vm.sidebarLogo,\n              callback: function ($$v) {\n                _vm.sidebarLogo = $$v\n              },\n              expression: \"sidebarLogo\",\n            },\n          }),\n        ],\n        1\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CACpDF,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAChCH,EAAE,CAAC,WAAW,EAAE;IACdE,WAAW,EAAE,eAAe;IAC5BE,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACO,QAAQ;MACnBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACO,QAAQ,GAAGE,GAAG;MACpB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7BH,EAAE,CAAC,WAAW,EAAE;IACdE,WAAW,EAAE,eAAe;IAC5BE,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACW,WAAW;MACtBH,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACW,WAAW,GAAGF,GAAG;MACvB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAChCH,EAAE,CAAC,WAAW,EAAE;IACdE,WAAW,EAAE,eAAe;IAC5BE,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACY,WAAW;MACtBJ,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACY,WAAW,GAAGH,GAAG;MACvB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIG,eAAe,GAAG,EAAE;AACxBd,MAAM,CAACe,aAAa,GAAG,IAAI;AAE3B,SAASf,MAAM,EAAEc,eAAe", "ignoreList": []}]}