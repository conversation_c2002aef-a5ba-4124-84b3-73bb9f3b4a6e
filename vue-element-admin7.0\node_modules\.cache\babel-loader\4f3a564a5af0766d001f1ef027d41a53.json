{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\api\\role.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\api\\role.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKZXhwb3J0IGZ1bmN0aW9uIGdldFJvdXRlcygpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvdnVlLWVsZW1lbnQtYWRtaW4vcm91dGVzJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gZ2V0Um9sZXMoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Z1ZS1lbGVtZW50LWFkbWluL3JvbGVzJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gYWRkUm9sZShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3Z1ZS1lbGVtZW50LWFkbWluL3JvbGUnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZVJvbGUoaWQsIGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICIvdnVlLWVsZW1lbnQtYWRtaW4vcm9sZS8iLmNvbmNhdChpZCksCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CmV4cG9ydCBmdW5jdGlvbiBkZWxldGVSb2xlKGlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAiL3Z1ZS1lbGVtZW50LWFkbWluL3JvbGUvIi5jb25jYXQoaWQpLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["request", "getRoutes", "url", "method", "getRoles", "addRole", "data", "updateRole", "id", "concat", "deleteRole"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/api/role.js"], "sourcesContent": ["import request from '@/utils/request'\n\nexport function getRoutes() {\n  return request({\n    url: '/vue-element-admin/routes',\n    method: 'get'\n  })\n}\n\nexport function getRoles() {\n  return request({\n    url: '/vue-element-admin/roles',\n    method: 'get'\n  })\n}\n\nexport function addRole(data) {\n  return request({\n    url: '/vue-element-admin/role',\n    method: 'post',\n    data\n  })\n}\n\nexport function updateRole(id, data) {\n  return request({\n    url: `/vue-element-admin/role/${id}`,\n    method: 'put',\n    data\n  })\n}\n\nexport function deleteRole(id) {\n  return request({\n    url: `/vue-element-admin/role/${id}`,\n    method: 'delete'\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAOJ,OAAO,CAAC;IACbE,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASE,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAON,OAAO,CAAC;IACbE,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASC,UAAUA,CAACC,EAAE,EAAEF,IAAI,EAAE;EACnC,OAAON,OAAO,CAAC;IACbE,GAAG,6BAAAO,MAAA,CAA6BD,EAAE,CAAE;IACpCL,MAAM,EAAE,KAAK;IACbG,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASI,UAAUA,CAACF,EAAE,EAAE;EAC7B,OAAOR,OAAO,CAAC;IACbE,GAAG,6BAAAO,MAAA,CAA6BD,EAAE,CAAE;IACpCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}