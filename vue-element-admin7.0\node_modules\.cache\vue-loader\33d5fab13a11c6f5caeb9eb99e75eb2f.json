{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1747749564455}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBQYW5lbEdyb3VwIGZyb20gJy4vY29tcG9uZW50cy9QYW5lbEdyb3VwJwppbXBvcnQgTGluZUNoYXJ0IGZyb20gJy4vY29tcG9uZW50cy9MaW5lQ2hhcnQnCgpjb25zdCBsaW5lQ2hhcnREYXRhID0gewogIG5ld1Zpc2l0aXM6IHsKICAgIGV4cGVjdGVkRGF0YTogWzEwMCwgMTIwLCAxNjEsIDEzNCwgMTA1LCAxNjAsIDE2NV0sCiAgICBhY3R1YWxEYXRhOiBbMTIwLCA4MiwgOTEsIDE1NCwgMTYyLCAxNDAsIDE0NV0KICB9LAogIG1lc3NhZ2VzOiB7CiAgICBleHBlY3RlZERhdGE6IFsyMDAsIDE5MiwgMTIwLCAxNDQsIDE2MCwgMTMwLCAxNDBdLAogICAgYWN0dWFsRGF0YTogWzE4MCwgMTYwLCAxNTEsIDEwNiwgMTQ1LCAxNTAsIDEzMF0KICB9LAogIHB1cmNoYXNlczogewogICAgZXhwZWN0ZWREYXRhOiBbODAsIDEwMCwgMTIxLCAxMDQsIDEwNSwgOTAsIDEwMF0sCiAgICBhY3R1YWxEYXRhOiBbMTIwLCA5MCwgMTAwLCAxMzgsIDE0MiwgMTMwLCAxMzBdCiAgfSwKICBzaG9wcGluZ3M6IHsKICAgIGV4cGVjdGVkRGF0YTogWzEzMCwgMTQwLCAxNDEsIDE0MiwgMTQ1LCAxNTAsIDE2MF0sCiAgICBhY3R1YWxEYXRhOiBbMTIwLCA4MiwgOTEsIDE1NCwgMTYyLCAxNDAsIDEzMF0KICB9Cn0KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGFzaGJvYXJkQWRtaW4nLAogIGNvbXBvbmVudHM6IHsKICAgIFBhbmVsR3JvdXAsCiAgICBMaW5lQ2hhcnQKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsaW5lQ2hhcnREYXRhOiBsaW5lQ2hhcnREYXRhLm5ld1Zpc2l0aXMKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNldExpbmVDaGFydERhdGEodHlwZSkgewogICAgICB0aGlzLmxpbmVDaGFydERhdGEgPSBsaW5lQ2hhcnREYXRhW3R5cGVdCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0CA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard/admin", "sourcesContent": ["<template>\n  <div class=\"dashboard-editor-container\">\n\n    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />\n\n    <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\n      <line-chart :chart-data=\"lineChartData\" />\n    </el-row>\n\n    <!-- <el-row :gutter=\"32\">\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <raddar-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <pie-chart />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\n        <div class=\"chart-wrapper\">\n          <bar-chart />\n        </div>\n      </el-col>\n    </el-row> -->\n\n    <!-- <el-row :gutter=\"8\">\n      <el-col :xs=\"{span: 24}\" :sm=\"{span: 24}\" :md=\"{span: 24}\" :lg=\"{span: 12}\" :xl=\"{span: 12}\" style=\"padding-right:8px;margin-bottom:30px;\">\n        <transaction-table />\n      </el-col>\n      <el-col :xs=\"{span: 24}\" :sm=\"{span: 12}\" :md=\"{span: 12}\" :lg=\"{span: 6}\" :xl=\"{span: 6}\" style=\"margin-bottom:30px;\">\n        <todo-list />\n      </el-col>\n      <el-col :xs=\"{span: 24}\" :sm=\"{span: 12}\" :md=\"{span: 12}\" :lg=\"{span: 6}\" :xl=\"{span: 6}\" style=\"margin-bottom:30px;\">\n        <box-card />\n      </el-col> -->\n    <!-- </el-row> -->\n  </div>\n</template>\n\n<script>\nimport PanelGroup from './components/PanelGroup'\nimport LineChart from './components/LineChart'\n\nconst lineChartData = {\n  newVisitis: {\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\n    actualData: [120, 82, 91, 154, 162, 140, 145]\n  },\n  messages: {\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\n    actualData: [180, 160, 151, 106, 145, 150, 130]\n  },\n  purchases: {\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\n    actualData: [120, 90, 100, 138, 142, 130, 130]\n  },\n  shoppings: {\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\n    actualData: [120, 82, 91, 154, 162, 140, 130]\n  }\n}\n\nexport default {\n  name: 'DashboardAdmin',\n  components: {\n    PanelGroup,\n    LineChart\n  },\n  data() {\n    return {\n      lineChartData: lineChartData.newVisitis\n    }\n  },\n  methods: {\n    handleSetLineChartData(type) {\n      this.lineChartData = lineChartData[type]\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-editor-container {\n  padding: 32px;\n  background-color: rgb(240, 242, 245);\n  position: relative;\n\n  .chart-wrapper {\n    background: #fff;\n    padding: 16px 16px 0;\n    margin-bottom: 32px;\n  }\n}\n\n@media (max-width:1024px) {\n  .chart-wrapper {\n    padding: 8px;\n  }\n}\n</style>\n"]}]}