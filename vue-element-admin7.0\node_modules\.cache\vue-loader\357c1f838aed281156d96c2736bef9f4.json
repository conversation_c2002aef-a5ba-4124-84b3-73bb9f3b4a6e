{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\directive.vue?vue&type=style&index=0&id=3a6147a9&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\permission\\directive.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hcHAtY29udGFpbmVyIHsKICA6OnYtZGVlcCAucGVybWlzc2lvbi1hbGVydCB7CiAgICB3aWR0aDogMzIwcHg7CiAgICBtYXJnaW4tdG9wOiAxNXB4OwogICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjllYjsKICAgIGNvbG9yOiAjNjdjMjNhOwogICAgcGFkZGluZzogOHB4IDE2cHg7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgfQogIDo6di1kZWVwIC5wZXJtaXNzaW9uLXNvdXJjZUNvZGUgewogICAgbWFyZ2luLWxlZnQ6IDE1cHg7CiAgfQogIDo6di1kZWVwIC5wZXJtaXNzaW9uLXRhZyB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNmNWZmOwogIH0KfQo="}, {"version": 3, "sources": ["directive.vue"], "names": [], "mappings": ";AA4FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "directive.vue", "sourceRoot": "src/views/permission", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <switch-roles @change=\"handleRolesChange\" />\n    <div :key=\"key\" style=\"margin-top:30px;\">\n      <div>\n        <span v-permission=\"['admin']\" class=\"permission-alert\">\n          Only\n          <el-tag class=\"permission-tag\" size=\"small\">admin</el-tag> can see this\n        </span>\n        <el-tag v-permission=\"['admin']\" class=\"permission-sourceCode\" type=\"info\">\n          v-permission=\"['admin']\"\n        </el-tag>\n      </div>\n\n      <div>\n        <span v-permission=\"['editor']\" class=\"permission-alert\">\n          Only\n          <el-tag class=\"permission-tag\" size=\"small\">editor</el-tag> can see this\n        </span>\n        <el-tag v-permission=\"['editor']\" class=\"permission-sourceCode\" type=\"info\">\n          v-permission=\"['editor']\"\n        </el-tag>\n      </div>\n\n      <div>\n        <span v-permission=\"['admin','editor']\" class=\"permission-alert\">\n          Both\n          <el-tag class=\"permission-tag\" size=\"small\">admin</el-tag> and\n          <el-tag class=\"permission-tag\" size=\"small\">editor</el-tag> can see this\n        </span>\n        <el-tag v-permission=\"['admin','editor']\" class=\"permission-sourceCode\" type=\"info\">\n          v-permission=\"['admin','editor']\"\n        </el-tag>\n      </div>\n    </div>\n\n    <div :key=\"'checkPermission'+key\" style=\"margin-top:60px;\">\n      <aside>\n        In some cases, using v-permission will have no effect. For example: Element-UI's Tab component or el-table-column and other scenes that dynamically render dom. You can only do this with v-if.\n        <br> e.g.\n      </aside>\n\n      <el-tabs type=\"border-card\" style=\"width:550px;\">\n        <el-tab-pane v-if=\"checkPermission(['admin'])\" label=\"Admin\">\n          Admin can see this\n          <el-tag class=\"permission-sourceCode\" type=\"info\">\n            v-if=\"checkPermission(['admin'])\"\n          </el-tag>\n        </el-tab-pane>\n\n        <el-tab-pane v-if=\"checkPermission(['editor'])\" label=\"Editor\">\n          Editor can see this\n          <el-tag class=\"permission-sourceCode\" type=\"info\">\n            v-if=\"checkPermission(['editor'])\"\n          </el-tag>\n        </el-tab-pane>\n\n        <el-tab-pane v-if=\"checkPermission(['admin','editor'])\" label=\"Admin-OR-Editor\">\n          Both admin or editor can see this\n          <el-tag class=\"permission-sourceCode\" type=\"info\">\n            v-if=\"checkPermission(['admin','editor'])\"\n          </el-tag>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n  </div>\n</template>\n\n<script>\nimport permission from '@/directive/permission/index.js' // 权限判断指令\nimport checkPermission from '@/utils/permission' // 权限判断函数\nimport SwitchRoles from './components/SwitchRoles'\n\nexport default {\n  name: 'DirectivePermission',\n  components: { SwitchRoles },\n  directives: { permission },\n  data() {\n    return {\n      key: 1 // 为了能每次切换权限的时候重新初始化指令\n    }\n  },\n  methods: {\n    checkPermission,\n    handleRolesChange() {\n      this.key++\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  ::v-deep .permission-alert {\n    width: 320px;\n    margin-top: 15px;\n    background-color: #f0f9eb;\n    color: #67c23a;\n    padding: 8px 16px;\n    border-radius: 4px;\n    display: inline-block;\n  }\n  ::v-deep .permission-sourceCode {\n    margin-left: 15px;\n  }\n  ::v-deep .permission-tag {\n    background-color: #ecf5ff;\n  }\n}\n</style>\n\n"]}]}