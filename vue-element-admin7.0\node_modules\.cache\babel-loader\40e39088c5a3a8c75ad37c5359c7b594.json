{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\request.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\utils\\request.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\eslint-loader\\index.js", "mtime": 1749148887281}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "MessageBox", "Message", "store", "getToken", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "getters", "token", "headers", "error", "console", "log", "Promise", "reject", "response", "res", "data", "code", "message", "type", "duration", "confirm", "confirmButtonText", "cancelButtonText", "then", "dispatch", "location", "reload", "Error"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\nimport { MessageBox, Message } from 'element-ui'\nimport store from '@/store'\nimport { getToken } from '@/utils/auth'\n\n// create an axios instance\nconst service = axios.create({\n  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url\n  // withCredentials: true, // send cookies when cross-domain requests\n  timeout: 5000 // request timeout\n})\n\n// request interceptor\nservice.interceptors.request.use(\n  config => {\n    // do something before request is sent\n\n    if (store.getters.token) {\n      // let each request carry token\n      // ['X-Token'] is a custom headers key\n      // please modify it according to the actual situation\n      config.headers['X-Token'] = getToken()\n    }\n    return config\n  },\n  error => {\n    // do something with request error\n    console.log(error) // for debug\n    return Promise.reject(error)\n  }\n)\n\n// response interceptor\nservice.interceptors.response.use(\n  /**\n   * If you want to get http information such as headers or status\n   * Please return  response => response\n  */\n\n  /**\n   * Determine the request status by custom code\n   * Here is just an example\n   * You can also judge the status by HTTP Status Code\n   */\n  response => {\n    const res = response.data\n\n    // if the custom code is not 20000, it is judged as an error.\n    if (res.code !== 20000) {\n      Message({\n        message: res.message || 'Error',\n        type: 'error',\n        duration: 5 * 1000\n      })\n\n      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;\n      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {\n        // to re-login\n        MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {\n          confirmButtonText: 'Re-Login',\n          cancelButtonText: 'Cancel',\n          type: 'warning'\n        }).then(() => {\n          store.dispatch('user/resetToken').then(() => {\n            location.reload()\n          })\n        })\n      }\n      return Promise.reject(new Error(res.message || 'Error'))\n    } else {\n      return res\n    }\n  },\n  error => {\n    console.log('err' + error) // for debug\n    Message({\n      message: error.message,\n      type: 'error',\n      duration: 5 * 1000\n    })\n    return Promise.reject(error)\n  }\n)\n\nexport default service\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,QAAQ,YAAY;AAChD,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;;AAEvC;AACA,IAAMC,OAAO,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EAAE;EACvC;EACAC,OAAO,EAAE,IAAI,CAAC;AAChB,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAAAC,MAAM,EAAI;EACR;;EAEA,IAAIZ,KAAK,CAACa,OAAO,CAACC,KAAK,EAAE;IACvB;IACA;IACA;IACAF,MAAM,CAACG,OAAO,CAAC,SAAS,CAAC,GAAGd,QAAQ,CAAC,CAAC;EACxC;EACA,OAAOW,MAAM;AACf,CAAC,EACD,UAAAI,KAAK,EAAI;EACP;EACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAC;EACnB,OAAOG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,OAAO,CAACO,YAAY,CAACY,QAAQ,CAACV,GAAG;AAC/B;AACF;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACE,UAAAU,QAAQ,EAAI;EACV,IAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEzB;EACA,IAAID,GAAG,CAACE,IAAI,KAAK,KAAK,EAAE;IACtBzB,OAAO,CAAC;MACN0B,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI,OAAO;MAC/BC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;;IAEF;IACA,IAAIL,GAAG,CAACE,IAAI,KAAK,KAAK,IAAIF,GAAG,CAACE,IAAI,KAAK,KAAK,IAAIF,GAAG,CAACE,IAAI,KAAK,KAAK,EAAE;MAClE;MACA1B,UAAU,CAAC8B,OAAO,CAAC,gFAAgF,EAAE,gBAAgB,EAAE;QACrHC,iBAAiB,EAAE,UAAU;QAC7BC,gBAAgB,EAAE,QAAQ;QAC1BJ,IAAI,EAAE;MACR,CAAC,CAAC,CAACK,IAAI,CAAC,YAAM;QACZ/B,KAAK,CAACgC,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;UAC3CE,QAAQ,CAACC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,OAAOf,OAAO,CAACC,MAAM,CAAC,IAAIe,KAAK,CAACb,GAAG,CAACG,OAAO,IAAI,OAAO,CAAC,CAAC;EAC1D,CAAC,MAAM;IACL,OAAOH,GAAG;EACZ;AACF,CAAC,EACD,UAAAN,KAAK,EAAI;EACPC,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGF,KAAK,CAAC,EAAC;EAC3BjB,OAAO,CAAC;IACN0B,OAAO,EAAET,KAAK,CAACS,OAAO;IACtBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CAAC,GAAG;EAChB,CAAC,CAAC;EACF,OAAOR,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAed,OAAO", "ignoreList": []}]}