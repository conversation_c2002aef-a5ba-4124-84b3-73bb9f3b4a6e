{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\layout\\components\\Settings\\index.vue", "mtime": 1747749632954}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gaW1wb3J0IFRoZW1lUGlja2VyIGZyb20gJ0AvY29tcG9uZW50cy9UaGVtZVBpY2tlcicKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7fSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHt9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGZpeGVkSGVhZGVyOiB7CiAgICAgIGdldDogZnVuY3Rpb24gZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5maXhlZEhlYWRlcjsKICAgICAgfSwKICAgICAgc2V0OiBmdW5jdGlvbiBzZXQodmFsKSB7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7CiAgICAgICAgICBrZXk6ICdmaXhlZEhlYWRlcicsCiAgICAgICAgICB2YWx1ZTogdmFsCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICB0YWdzVmlldzogewogICAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHsKICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudGFnc1ZpZXc7CiAgICAgIH0sCiAgICAgIHNldDogZnVuY3Rpb24gc2V0KHZhbCkgewogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICAgICAga2V5OiAndGFnc1ZpZXcnLAogICAgICAgICAgdmFsdWU6IHZhbAogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgc2lkZWJhckxvZ286IHsKICAgICAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnNpZGViYXJMb2dvOwogICAgICB9LAogICAgICBzZXQ6IGZ1bmN0aW9uIHNldCh2YWwpIHsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICAgIGtleTogJ3NpZGViYXJMb2dvJywKICAgICAgICAgIHZhbHVlOiB2YWwKICAgICAgICB9KTsKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgdGhlbWVDaGFuZ2U6IGZ1bmN0aW9uIHRoZW1lQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsKICAgICAgICBrZXk6ICd0aGVtZScsCiAgICAgICAgdmFsdWU6IHZhbAogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["components", "data", "computed", "fixedHeader", "get", "$store", "state", "settings", "set", "val", "dispatch", "key", "value", "tagsView", "sidebarLogo", "methods", "themeChange"], "sources": ["src/layout/components/Settings/index.vue"], "sourcesContent": ["<template>\n  <div class=\"drawer-container\">\n    <div>\n      <h3 class=\"drawer-title\">页面样式设置</h3>\n\n      <!-- <div class=\"drawer-item\">\n        <span>Te Cohemlor</span>\n        <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n      </div> -->\n\n      <div class=\"drawer-item\">\n        <span>开启标签页面可见</span>\n        <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>固定标签头</span>\n        <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n      </div>\n\n      <div class=\"drawer-item\">\n        <span>侧边栏的标志可见</span>\n        <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n      </div>\n\n    </div>\n  </div>\n</template>\n\n<script>\n// import ThemePicker from '@/components/ThemePicker'\n\nexport default {\n  components: {},\n  data() {\n    return {}\n  },\n  computed: {\n    fixedHeader: {\n      get() {\n        return this.$store.state.settings.fixedHeader\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        })\n      }\n    },\n    tagsView: {\n      get() {\n        return this.$store.state.settings.tagsView\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        })\n      }\n    },\n    sidebarLogo: {\n      get() {\n        return this.$store.state.settings.sidebarLogo\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        })\n      }\n    }\n  },\n  methods: {\n    themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.drawer-container {\n  padding: 24px;\n  font-size: 14px;\n  line-height: 1.5;\n  word-wrap: break-word;\n\n  .drawer-title {\n    margin-bottom: 12px;\n    color: rgba(0, 0, 0, .85);\n    font-size: 14px;\n    line-height: 22px;\n  }\n\n  .drawer-item {\n    color: rgba(0, 0, 0, .65);\n    font-size: 14px;\n    padding: 12px 0;\n  }\n\n  .drawer-switch {\n    float: right\n  }\n}\n</style>\n"], "mappings": "AA8BA;;AAEA;EACAA,UAAA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAJ,WAAA;MACA;MACAK,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAJ,MAAA,CAAAK,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,QAAA;MACAT,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAM,QAAA;MACA;MACAL,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAJ,MAAA,CAAAK,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAK,WAAA;MACAV,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAO,WAAA;MACA;MACAN,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAJ,MAAA,CAAAK,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;EACA;EACAM,OAAA;IACAC,WAAA,WAAAA,YAAAP,GAAA;MACA,KAAAJ,MAAA,CAAAK,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;IACA;EACA;AACA", "ignoreList": []}]}