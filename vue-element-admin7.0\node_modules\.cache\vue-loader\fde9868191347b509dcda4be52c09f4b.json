{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\components\\SocialSignin.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\components\\SocialSignin.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIGltcG9ydCBvcGVuV2luZG93IGZyb20gJ0AvdXRpbHMvb3Blbi13aW5kb3cnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NvY2lhbFNpZ25pbicsCiAgbWV0aG9kczogewogICAgd2VjaGF0SGFuZGxlQ2xpY2sodGhpcmRwYXJ0KSB7CiAgICAgIGFsZXJ0KCdvaycpCiAgICAgIC8vIHRoaXMuJHN0b3JlLmNvbW1pdCgnU0VUX0FVVEhfVFlQRScsIHRoaXJkcGFydCkKICAgICAgLy8gY29uc3QgYXBwaWQgPSAneHh4eHgnCiAgICAgIC8vIGNvbnN0IHJlZGlyZWN0X3VyaSA9IGVuY29kZVVSSUNvbXBvbmVudCgneHh4L3JlZGlyZWN0P3JlZGlyZWN0PScgKyB3aW5kb3cubG9jYXRpb24ub3JpZ2luICsgJy9hdXRoLXJlZGlyZWN0JykKICAgICAgLy8gY29uc3QgdXJsID0gJ2h0dHBzOi8vb3Blbi53ZWl4aW4ucXEuY29tL2Nvbm5lY3QvcXJjb25uZWN0P2FwcGlkPScgKyBhcHBpZCArICcmcmVkaXJlY3RfdXJpPScgKyByZWRpcmVjdF91cmkgKyAnJnJlc3BvbnNlX3R5cGU9Y29kZSZzY29wZT1zbnNhcGlfbG9naW4jd2VjaGF0X3JlZGlyZWN0JwogICAgICAvLyBvcGVuV2luZG93KHVybCwgdGhpcmRwYXJ0LCA1NDAsIDU0MCkKICAgIH0sCiAgICB0ZW5jZW50SGFuZGxlQ2xpY2sodGhpcmRwYXJ0KSB7CiAgICAgIGFsZXJ0KCdvaycpCiAgICAgIC8vIHRoaXMuJHN0b3JlLmNvbW1pdCgnU0VUX0FVVEhfVFlQRScsIHRoaXJkcGFydCkKICAgICAgLy8gY29uc3QgY2xpZW50X2lkID0gJ3h4eHh4JwogICAgICAvLyBjb25zdCByZWRpcmVjdF91cmkgPSBlbmNvZGVVUklDb21wb25lbnQoJ3h4eC9yZWRpcmVjdD9yZWRpcmVjdD0nICsgd2luZG93LmxvY2F0aW9uLm9yaWdpbiArICcvYXV0aC1yZWRpcmVjdCcpCiAgICAgIC8vIGNvbnN0IHVybCA9ICdodHRwczovL2dyYXBoLnFxLmNvbS9vYXV0aDIuMC9hdXRob3JpemU/cmVzcG9uc2VfdHlwZT1jb2RlJmNsaWVudF9pZD0nICsgY2xpZW50X2lkICsgJyZyZWRpcmVjdF91cmk9JyArIHJlZGlyZWN0X3VyaQogICAgICAvLyBvcGVuV2luZG93KHVybCwgdGhpcmRwYXJ0LCA1NDAsIDU0MCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["SocialSignin.vue"], "names": [], "mappings": ";AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SocialSignin.vue", "sourceRoot": "src/views/login/components", "sourcesContent": ["<template>\n  <div class=\"social-signup-container\">\n    <div class=\"sign-btn\" @click=\"wechatHandleClick('wechat')\">\n      <span class=\"wx-svg-container\"><svg-icon icon-class=\"wechat\" class=\"icon\" /></span>\n      WeChat\n    </div>\n    <div class=\"sign-btn\" @click=\"tencentHandleClick('tencent')\">\n      <span class=\"qq-svg-container\"><svg-icon icon-class=\"qq\" class=\"icon\" /></span>\n      QQ\n    </div>\n  </div>\n</template>\n\n<script>\n// import openWindow from '@/utils/open-window'\n\nexport default {\n  name: 'SocialSignin',\n  methods: {\n    wechatHandleClick(thirdpart) {\n      alert('ok')\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\n      // const appid = 'xxxxx'\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\n      // const url = 'https://open.weixin.qq.com/connect/qrconnect?appid=' + appid + '&redirect_uri=' + redirect_uri + '&response_type=code&scope=snsapi_login#wechat_redirect'\n      // openWindow(url, thirdpart, 540, 540)\n    },\n    tencentHandleClick(thirdpart) {\n      alert('ok')\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\n      // const client_id = 'xxxxx'\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\n      // const url = 'https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=' + client_id + '&redirect_uri=' + redirect_uri\n      // openWindow(url, thirdpart, 540, 540)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .social-signup-container {\n    margin: 20px 0;\n    .sign-btn {\n      display: inline-block;\n      cursor: pointer;\n    }\n    .icon {\n      color: #fff;\n      font-size: 24px;\n      margin-top: 8px;\n    }\n    .wx-svg-container,\n    .qq-svg-container {\n      display: inline-block;\n      width: 40px;\n      height: 40px;\n      line-height: 40px;\n      text-align: center;\n      padding-top: 1px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n      margin-right: 5px;\n    }\n    .wx-svg-container {\n      background-color: #24da70;\n    }\n    .qq-svg-container {\n      background-color: #6BA2D6;\n      margin-left: 50px;\n    }\n  }\n</style>\n"]}]}