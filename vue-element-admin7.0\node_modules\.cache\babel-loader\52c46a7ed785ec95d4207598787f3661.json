{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js!D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\@babel\\runtime\\helpers\\esm\\arrayWithoutHoles.js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\@babel\\runtime\\helpers\\esm\\arrayWithoutHoles.js", "mtime": 1749148890535}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\babel.config.js", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGFycmF5TGlrZVRvQXJyYXkgZnJvbSAiLi9hcnJheUxpa2VUb0FycmF5LmpzIjsKZnVuY3Rpb24gX2FycmF5V2l0aG91dEhvbGVzKHIpIHsKICBpZiAoQXJyYXkuaXNBcnJheShyKSkgcmV0dXJuIGFycmF5TGlrZVRvQXJyYXkocik7Cn0KZXhwb3J0IHsgX2FycmF5V2l0aG91dEhvbGVzIGFzIGRlZmF1bHQgfTs="}, {"version": 3, "names": ["arrayLikeToArray", "_arrayWithoutHoles", "r", "Array", "isArray", "default"], "sources": ["D:/2025大创_地下田庄/vue-element-admin7.0/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js"], "sourcesContent": ["import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,kBAAkBA,CAACC,CAAC,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOF,gBAAgB,CAACE,CAAC,CAAC;AAClD;AACA,SAASD,kBAAkB,IAAII,OAAO", "ignoreList": []}]}