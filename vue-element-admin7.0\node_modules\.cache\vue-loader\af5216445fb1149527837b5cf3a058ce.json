{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\PanThumb\\index.vue?vue&type=style&index=0&id=175fbaac&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\PanThumb\\index.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/PanThumb", "sourcesContent": ["<template>\n  <div :style=\"{zIndex:zIndex,height:height,width:width}\" class=\"pan-item\">\n    <div class=\"pan-info\">\n      <div class=\"pan-info-roles-container\">\n        <slot />\n      </div>\n    </div>\n    <!-- eslint-disable-next-line -->\n    <div :style=\"{backgroundImage: `url(${image})`}\" class=\"pan-thumb\"></div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'PanThumb',\n  props: {\n    image: {\n      type: String,\n      required: true\n    },\n    zIndex: {\n      type: Number,\n      default: 1\n    },\n    width: {\n      type: String,\n      default: '150px'\n    },\n    height: {\n      type: String,\n      default: '150px'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pan-item {\n  width: 200px;\n  height: 200px;\n  border-radius: 50%;\n  display: inline-block;\n  position: relative;\n  cursor: default;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\n}\n\n.pan-info-roles-container {\n  padding: 20px;\n  text-align: center;\n}\n\n.pan-thumb {\n  width: 100%;\n  height: 100%;\n  background-position: center center;\n  background-size: cover;\n  border-radius: 50%;\n  overflow: hidden;\n  position: absolute;\n  transform-origin: 95% 40%;\n  transition: all 0.3s ease-in-out;\n}\n\n/* .pan-thumb:after {\n  content: '';\n  width: 8px;\n  height: 8px;\n  position: absolute;\n  border-radius: 50%;\n  top: 40%;\n  left: 95%;\n  margin: -4px 0 0 -4px;\n  background: radial-gradient(ellipse at center, rgba(14, 14, 14, 1) 0%, rgba(125, 126, 125, 1) 100%);\n  box-shadow: 0 0 1px rgba(255, 255, 255, 0.9);\n} */\n\n.pan-info {\n  position: absolute;\n  width: inherit;\n  height: inherit;\n  border-radius: 50%;\n  overflow: hidden;\n  box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0.05);\n}\n\n.pan-info h3 {\n  color: #fff;\n  text-transform: uppercase;\n  position: relative;\n  letter-spacing: 2px;\n  font-size: 18px;\n  margin: 0 60px;\n  padding: 22px 0 0 0;\n  height: 85px;\n  font-family: 'Open Sans', Arial, sans-serif;\n  text-shadow: 0 0 1px #fff, 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.pan-info p {\n  color: #fff;\n  padding: 10px 5px;\n  font-style: italic;\n  margin: 0 30px;\n  font-size: 12px;\n  border-top: 1px solid rgba(255, 255, 255, 0.5);\n}\n\n.pan-info p a {\n  display: block;\n  color: #333;\n  width: 80px;\n  height: 80px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  color: #fff;\n  font-style: normal;\n  font-weight: 700;\n  text-transform: uppercase;\n  font-size: 9px;\n  letter-spacing: 1px;\n  padding-top: 24px;\n  margin: 7px auto 0;\n  font-family: 'Open Sans', Arial, sans-serif;\n  opacity: 0;\n  transition: transform 0.3s ease-in-out 0.2s, opacity 0.3s ease-in-out 0.2s, background 0.2s linear 0s;\n  transform: translateX(60px) rotate(90deg);\n}\n\n.pan-info p a:hover {\n  background: rgba(255, 255, 255, 0.5);\n}\n\n.pan-item:hover .pan-thumb {\n  transform: rotate(-110deg);\n}\n\n.pan-item:hover .pan-info p a {\n  opacity: 1;\n  transform: translateX(0px) rotate(0deg);\n}\n</style>\n"]}]}