{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\SvgIcon\\index.vue", "mtime": 1731856724000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749148891391}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIGRvYzogaHR0cHM6Ly9wYW5qaWFjaGVuLmdpdGh1Yi5pby92dWUtZWxlbWVudC1hZG1pbi1zaXRlL2ZlYXR1cmUvY29tcG9uZW50L3N2Zy1pY29uLmh0bWwjdXNhZ2UKaW1wb3J0IHsgaXNFeHRlcm5hbCB9IGZyb20gJ0AvdXRpbHMvdmFsaWRhdGUnCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3ZnSWNvbicsCiAgcHJvcHM6IHsKICAgIGljb25DbGFzczogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2xhc3NOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gaXNFeHRlcm5hbCh0aGlzLmljb25DbGFzcykKICAgIH0sCiAgICBpY29uTmFtZSgpIHsKICAgICAgcmV0dXJuIGAjaWNvbi0ke3RoaXMuaWNvbkNsYXNzfWAKICAgIH0sCiAgICBzdmdDbGFzcygpIHsKICAgICAgaWYgKHRoaXMuY2xhc3NOYW1lKSB7CiAgICAgICAgcmV0dXJuICdzdmctaWNvbiAnICsgdGhpcy5jbGFzc05hbWUKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uJwogICAgICB9CiAgICB9LAogICAgc3R5bGVFeHRlcm5hbEljb24oKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgbWFzazogYHVybCgke3RoaXMuaWNvbkNsYXNzfSkgbm8tcmVwZWF0IDUwJSA1MCVgLAogICAgICAgICctd2Via2l0LW1hc2snOiBgdXJsKCR7dGhpcy5pY29uQ2xhc3N9KSBuby1yZXBlYXQgNTAlIDUwJWAKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SvgIcon", "sourcesContent": ["<template>\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\n// doc: https://panjiachen.github.io/vue-element-admin-site/feature/component/svg-icon.html#usage\nimport { isExternal } from '@/utils/validate'\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.iconClass)\n    },\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    },\n    styleExternalIcon() {\n      return {\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n.svg-external-icon {\n  background-color: currentColor;\n  mask-size: cover!important;\n  display: inline-block;\n}\n</style>\n"]}]}