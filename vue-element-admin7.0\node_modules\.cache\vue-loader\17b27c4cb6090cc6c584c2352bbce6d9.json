{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\error-page\\401.vue?vue&type=style&index=0&id=cbc28918&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\error-page\\401.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5lcnJQYWdlLWNvbnRhaW5lciB7CiAgd2lkdGg6IDgwMHB4OwogIG1heC13aWR0aDogMTAwJTsKICBtYXJnaW46IDEwMHB4IGF1dG87CiAgLnBhbi1iYWNrLWJ0biB7CiAgICBiYWNrZ3JvdW5kOiAjMDA4NDg5OwogICAgY29sb3I6ICNmZmY7CiAgICBib3JkZXI6IG5vbmUhaW1wb3J0YW50OwogIH0KICAucGFuLWdpZiB7CiAgICBtYXJnaW46IDAgYXV0bzsKICAgIGRpc3BsYXk6IGJsb2NrOwogIH0KICAucGFuLWltZyB7CiAgICBkaXNwbGF5OiBibG9jazsKICAgIG1hcmdpbjogMCBhdXRvOwogICAgd2lkdGg6IDEwMCU7CiAgfQogIC50ZXh0LWp1bWJvIHsKICAgIGZvbnQtc2l6ZTogNjBweDsKICAgIGZvbnQtd2VpZ2h0OiA3MDA7CiAgICBjb2xvcjogIzQ4NDg0ODsKICB9CiAgLmxpc3QtdW5zdHlsZWQgewogICAgZm9udC1zaXplOiAxNHB4OwogICAgbGkgewogICAgICBwYWRkaW5nLWJvdHRvbTogNXB4OwogICAgfQogICAgYSB7CiAgICAgIGNvbG9yOiAjMDA4NDg5OwogICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7CiAgICAgICY6aG92ZXIgewogICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["401.vue"], "names": [], "mappings": ";AAoGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "401.vue", "sourceRoot": "src/views/error-page", "sourcesContent": ["<template>\n  <div class=\"errPage-container\">\n    <el-button icon=\"el-icon-arrow-left\" class=\"pan-back-btn\" @click=\"back\">\n      返回\n    </el-button>\n    <el-row>\n      <el-col :span=\"12\">\n        <h1 class=\"text-jumbo text-ginormous\">\n          Oops!\n        </h1>\n        gif来源<a href=\"https://zh.airbnb.com/\" target=\"_blank\">airbnb</a> 页面\n        <h2>你没有权限去该页面</h2>\n        <h6>如有不满请联系你领导</h6>\n        <ul class=\"list-unstyled\">\n          <li>或者你可以去:</li>\n          <li class=\"link-type\">\n            <router-link to=\"/dashboard\">\n              回首页\n            </router-link>\n          </li>\n          <li class=\"link-type\">\n            <a href=\"https://www.taobao.com/\">随便看看</a>\n          </li>\n          <li><a href=\"#\" @click.prevent=\"dialogVisible=true\">点我看图</a></li>\n        </ul>\n      </el-col>\n      <el-col :span=\"12\">\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\n      </el-col>\n    </el-row>\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"随便看\">\n      <img :src=\"ewizardClap\" class=\"pan-img\">\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport errGif from '@/assets/401_images/401.gif'\n\nexport default {\n  name: 'Page401',\n  data() {\n    return {\n      errGif: errGif + '?' + +new Date(),\n      ewizardClap: 'https://wpimg.wallstcn.com/007ef517-bafd-4066-aae4-6883632d9646',\n      dialogVisible: false\n    }\n  },\n  methods: {\n    back() {\n      if (this.$route.query.noGoBack) {\n        this.$router.push({ path: '/dashboard' })\n      } else {\n        this.$router.go(-1)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .errPage-container {\n    width: 800px;\n    max-width: 100%;\n    margin: 100px auto;\n    .pan-back-btn {\n      background: #008489;\n      color: #fff;\n      border: none!important;\n    }\n    .pan-gif {\n      margin: 0 auto;\n      display: block;\n    }\n    .pan-img {\n      display: block;\n      margin: 0 auto;\n      width: 100%;\n    }\n    .text-jumbo {\n      font-size: 60px;\n      font-weight: 700;\n      color: #484848;\n    }\n    .list-unstyled {\n      font-size: 14px;\n      li {\n        padding-bottom: 5px;\n      }\n      a {\n        color: #008489;\n        text-decoration: none;\n        &:hover {\n          text-decoration: underline;\n        }\n      }\n    }\n  }\n</style>\n"]}]}