{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\update\\updateFile.vue?vue&type=style&index=0&id=8065da7e&scoped=true&lang=css", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\update\\updateFile.vue", "mtime": 1747748935259}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudXBsb2FkLWRlbW8gew0KICBtYXJnaW46IDIwcHg7IC8qIOWinuWKoOaVtOS9k+i+uei3nSAqLw0KfQ0KDQouZWwtdXBsb2FkIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsgLyog5LiK5Lyg57uE5Lu25LiL5pa56L656LedICovDQp9DQoNCi5lbC1idXR0b24gew0KICBtYXJnaW4tdG9wOiAxMHB4OyAvKiDkuIrkvKDmjInpkq7kuIrmlrnovrnot50gKi8NCn0NCg0KLmVsLXVwbG9hZF9fdGlwIHsNCiAgbWFyZ2luLXRvcDogMTBweDsgLyog5o+Q56S65paH5pys5LiK5pa56L656LedICovDQp9DQoNCi51cGxvYWQtY29udGFpbmVyIHsNCiAgbWFyZ2luLXJpZ2h0OiA0MHB4OyAvKiDmt7vliqDlj7Pkvqfpl7Tot50gKi8NCn0NCg=="}, {"version": 3, "sources": ["updateFile.vue"], "names": [], "mappings": ";AAmRA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "updateFile.vue", "sourceRoot": "src/views/update", "sourcesContent": ["<template>\r\n  <div style=\"margin-right: 40px\">\r\n    <el-select\r\n        v-model=\"value\"\r\n        placeholder=\"数据表\"\r\n        no-data-text=\"已经没有数据表了\"\r\n        style=\"margin-left: 20px;\"\r\n        @focus=\"handleSearch\"\r\n        @change=\"handleSelectChange\"\r\n    >\r\n        <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\n/>\r\n    </el-select>\r\n    <el-upload\r\n      ref=\"upload\"\r\n      class=\"upload-demo\"\r\n      action=\"http://localhost:8000/file\"\r\n      :on-preview=\"handlePreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-change=\"handleChange\"\r\n      :file-list=\"fileList\"\r\n      :auto-upload=\"false\"\r\n      :on-success=\"handleSuccess\"\r\n      :on-error=\"handleError\"\r\n      accept=\".xls,.xlsx\"\n>\r\n      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\r\n      <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"success\" @click=\"submitUpload\">导入数据</el-button>\r\n      <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"info\" @click=\"DownloadModel\">下载模板</el-button>\r\n      <div slot=\"tip\" class=\"el-upload__tip\">只能上传xls/xlsx文件</div>\r\n    </el-upload>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport * as XLSX from 'xlsx'\r\nimport { USER } from '../login/index.vue'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      options: [],\r\n      value: '', // 选中的数据表\r\n      fileList: [],\r\n      transactionData: [], // 用于存储交易数据\r\n      requiredHeaders: [\r\n        '交易户名', '交易卡号', '交易账号', '交易时间',\r\n        '交易金额', '交易余额', '收付标志', '对手账号',\r\n        '现金标志', '对手户名', '对手身份证号', '对手开户银行',\r\n        '摘要说明', '交易币种', '交易网点名称', '交易发生地',\r\n        '交易是否成功', '传票号', 'IP地址', 'MAC地址',\r\n        '对手交易余额', '交易流水号', '日志号', '凭证种类',\r\n        '凭证号', '交易柜员号', '备注', '查询反馈结果原因'\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    submitUpload() {\r\n      const files = this.fileList\r\n      if (files.length > 0) {\r\n        // this.$refs.upload.submit();  //自动上传整个文件列表\r\n        // 遍历所有文件\r\n        files.forEach(fileItem => {\r\n          const file = fileItem.raw // 获取每个文件\r\n          console.log(file.name)\r\n\r\n          const reader = new FileReader()\r\n          reader.onload = (e) => {\r\n            const arrayBuffer = e.target.result // 使用 ArrayBuffer\r\n            const workbook = XLSX.read(arrayBuffer, { type: 'array' })\r\n\r\n            // 只处理第一个工作表\r\n            const firstSheetName = workbook.SheetNames[0]\r\n            const worksheet = workbook.Sheets[firstSheetName]\r\n            // 将数据转为 JSON 格式\r\n            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })\r\n            // 检查表头\r\n            const headers = jsonData[0]\r\n            const isValid = this.validateHeaders(headers)\r\n\r\n            if (isValid) {\r\n              const fileName = file.name\r\n              const nameWithoutExt = fileName.replace(/\\.[^/.]+$/, '')\r\n              // 使用正则表达式去除数字\r\n              const validName = nameWithoutExt.replace(/[0-9]/g, '')\r\n              console.log(validName)\r\n\r\n              // 保存交易数据，第一列作为交易户名\r\n              const header = jsonData[0] // 获取表头\r\n              const expectedHeader = '交易户名'\r\n              var flag = 0\r\n              // 检查表头第一个元素是否为交易户名\r\n              if (header[0] !== expectedHeader) {\r\n                // 手动添加交易户名到表头\r\n                header.unshift(expectedHeader) // 在开头插入交易户名\r\n                flag = 1\r\n              }\r\n              // 保存交易数据，第一列作为交易户名\r\n              for (let i = 1; i < jsonData.length; i++) {\r\n                const row = jsonData[i]\r\n                var transaction = {}\r\n                if (row.length > 0) {\r\n                  if (flag === 1) {\r\n                    transaction = {\r\n                      交易户名: validName, // 文件名作为交易户名\r\n                      交易卡号: row[0] ? String(row[0]).replace(/\\t/g, '').trim() : '',\r\n                      交易账号: row[1] ? String(row[1]).replace(/\\t/g, '').trim() : '',\r\n                      交易时间: row[2] ? String(row[2]).replace(/\\t/g, '').trim() : '',\r\n                      交易金额: row[3] ? String(row[3]).replace(/\\t/g, '').trim() : '',\r\n                      交易余额: row[4] ? String(row[4]).replace(/\\t/g, '').trim() : '',\r\n                      收付标志: row[5] ? String(row[5]).replace(/\\t/g, '').trim() : '',\r\n                      对手账号: row[6] ? String(row[6]).replace(/\\t/g, '').trim() : '',\r\n                      现金标志: row[7] ? String(row[7]).replace(/\\t/g, '').trim() : '',\r\n                      对手户名: row[8] ? String(row[8]).replace(/\\t/g, '').trim() : '',\r\n                      对手身份证号: row[9] ? String(row[9]).replace(/\\t/g, '').trim() : '',\r\n                      对手开户银行: row[10] ? String(row[10]).replace(/\\t/g, '').trim() : '',\r\n                      摘要说明: row[11] ? String(row[11]).replace(/\\t/g, '').trim() : '',\r\n                      交易币种: row[12] ? String(row[12]).replace(/\\t/g, '').trim() : '',\r\n                      交易网点名称: row[13] ? String(row[13]).replace(/\\t/g, '').trim() : '',\r\n                      交易发生地: row[14] ? String(row[14]).replace(/\\t/g, '').trim() : '',\r\n                      交易是否成功: row[15] ? String(row[15]).replace(/\\t/g, '').trim() : '',\r\n                      传票号: row[16] ? String(row[16]).replace(/\\t/g, '').trim() : '',\r\n                      IP地址: row[17] ? String(row[17]).replace(/\\t/g, '').trim() : '',\r\n                      MAC地址: row[18] ? String(row[18]).replace(/\\t/g, '').trim() : '',\r\n                      对手交易余额: row[19] ? String(row[19]).replace(/\\t/g, '').trim() : '',\r\n                      交易流水号: row[20] ? String(row[20]).replace(/\\t/g, '').trim() : '',\r\n                      日志号: row[21] ? String(row[21]).replace(/\\t/g, '').trim() : '',\r\n                      凭证种类: row[22] ? String(row[22]).replace(/\\t/g, '').trim() : '',\r\n                      凭证号: row[23] ? String(row[23]).replace(/\\t/g, '').trim() : '',\r\n                      交易柜员号: row[24] ? String(row[24]).replace(/\\t/g, '').trim() : '',\r\n                      备注: row[25] ? String(row[25]).replace(/\\t/g, '').trim() : '',\r\n                      查询反馈结果原因: row[26] ? String(row[26]).replace(/\\t/g, '').trim() : ''\r\n                    }\r\n                  } else {\r\n                    transaction = {\r\n                      交易户名: row[0],\r\n                      交易卡号: row[1] ? String(row[1]).replace(/\\t/g, '').trim() : '',\r\n                      交易账号: row[2] ? String(row[2]).replace(/\\t/g, '').trim() : '',\r\n                      交易时间: row[3] ? String(row[3]).replace(/\\t/g, '').trim() : '',\r\n                      交易金额: row[4] ? String(row[4]).replace(/\\t/g, '').trim() : '',\r\n                      交易余额: row[5] ? String(row[5]).replace(/\\t/g, '').trim() : '',\r\n                      收付标志: row[6] ? String(row[6]).replace(/\\t/g, '').trim() : '',\r\n                      对手账号: row[7] ? String(row[7]).replace(/\\t/g, '').trim() : '',\r\n                      现金标志: row[8] ? String(row[8]).replace(/\\t/g, '').trim() : '',\r\n                      对手户名: row[9] ? String(row[9]).replace(/\\t/g, '').trim() : '',\r\n                      对手身份证号: row[10] ? String(row[10]).replace(/\\t/g, '').trim() : '',\r\n                      对手开户银行: row[11] ? String(row[11]).replace(/\\t/g, '').trim() : '',\r\n                      摘要说明: row[12] ? String(row[12]).replace(/\\t/g, '').trim() : '',\r\n                      交易币种: row[13] ? String(row[13]).replace(/\\t/g, '').trim() : '',\r\n                      交易网点名称: row[14] ? String(row[14]).replace(/\\t/g, '').trim() : '',\r\n                      交易发生地: row[15] ? String(row[15]).replace(/\\t/g, '').trim() : '',\r\n                      交易是否成功: row[16] ? String(row[16]).replace(/\\t/g, '').trim() : '',\r\n                      传票号: row[17] ? String(row[17]).replace(/\\t/g, '').trim() : '',\r\n                      IP地址: row[18] ? String(row[18]).replace(/\\t/g, '').trim() : '',\r\n                      MAC地址: row[19] ? String(row[19]).replace(/\\t/g, '').trim() : '',\r\n                      对手交易余额: row[20] ? String(row[20]).replace(/\\t/g, '').trim() : '',\r\n                      交易流水号: row[21] ? String(row[21]).replace(/\\t/g, '').trim() : '',\r\n                      日志号: row[22] ? String(row[22]).replace(/\\t/g, '').trim() : '',\r\n                      凭证种类: row[23] ? String(row[23]).replace(/\\t/g, '').trim() : '',\r\n                      凭证号: row[24] ? String(row[24]).replace(/\\t/g, '').trim() : '',\r\n                      交易柜员号: row[25] ? String(row[25]).replace(/\\t/g, '').trim() : '',\r\n                      备注: row[26] ? String(row[26]).replace(/\\t/g, '').trim() : '',\r\n                      查询反馈结果原因: row[27] ? String(row[27]).replace(/\\t/g, '').trim() : ''\r\n                    }\r\n                  }\r\n\r\n                  this.transactionData.push(transaction)\r\n                }\r\n              }\r\n              // console.log('交易数据:', this.transactionData);\r\n              this.fetchData()\r\n            } else {\r\n              this.$message.error(file.name + '的文件格式错误，请检查表头是否符合要求。')\r\n            }\r\n          }\r\n          reader.readAsArrayBuffer(file)\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '未上传任何文件',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    fetchData() {\r\n      const filenames = Array.from(this.fileList).map(file => file.name)\r\n      axios.post('http://127.0.0.1:8000/upload', { user: USER, filename: filenames, tableName: this.value, newMember: this.transactionData })\r\n        .then(response => {\r\n          const success_lines = response.data.success_lines\r\n          const fail_lines = response.data.fail_lines\r\n          const repeat_members = response.data.intersection\r\n          // 原来替代再往下一行的<pre style=\"text-align: left; background: #f5f7fa; padding: 15px; border-radius: 4px;\">\r\n          this.$alert(`\r\n              <pre style=\"white-space: pre-wrap; text-align: left; background: #f5f7fa; \r\n               padding: 15px; border-radius: 4px; max-height: 400px; overflow-y: auto;\">\r\n符合要求的文件上传成功！\r\n成功行数: ${success_lines}\r\n失败行数: ${fail_lines}\r\n重复对象: ${repeat_members}\r\n              </pre>\r\n            `, {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定',\r\n            center: true,\r\n            // closeOnClickModal: true,\r\n            lockScroll: true,\r\n            showClose: true,\r\n            customClass: 'code-alert'\r\n          })\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    },\r\n    validateHeaders(headers) { // 检查表头是否符合要求\r\n      // 去除表头中的制表符和多余的空格\r\n      headers = headers.map(header => header.replace(/\\t/g, '').trim())\r\n      const cleanHeaders = [...headers]\r\n      // 检查表头的第一个元素是否为 \"交易户名\"\r\n      if (cleanHeaders[0] !== '交易户名') {\r\n        cleanHeaders.unshift('交易户名') // 如果不是，手动添加 \"交易户名\" 到开头\r\n      }\r\n      // console.log(JSON.stringify(cleanHeaders))\r\n      // console.log(JSON.stringify(this.requiredHeaders))\r\n      return JSON.stringify(cleanHeaders) === JSON.stringify(this.requiredHeaders)\r\n    },\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList // 更新 fileList\r\n    },\r\n    handleRemove(file, fileList) {\r\n      this.fileList = fileList\r\n      console.log(file, fileList)\r\n    },\r\n    handlePreview(file) {\r\n      console.log(file)\r\n    },\r\n    handleSuccess(response, file) {\r\n      console.log('上传成功:', response)\r\n    },\r\n    handleError(err, file) {\r\n      console.error('上传失败，请重新上传符合条件的文件格式:', err)\r\n    },\r\n    DownloadModel() {\r\n      const link = document.createElement('a')\r\n      link.href = '模板.xlsx'\r\n      link.download = '模板.xlsx' // 指定下载的文件名\r\n      document.body.appendChild(link)\r\n      link.click()\r\n      document.body.removeChild(link)\r\n    },\r\n    handleSearch() {\r\n      // 发送交易数据到后端\r\n      axios.get('http://127.0.0.1:8000/all_tables')\r\n        .then(response => {\r\n          console.log(response.data)\r\n          const data = response.data.all_tables\r\n          this.options = data.map(item => ({\r\n            label: item, // 使用字符串作为 label\r\n            value: item // 使用相同的字符串作为 value\r\n          }))\r\n        })\r\n        .catch(error => {\r\n          this.$message.error('上传失败:', error)\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.upload-demo {\r\n  margin: 20px; /* 增加整体边距 */\r\n}\r\n\r\n.el-upload {\r\n  margin-bottom: 20px; /* 上传组件下方边距 */\r\n}\r\n\r\n.el-button {\r\n  margin-top: 10px; /* 上传按钮上方边距 */\r\n}\r\n\r\n.el-upload__tip {\r\n  margin-top: 10px; /* 提示文本上方边距 */\r\n}\r\n\r\n.upload-container {\r\n  margin-right: 40px; /* 添加右侧间距 */\r\n}\r\n</style>\n"]}]}