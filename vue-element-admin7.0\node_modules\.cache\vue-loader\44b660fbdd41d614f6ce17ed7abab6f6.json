{"remainingRequest": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\components\\SocialSignin.vue?vue&type=style&index=0&id=2b33be98&lang=scss&scoped=true", "dependencies": [{"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\src\\views\\login\\components\\SocialSignin.vue", "mtime": 1731738504000}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749148886058}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749148885228}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749148885313}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749148892495}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749148891500}, {"path": "D:\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749148885200}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zb2NpYWwtc2lnbnVwLWNvbnRhaW5lciB7CiAgbWFyZ2luOiAyMHB4IDA7CiAgLnNpZ24tYnRuIHsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIGN1cnNvcjogcG9pbnRlcjsKICB9CiAgLmljb24gewogICAgY29sb3I6ICNmZmY7CiAgICBmb250LXNpemU6IDI0cHg7CiAgICBtYXJnaW4tdG9wOiA4cHg7CiAgfQogIC53eC1zdmctY29udGFpbmVyLAogIC5xcS1zdmctY29udGFpbmVyIHsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIHdpZHRoOiA0MHB4OwogICAgaGVpZ2h0OiA0MHB4OwogICAgbGluZS1oZWlnaHQ6IDQwcHg7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBwYWRkaW5nLXRvcDogMXB4OwogICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIG1hcmdpbi1yaWdodDogNXB4OwogIH0KICAud3gtc3ZnLWNvbnRhaW5lciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjRkYTcwOwogIH0KICAucXEtc3ZnLWNvbnRhaW5lciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNkJBMkQ2OwogICAgbWFyZ2luLWxlZnQ6IDUwcHg7CiAgfQp9Cg=="}, {"version": 3, "sources": ["SocialSignin.vue"], "names": [], "mappings": ";AAyEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SocialSignin.vue", "sourceRoot": "src/views/login/components", "sourcesContent": ["<template>\n  <div class=\"social-signup-container\">\n    <div class=\"sign-btn\" @click=\"wechatHandleClick('wechat')\">\n      <span class=\"wx-svg-container\"><svg-icon icon-class=\"wechat\" class=\"icon\" /></span>\n      WeChat\n    </div>\n    <div class=\"sign-btn\" @click=\"tencentHandleClick('tencent')\">\n      <span class=\"qq-svg-container\"><svg-icon icon-class=\"qq\" class=\"icon\" /></span>\n      QQ\n    </div>\n  </div>\n</template>\n\n<script>\n// import openWindow from '@/utils/open-window'\n\nexport default {\n  name: 'SocialSignin',\n  methods: {\n    wechatHandleClick(thirdpart) {\n      alert('ok')\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\n      // const appid = 'xxxxx'\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\n      // const url = 'https://open.weixin.qq.com/connect/qrconnect?appid=' + appid + '&redirect_uri=' + redirect_uri + '&response_type=code&scope=snsapi_login#wechat_redirect'\n      // openWindow(url, thirdpart, 540, 540)\n    },\n    tencentHandleClick(thirdpart) {\n      alert('ok')\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\n      // const client_id = 'xxxxx'\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\n      // const url = 'https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=' + client_id + '&redirect_uri=' + redirect_uri\n      // openWindow(url, thirdpart, 540, 540)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .social-signup-container {\n    margin: 20px 0;\n    .sign-btn {\n      display: inline-block;\n      cursor: pointer;\n    }\n    .icon {\n      color: #fff;\n      font-size: 24px;\n      margin-top: 8px;\n    }\n    .wx-svg-container,\n    .qq-svg-container {\n      display: inline-block;\n      width: 40px;\n      height: 40px;\n      line-height: 40px;\n      text-align: center;\n      padding-top: 1px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n      margin-right: 5px;\n    }\n    .wx-svg-container {\n      background-color: #24da70;\n    }\n    .qq-svg-container {\n      background-color: #6BA2D6;\n      margin-left: 50px;\n    }\n  }\n</style>\n"]}]}